"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for KhaiBaoThoiPhanBoCCDC module.
"""

from django.urls import path

from django_ledger.api.views.cong_cu.dung_phan_bo_ccdc.khai_bao_thoi_phan_bo_ccdc import KhaiBaoThoiPhanBoCCDCViewSet

# URL patterns for the module
urlpatterns = [
    # List view
    path('', KhaiBaoThoiPhanBoCCDCViewSet.as_view({
        'get': 'list'
    }), name='khai-bao-thoi-phan-bo-ccdc-list'),

    # Detail view
    path('<uuid:pk>/', KhaiBaoThoiPhanBoCCDCViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update'
    }), name='khai-bao-thoi-phan-bo-ccdc-detail'),
]
