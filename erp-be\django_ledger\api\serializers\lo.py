"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for Lo (Lot) Model
"""

from rest_framework import serializers

from django_ledger.models import LoModel


class LoModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the LoModel
    """

    class Meta:
        model = LoModel
        fields = [
            'uuid',
            'entity_model',
            'ma_vt',
            'ma_lo',
            'ten_lo',
            'ten_lo2',
            'ma_vt2',
            'ngay_nhap',
            'ngay_sx',
            'ngay_hhsd',
            'ngay_hhbh',
            'ghi_chu',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'created',
            'updated'
        ]

        swagger_schema_fields = {
            'example': {
                'ma_vt': 'VT001',
                'ma_lo': 'LO001',
                'ten_lo': 'Lô hàng 001',
                'ten_lo2': 'Lot 001',
                'ma_vt2': 'VT001-A',
                'ngay_nhap': '2023-01-01',
                'ngay_sx': '2022-12-01',
                'ngay_hhsd': '2024-01-01',
                'ngay_hhbh': '2024-06-01',
                'ghi_chu': 'Ghi chú về lô hàng',
                'status': '1'
            }
        }
