"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ThongTinThanhToanHoaDonDichVu model.
"""

from rest_framework import serializers

from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_dich_vu import ThongTinThanhToanHoaDonDichVuModel


class ThongTinThanhToanHoaDonDichVuSerializer(serializers.ModelSerializer):
    """
    Serializer for ThongTinThanhToanHoaDonDichVuModel.
    """
    ma_httt_data = serializers.SerializerMethodField()
    tknh_data = serializers.SerializerMethodField()
    tk_data = serializers.SerializerMethodField()
    ma_ct_data = serializers.SerializerMethodField()
    ma_nk_data = serializers.SerializerMethodField()
    id_ct_tt_data = serializers.SerializerMethodField()

    class Meta:
        model = ThongTinThanhToanHoaDonDichVuModel
        fields = '__all__'
        read_only_fields = ['uuid', 'created', 'updated']

    def get_ma_httt_data(self, obj):
        if obj.ma_httt:
            return {
                'uuid': obj.ma_httt.uuid,
                'ma_httt': obj.ma_httt.ma_httt,
                'ten_httt': obj.ma_httt.ten_httt
            }
        return None

    def get_tknh_data(self, obj):
        if obj.tknh:
            return {
                'uuid': obj.tknh.uuid,
                'account_number': obj.tknh.account_number,
                'account_name': obj.tknh.account_name
            }
        return None

    def get_tk_data(self, obj):
        if obj.tk:
            return {
                'uuid': obj.tk.uuid,
                'code': obj.tk.code,
                'name': obj.tk.name
            }
        return None

    def get_ma_ct_data(self, obj):
        if obj.ma_ct:
            return {
                'uuid': obj.ma_ct.uuid,
                'ma_ct': obj.ma_ct.ma_ct,
                'ten_ct': obj.ma_ct.ten_ct
            }
        return None

    def get_ma_nk_data(self, obj):
        if obj.ma_nk:
            return {
                'uuid': obj.ma_nk.uuid,
                'ma_quyen': obj.ma_nk.ma_quyen,
                'ten_quyen': obj.ma_nk.ten_quyen
            }
        return None

    def get_id_ct_tt_data(self, obj):
        if obj.id_ct_tt:
            return {
                'uuid': obj.id_ct_tt.uuid,
                'ma_ct': obj.id_ct_tt.ma_ct,
                'ten_ct': obj.id_ct_tt.ten_ct
            }
        return None
