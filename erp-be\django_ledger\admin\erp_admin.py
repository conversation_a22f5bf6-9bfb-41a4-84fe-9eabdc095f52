from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from django_ledger.models import (
    KhoHangModel, NhanVienModel, NgoaiTeModel,
    HanThanhToanModel, PhuongThucThanhToanModel,
    LoaiVatTuModel, ThueSuatThueGTGTModel,
    VatTuSanPhamDonViTinhModel, VatTuSanPhamHangHoaModel,
    GiaMuaModel, RangBuocNhapModel,
    NhomPhiModel, PhiModel, UnitOfMeasureModel,
    VendorModel, CustomerModel, QuocGiaModel, TinhThanhModel, QuanHuyenModel, XaPhuongModel, KhuVucModel,
    BoPhanModel, BoPhanSuDungTSModel  # Import models
)



# Define VendorModelAdmin
class VendorModelAdmin(admin.ModelAdmin):
    list_display = ['vendor_name', 'vendor_number', 'active']
    search_fields = ['vendor_name', 'vendor_number']
    list_filter = ['active']

# Define CustomerModelAdmin
class CustomerModelAdmin(admin.ModelAdmin):
    list_display = ['customer_name', 'customer_number', 'active']
    search_fields = ['customer_name', 'customer_number']
    list_filter = ['active']

class KhoHangModelAdmin(admin.ModelAdmin):
    list_display = ['ma_kho', 'ten_kho', 'don_vi', 'trang_thai']
    search_fields = ['ma_kho', 'ten_kho', 'don_vi']
    list_filter = ['trang_thai']


class NhanVienModelAdmin(admin.ModelAdmin):
    list_display = ['ma_nhan_vien', 'ho_va_ten', 'chuc_vu', 'bo_phan', 'trang_thai']
    search_fields = ['ma_nhan_vien', 'ho_va_ten', 'chuc_vu', 'bo_phan']
    list_filter = ['trang_thai', 'ban_hang', 'mua_hang', 'cong_no_tam_ung']


class NgoaiTeModelAdmin(admin.ModelAdmin):
    list_display = ['ngoai_te', 'ten_ngoai_te', 'trang_thai']
    search_fields = ['ngoai_te', 'ten_ngoai_te']
    list_filter = ['trang_thai']


class HanThanhToanModelAdmin(admin.ModelAdmin):
    list_display = ['ma_tt', 'ten_tt', 'han_tt', 'status', 'created_by', 'updated_by']
    search_fields = ['ma_tt', 'ten_tt', 'ten_tt2']
    list_filter = ['status']
    readonly_fields = ['uuid', 'created', 'updated', 'created_by', 'updated_by']


class PhuongThucThanhToanModelAdmin(admin.ModelAdmin):
    list_display = ['ma_pttt', 'ten_pttt', 'ten_pttt2', 'status']
    search_fields = ['ma_pttt', 'ten_pttt', 'ten_pttt2']
    list_filter = ['status']


class LoaiVatTuModelAdmin(admin.ModelAdmin):
    list_display = ['ma_loai_vat_tu', 'ten_loai_vat_tu', 'trang_thai']
    search_fields = ['ma_loai_vat_tu', 'ten_loai_vat_tu']
    list_filter = ['trang_thai']


class ThueSuatThueGTGTModelAdmin(admin.ModelAdmin):
    list_display = ['ma_thue', 'ten_thue', 'thue_suat', 'trang_thai']
    search_fields = ['ma_thue', 'ten_thue']
    list_filter = ['trang_thai']


class UnitOfMeasureModelAdmin(admin.ModelAdmin):
    list_display = ['name', 'unit_abbr', 'is_active']
    search_fields = ['name', 'unit_abbr']
    list_filter = ['is_active']


class VatTuSanPhamDonViTinhModelAdmin(admin.ModelAdmin):
    list_display = ['vat_tu_san_pham_id', 'don_vi_tinh_id', 'ten_dvt', 'he_so']
    search_fields = ['ten_dvt']


class VatTuSanPhamHangHoaModelAdmin(admin.ModelAdmin):
    list_display = ['vat_tu_san_pham_id', 'ma_hang_hoa']
    search_fields = ['ma_hang_hoa']


class GiaMuaModelAdmin(admin.ModelAdmin):
    list_display = ['ma_vat_tu', 'don_vi_tinh', 'nha_cung_cap', 'so_luong_tu', 'gia_mua', 'trang_thai']
    search_fields = ['ma_vat_tu__name', 'nha_cung_cap__vendor_name']
    list_filter = ['trang_thai']


class RangBuocNhapModelAdmin(admin.ModelAdmin):
    list_display = ['tai_khoan_id', 'ten_doi_tuong', 'bat_buoc_nhap']
    search_fields = ['tai_khoan_id__code', 'ten_doi_tuong']
    list_filter = ['bat_buoc_nhap']





class NhomPhiModelAdmin(admin.ModelAdmin):
    list_display = ['ma_nhom', 'ten_phan_nhom', 'loai_nhom', 'trang_thai']
    search_fields = ['ma_nhom', 'ten_phan_nhom']
    list_filter = ['loai_nhom', 'trang_thai']


class PhiModelAdmin(admin.ModelAdmin):
    list_display = ['ma_phi', 'ten_phi', 'nhom_phi_1', 'bo_phan', 'trang_thai']
    search_fields = ['ma_phi', 'ten_phi', 'bo_phan']
    list_filter = ['trang_thai']


class QuocGiaModelAdmin(admin.ModelAdmin):
    list_display = ['ma_qg', 'ten_qg', 'ten_qg2', 'status']
    search_fields = ['ma_qg', 'ten_qg', 'ten_qg2']
    list_filter = ['status']


class TinhThanhModelAdmin(admin.ModelAdmin):
    list_display = ['ma_tinh', 'ten_tinh', 'ten_tinh2', 'status']
    search_fields = ['ma_tinh', 'ten_tinh', 'ten_tinh2']
    list_filter = ['status']


class QuanHuyenModelAdmin(admin.ModelAdmin):
    list_display = ['ma_quan', 'ten_quan', 'ma_tinh', 'status']
    search_fields = ['ma_quan', 'ten_quan', 'ma_tinh']
    list_filter = ['status', 'ma_tinh']
    readonly_fields = ['uuid', 'created', 'updated']


class XaPhuongModelAdmin(admin.ModelAdmin):
    list_display = ['ma_xp', 'ten_xp', 'ma_tinh', 'ma_quan', 'status']
    search_fields = ['ma_xp', 'ten_xp']
    list_filter = ['status', 'ma_tinh', 'ma_quan']
    readonly_fields = ['uuid', 'created', 'updated']


class KhuVucModelAdmin(admin.ModelAdmin):
    list_display = ['rg_code', 'rgname', 'parent_rg', 'stt', 'status']
    search_fields = ['rg_code', 'rgname', 'rgname2']
    list_filter = ['status']
    readonly_fields = ['uuid', 'created', 'updated']


class BoPhanModelAdmin(admin.ModelAdmin):
    list_display = ['ma_bp', 'ten_bp', 'ten_bp2', 'status', 'created_by', 'updated_by']
    search_fields = ['ma_bp', 'ten_bp', 'ten_bp2', 'ghi_chu']
    list_filter = ['status']
    readonly_fields = ['uuid', 'created', 'updated', 'created_by', 'updated_by']


class BoPhanSuDungTSModelAdmin(admin.ModelAdmin):
    list_display = ['ma_bp', 'ten_bp', 'ten_bp2', 'ma_bp_phi', 'status', 'created', 'updated']
    search_fields = ['ma_bp', 'ten_bp', 'ten_bp2', 'ghi_chu']
    list_filter = ['status']
    readonly_fields = ['uuid', 'created', 'updated']
    raw_id_fields = ['entity_model', 'ma_bp_phi']
