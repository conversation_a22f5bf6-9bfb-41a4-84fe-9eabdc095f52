"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for HoaDonDieuChinhGiaHangBan (Price Adjustment Invoice) model.
"""

from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dieu_chinh_gia_hang_ban import (
    HoaDonDieuChinhGiaHangBanModel,
)
from rest_framework import serializers


class HoaDonDieuChinhGiaHangBanModelSerializer(serializers.ModelSerializer):
    """
    Serializer for HoaDonDieuChinhGiaHangBan model.
    """

    ma_kh_data = serializers.SerializerMethodField()
    tk_data = serializers.SerializerMethodField()
    ma_tt_data = serializers.SerializerMethodField()
    unit_id_data = serializers.SerializerMethodField()
    ma_nk_data = serializers.SerializerMethodField()
    ma_nt_data = serializers.SerializerMethodField()
    ma_kh9_data = serializers.SerializerMethodField()
    so_ct_data = serializers.SerializerMethodField()
    so_ct2_data = serializers.SerializerMethodField()
    chi_tiet_count = serializers.SerializerMethodField()

    class Meta:
        model = HoaDonDieuChinhGiaHangBanModel
        fields = "__all__"
        read_only_fields = ["uuid", "created", "updated"]

    def get_ma_kh_data(self, obj):
        """
        Get ma_kh data.
        """
        if obj.ma_kh:
            return {
                "uuid": obj.ma_kh.uuid,
                "customer_name": obj.ma_kh.customer_name,
                "customer_number": obj.ma_kh.customer_number,
            }
        return None

    def get_tk_data(self, obj):
        """
        Get tk data.
        """
        if obj.tk:
            return {"uuid": obj.tk.uuid, "code": obj.tk.code, "name": obj.tk.name}
        return None

    def get_ma_tt_data(self, obj):
        """
        Get ma_tt data.
        """
        if obj.ma_tt:
            return {
                "uuid": obj.ma_tt.uuid,
                "ma_tt": obj.ma_tt.ma_tt,
                "ten_tt": obj.ma_tt.ten_tt,
            }
        return None

    def get_unit_id_data(self, obj):
        """
        Get unit_id data.
        """
        if obj.unit_id:
            return {
                "uuid": obj.unit_id.uuid,
                "name": obj.unit_id.name,
                "slug": obj.unit_id.slug,
            }
        return None

    def get_ma_nk_data(self, obj):
        """
        Get ma_nk data.
        """
        if obj.ma_nk:
            return {
                "uuid": obj.ma_nk.uuid,
                "ma_quyen": obj.ma_nk.ma_quyen,
                "ten_quyen": obj.ma_nk.ten_quyen,
            }
        return None

    def get_ma_nt_data(self, obj):
        """
        Get ma_nt data.
        """
        if obj.ma_nt:
            return {
                "uuid": obj.ma_nt.uuid,
                "ma_nt": obj.ma_nt.ma_nt,
                "ten_nt": obj.ma_nt.ten_nt,
            }
        return None

    def get_ma_kh9_data(self, obj):
        """
        Get ma_kh9 data.
        """
        if obj.ma_kh9:
            return {
                "uuid": obj.ma_kh9.uuid,
                "customer_name": obj.ma_kh9.customer_name,
                "customer_number": obj.ma_kh9.customer_number,
            }
        return None

    def get_so_ct_data(self, obj):
        """
        Get so_ct data.
        """
        if obj.so_ct:
            return {
                "uuid": obj.so_ct.uuid,
                "so_ct": obj.so_ct.so_ct,
                "ten_ct": getattr(obj.so_ct, "ten_ct", ""),
            }
        return None

    def get_so_ct2_data(self, obj):
        """
        Get so_ct2 data.
        """
        if obj.so_ct2:
            return {
                "uuid": obj.so_ct2.uuid,
                "so_ct": obj.so_ct2.so_ct,
                "ten_ct": getattr(obj.so_ct2, "ten_ct", ""),
            }
        return None

    def get_chi_tiet_count(self, obj):
        """
        Get chi_tiet count.
        """
        return obj.chi_tiet.count()


class HoaDonDieuChinhGiaHangBanModelCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating HoaDonDieuChinhGiaHangBan model.
    """

    chi_tiet = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = HoaDonDieuChinhGiaHangBanModel
        fields = "__all__"
        read_only_fields = ["uuid", "created", "updated"]
