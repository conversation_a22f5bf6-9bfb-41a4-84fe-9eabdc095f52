"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the serializers for the HoaDonDieuChinhThongTinModel.
"""

from rest_framework import serializers

from django_ledger.models.ban_hang.hoa_don_dien_tu.dieu_chinh_thong_tin_hoa_don_da_xac_thuc import HoaDonDieuChinhThongTinModel
from django_ledger.api.serializers.entity import EntityModelSerializer


class HoaDonDieuChinhThongTinModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the HoaDonDieuChinhThongTinModel.
    Used for read operations.
    """
    entity_model = EntityModelSerializer(read_only=True)

    class Meta:
        model = HoaDonDieuChinhThongTinModel
        fields = [
            'uuid',
            'entity_model',
            'id_goc',
            'ngay_ct',
            'unit_id',
            'ma_kh',
            'ma_nt',
            'ten_kh_thue',
            'ong_ba',
            'ma_so_thue',
            'dia_chi',
            'dien_thoai',
            'e_mail',
            'tknh',
            'ten_nh',
            'ma_pttt',
            'noi_dung',
            'van_ban_thoa_thuan',
            'ngay_thoa_thuan',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'entity_model', 'created', 'updated']

    def to_representation(self, instance):
        """
        Transform the serialized data.

        Parameters
        ----------
        instance : HoaDonDieuChinhThongTinModel
            The instance to serialize.

        Returns
        -------
        dict
            The serialized data.
        """
        data = super().to_representation(instance)

        # Thêm thông tin chi tiết cho các trường tham chiếu

        # Thêm thông tin chi tiết cho unit_id nếu có
        if hasattr(instance, 'unit_id') and instance.unit_id:
            try:
                from django_ledger.models.unit import EntityUnitModel
                unit = EntityUnitModel.objects.filter(unit_id=instance.unit_id).first()
                if unit:
                    data['unit_id_data'] = {
                        'unit_id': unit.unit_id,
                        'ten_unit': unit.ten_unit if hasattr(unit, 'ten_unit') else None,
                        'ma_unit': unit.ma_unit if hasattr(unit, 'ma_unit') else None
                    }
            except Exception:
                pass

        # Thêm thông tin chi tiết cho ma_pttt nếu có
        if hasattr(instance, 'ma_pttt') and instance.ma_pttt:
            try:
                pttt = instance.ma_pttt
                data['ma_pttt_data'] = {
                    'ma_pttt': pttt.ma_pttt if hasattr(pttt, 'ma_pttt') else None,
                    'ten_pttt': pttt.ten_pttt if hasattr(pttt, 'ten_pttt') else None
                }
            except Exception:
                pass

        return data


class HoaDonDieuChinhThongTinModelCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for the HoaDonDieuChinhThongTinModel.
    Used for create and update operations.
    """
    class Meta:
        model = HoaDonDieuChinhThongTinModel
        fields = [
            'id_goc',
            'ngay_ct',
            'unit_id',
            'ma_kh',
            'ma_nt',
            'ten_kh_thue',
            'ong_ba',
            'ma_so_thue',
            'dia_chi',
            'dien_thoai',
            'e_mail',
            'tknh',
            'ten_nh',
            'ma_pttt',
            'noi_dung',
            'van_ban_thoa_thuan',
            'ngay_thoa_thuan',
            'status'
        ]

    # Loại bỏ phương thức create và update vì chúng sẽ được xử lý trực tiếp trong view
