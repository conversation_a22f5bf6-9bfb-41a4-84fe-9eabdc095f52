"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Bao Cao Tinh Trang De <PERSON> (Payment Request Status Report) API.
"""

from rest_framework import serializers
from django.core.validators import MinValueValidator, MaxValueValidator


class BaoCaoTinhTrangDeNghiChiRequestSerializer(serializers.Serializer):
    """
    Serializer for validating payment request status report POST body data.
    Validates all POST body parameters from cURL request.
    """

    # Date range filters (from oMemvars)
    ngay_ct1 = serializers.DateTimeField(
        required=False,
        allow_null=True,
        help_text="<PERSON><PERSON><PERSON> chứng từ từ (FIND|@DFDFrom|a.ngay_ct >= %s)"
    )

    ngay_ct2 = serializers.DateTimeField(
        required=False,
        allow_null=True,
        help_text="<PERSON><PERSON>y chứng từ đến (FIND|@DFDTo|a.ngay_ct <= %s)"
    )

    # Document number range filters (from oMemvars)
    so_ct1 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Số chứng từ từ (FIND||a.so_ct >= N'%s')"
    )

    so_ct2 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Số chứng từ đến (FIND||a.so_ct <= N'%s')"
    )

    # Department filter (from oMemvars)
    ma_bp = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Mã bộ phận (FIND||=)"
    )

    # Status filter (from oMemvars)
    status = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Trạng thái đề nghị (FIND||=)"
    )

    # Description filter (from oMemvars)
    dien_giai = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Diễn giải (FIND||a.dien_giai like N'%%s%')"
    )

    # Standard report parameters (from oMemvars)
    ma_unit = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Mã đơn vị"
    )

    mau_bc = serializers.IntegerField(
        required=False,
        default=20,
        help_text="Mẫu báo cáo"
    )



    def validate_status(self, value):
        """
        Validate status value.
        """
        if value and value not in ['0', '1', '2', '3', '4']:
            raise serializers.ValidationError("Trạng thái phải là 0, 1, 2, 3, hoặc 4")
        return value


class BaoCaoTinhTrangDeNghiChiResponseSerializer(serializers.Serializer):
    """
    Serializer for formatting payment request status report response data.
    Matches the exact fields from cURL response.
    """

    id = serializers.CharField(
        help_text="ID của bản ghi"
    )

    line = serializers.IntegerField(
        help_text="Số dòng"
    )

    unit_id = serializers.CharField(
        help_text="ID đơn vị"
    )

    ngay_ct = serializers.CharField(
        help_text="Ngày chứng từ"
    )

    so_ct = serializers.CharField(
        help_text="Số chứng từ"
    )

    ma_ct = serializers.CharField(
        help_text="Mã chứng từ"
    )

    ma_bp = serializers.CharField(
        help_text="Mã bộ phận"
    )

    user_id = serializers.CharField(
        help_text="ID người dùng"
    )

    dien_giai = serializers.CharField(
        help_text="Diễn giải"
    )

    tien = serializers.DecimalField(
        max_digits=15,
        decimal_places=3,
        help_text="Số tiền"
    )

    tien_chi = serializers.DecimalField(
        max_digits=15,
        decimal_places=3,
        help_text="Tiền chi"
    )

    status = serializers.CharField(
        help_text="Trạng thái"
    )

    tien_cl = serializers.DecimalField(
        max_digits=15,
        decimal_places=3,
        help_text="Tiền còn lại"
    )

    ten_bp = serializers.CharField(
        help_text="Tên bộ phận"
    )

    ten_ttct = serializers.CharField(
        help_text="Tên trạng thái chứng từ"
    )

    ma_unit = serializers.CharField(
        help_text="Mã đơn vị"
    )

    nguoi_yc = serializers.CharField(
        help_text="Người yêu cầu"
    )
