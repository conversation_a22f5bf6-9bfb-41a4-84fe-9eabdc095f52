"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Router for KheUoc (Loan/Contract) API endpoints.
"""

from django.urls import path
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.finance import (
    KheUocViewSet,
    KheUocThongTinLaiSuatViewSet,
    KheUocChiTietThanhToanViewSet
)

router = DefaultRouter()
router.register('loans', KheUocViewSet, basename='loans')
router.register('loan-interest-rates', KheUocThongTinLaiSuatViewSet, basename='loan-interest-rates')
router.register('loan-payments', KheUocChiTietThanhToanViewSet, basename='loan-payments')

urlpatterns = router.urls
