"""
Serializer for DiaChi (Address) model.
"""

from rest_framework import serializers

from django_ledger.models import DiaChiModel
from django_ledger.api.serializers.base import GlobalModelSerializer

class Dia<PERSON>hiSerializer(GlobalModelSerializer):
    """
    Serializer for DiaChi (Address) model.

    This serializer handles the conversion between DiaChi model instances
    and their JSON representations, including validation and field customization.
    """

    class Meta:
        model = DiaChiModel
        fields = [
            'uuid',
            'entity_model',
            'ma_kh',
            'ma_dc',
            'ten_dc',
            'ten_dc2',
            'lien_he_gh',
            'dt_gh',
            'fax_gh',
            'ghi_chu',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated', 'entity_model']

    def validate_ma_kh(self, value):
        """
        Validate the ma_kh field.

        Parameters
        ----------
        value : str
            The value to validate.

        Returns
        -------
        str
            The validated value.

        Raises
        ------
        serializers.ValidationError
            If validation fails.
        """
        if not value:
            raise serializers.ValidationError("ma_kh is required.")
        if len(value) > 100:
            raise serializers.ValidationError(
                "ma_kh must not exceed 100 characters."
            )
        return value

    def validate_ma_dc(self, value):
        """
        Validate the ma_dc field.

        Parameters
        ----------
        value : str
            The value to validate.

        Returns
        -------
        str
            The validated value.

        Raises
        ------
        serializers.ValidationError
            If validation fails.
        """
        if not value:
            raise serializers.ValidationError("ma_dc is required.")
        if len(value) > 100:
            raise serializers.ValidationError(
                "ma_dc must not exceed 100 characters."
            )
        return value

    def validate_ten_dc(self, value):
        """
        Validate the ten_dc field.

        Parameters
        ----------
        value : str
            The value to validate.

        Returns
        -------
        str
            The validated value.

        Raises
        ------
        serializers.ValidationError
            If validation fails.
        """
        if not value:
            raise serializers.ValidationError("ten_dc is required.")
        if len(value) > 255:
            raise serializers.ValidationError(
                "ten_dc must not exceed 255 characters."
            )
        return value

    def validate_ten_dc2(self, value):
        """
        Validate the ten_dc2 field.

        Parameters
        ----------
        value : str
            The value to validate.

        Returns
        -------
        str
            The validated value.

        Raises
        ------
        serializers.ValidationError
            If validation fails.
        """
        if value and len(value) > 255:
            raise serializers.ValidationError(
                "ten_dc2 must not exceed 255 characters."
            )
        return value

    def validate_lien_he_gh(self, value):
        """
        Validate the lien_he_gh field.

        Parameters
        ----------
        value : str
            The value to validate.

        Returns
        -------
        str
            The validated value.

        Raises
        ------
        serializers.ValidationError
            If validation fails.
        """
        if value and len(value) > 255:
            raise serializers.ValidationError(
                "lien_he_gh must not exceed 255 characters."
            )
        return value

    def validate_dt_gh(self, value):
        """
        Validate the dt_gh field.

        Parameters
        ----------
        value : str
            The value to validate.

        Returns
        -------
        str
            The validated value.

        Raises
        ------
        serializers.ValidationError
            If validation fails.
        """
        if value and len(value) > 50:
            raise serializers.ValidationError(
                "dt_gh must not exceed 50 characters."
            )
        return value

    def validate_fax_gh(self, value):
        """
        Validate the fax_gh field.

        Parameters
        ----------
        value : str
            The value to validate.

        Returns
        -------
        str
            The validated value.

        Raises
        ------
        serializers.ValidationError
            If validation fails.
        """
        if value and len(value) > 50:
            raise serializers.ValidationError(
                "fax_gh must not exceed 50 characters."
            )
        return value

    def validate_status(self, value):
        """
        Validate the status field.

        Parameters
        ----------
        value : str
            The value to validate.

        Returns
        -------
        str
            The validated value.

        Raises
        ------
        serializers.ValidationError
            If validation fails.
        """
        if value not in ['0', '1']:
            raise serializers.ValidationError(
                "status must be either '0' or '1'."
            )
        if len(value) > 2:
            raise serializers.ValidationError(
                "status must not exceed 2 characters."
            )
        return value

    def validate(self, data):
        """
        Validate the entire data dictionary.

        This method performs any validation that requires access to multiple fields.

        Parameters
        ----------
        data : dict
            The data dictionary to validate.

        Returns
        -------
        dict
            The validated data.

        Raises
        ------
        serializers.ValidationError
            If validation fails.
        """
        # When creating a new instance, check required fields
        if not self.instance:  # Create operation
            required_fields = ['ma_kh', 'ma_dc', 'ten_dc']
            for field in required_fields:
                if field not in data:
                    raise serializers.ValidationError({
                        field: f"{field} is required."
                    })

        return data
