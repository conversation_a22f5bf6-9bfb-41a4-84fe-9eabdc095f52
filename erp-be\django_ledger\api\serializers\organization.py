"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Organization-related models
"""

from rest_framework import serializers

from django_ledger.models import (
    BoPhanModel
)


class BoPhanModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the BoPhanModel (Department) model.

    This serializer handles the conversion between BoPhanModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - ma_bp: Department code
    - ten_bp: Primary name of the department
    - ten_bp2: Secondary/alternative name (optional)
    - ghi_chu: Notes or description for the department (optional)
    - status: Status indicator ('0'=inactive, '1'=active)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    - created_by: Username of creator
    - updated_by: Username of last updater
    """

    class Meta:
        model = BoPhanModel
        fields = ['uuid', 'ma_bp', 'ten_bp', 'ten_bp2', 'ghi_chu', 'status', 'entity_model',
                  'created', 'updated', 'created_by', 'updated_by']
        read_only_fields = ['uuid', 'created', 'updated']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_bp": "KT",
                "ten_bp": "Kế toán",
                "ten_bp2": "Accounting",
                "ghi_chu": "Bộ phận kế toán của công ty",
                "status": "1",
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z",
                "created_by": "admin",
                "updated_by": "admin"
            }
        }
