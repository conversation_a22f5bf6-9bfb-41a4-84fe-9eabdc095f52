"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for GiayBaoCo API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.tien_gui.hach_toan.giay_bao_co.giay_bao_co import GiayBaoCoViewSet
from django_ledger.api.views.tien_gui.hach_toan.giay_bao_co.chi_tiet_giay_bao_co import ChiTietGiayBaoCoViewSet
from django_ledger.api.views.tien_gui.hach_toan.giay_bao_co.phieu_ngan_hang_giay_bao_co import PhieuNganHangGiayBaoCoViewSet

# Main router for GiayBaoCo
router = DefaultRouter()
router.register('', GiayBaoCoViewSet, basename='giay-bao-co')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),

    # Nested routes for giay_bao_co
    path('<uuid:giay_bao_co_uuid>/', include([
        # ChiTietGiayBaoCo routes
        path('chi-tiet/', ChiTietGiayBaoCoViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-tiet-giay-bao-co-list'),

        path('chi-tiet/<uuid:uuid>/', ChiTietGiayBaoCoViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-tiet-giay-bao-co-detail'),

        # PhieuNganHangGiayBaoCo routes
        path('phieu-ngan-hang/', PhieuNganHangGiayBaoCoViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='phieu-ngan-hang-giay-bao-co-list'),

        path('phieu-ngan-hang/<uuid:uuid>/', PhieuNganHangGiayBaoCoViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='phieu-ngan-hang-giay-bao-co-detail'),
    ])),
]
