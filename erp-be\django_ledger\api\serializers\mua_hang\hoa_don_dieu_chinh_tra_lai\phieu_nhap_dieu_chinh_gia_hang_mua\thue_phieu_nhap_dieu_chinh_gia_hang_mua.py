"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ThuePhieuNhapDieuChinhGiaHangMua model.
"""
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from django_ledger.models import ThuePhieuNhapDieuChinhGiaHangMuaModel
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.accounts import AccountModelSerializer
from django_ledger.api.serializers.tax import TaxModelSerializer
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.tien_do_thanh_toan import DotThanhToanModelSerializer
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import KheUocModelSerializer
from django_ledger.api.serializers.danh_muc import PhiSerializer, ChiPhiKhongHopLeSerializer
from django_ledger.api.serializers.chung_tu import ChungTuSerializer
from django_ledger.api.serializers.han_thanh_toan import HanThanhToanModelSerializer


class ThuePhieuNhapDieuChinhGiaHangMuaSerializer(serializers.ModelSerializer):
    """
    Serializer for ThuePhieuNhapDieuChinhGiaHangMua model.
    """
    # Read-only fields for related objects
    phieu_nhap_data = serializers.SerializerMethodField(read_only=True)
    ma_thue_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_no_data = serializers.SerializerMethodField(read_only=True)
    tk_du_data = serializers.SerializerMethodField(read_only=True)
    ma_kh9_data = serializers.SerializerMethodField(read_only=True)
    ma_tt_data = serializers.SerializerMethodField(read_only=True)
    so_ct0_data = serializers.SerializerMethodField(read_only=True)
    so_ct2_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_dtt_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_cp0_data = serializers.SerializerMethodField(read_only=True)


    class Meta:
        model = ThuePhieuNhapDieuChinhGiaHangMuaModel
        fields = [
            'uuid',
            'phieu_nhap',
            'phieu_nhap_data',
            'ma_thue',
            'ma_thue_data',
            'ma_kh',
            'ma_kh_data',
            'tk_thue_no',
            'tk_thue_no_data',
            'tk_du',
            'tk_du_data',
            'ma_tt',
            'ma_tt_data',
            'so_ct0',
            'so_ct0_data',
            'so_ct2',
            'so_ct2_data',
            'ma_kh9',
            'ma_kh9_data',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_sp_data',
            'ma_cp0',
            'ma_cp0_data',
            'entity_model',
            'line',
            'id_tt',
            'ma_mau_ct',
            'ma_mau_bc',
            'ma_tc_thue',
            'ten_kh_thue',
            'dia_chi',
            'ma_so_thue',
            'ten_vt_thue',
            'ten_tk_thue_no',
            'ten_tk_du',
            'ten_kh9',
            'ten_tt',
            'ghi_chu',
            'ngay_ct0',
            'thue_suat',
            't_tien_nt',
            't_tien',
            't_thue_nt',
            't_thue',
            'ma_lsx',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'phieu_nhap_data',
            'ma_thue_data',
            'ma_kh_data',
            'tk_thue_no_data',
            'tk_du_data',
            'ma_kh9_data',
            'ma_tt_data',
            'so_ct0_data',
            'so_ct2_data',
            'ma_bp_data',
            'ma_vv_data',
            'ma_hd_data',
            'ma_dtt_data',
            'ma_ku_data',
            'ma_phi_data',
            'ma_sp_data',
            'ma_cp0_data',
            'created',
            'updated'
        ]

    def get_phieu_nhap_data(self, obj):
        """
        Get parent phieu nhap data (minimal to avoid circular reference).
        """
        if obj.phieu_nhap:
            return {
                'uuid': str(obj.phieu_nhap.uuid),
                'i_so_ct': obj.phieu_nhap.i_so_ct,
                'ngay_ct': obj.phieu_nhap.ngay_ct,
                'dien_giai': obj.phieu_nhap.dien_giai
            }
        return None

    def get_ma_thue_data(self, obj):
        """
        Get tax data.
        """
        if obj.ma_thue:
            return TaxModelSerializer(obj.ma_thue).data
        return None

    def get_ma_kh_data(self, obj):
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_tk_thue_no_data(self, obj):
        """
        Get tax debit account data.
        """
        if obj.tk_thue_no:
            return AccountModelSerializer(obj.tk_thue_no).data
        return None

    def get_tk_du_data(self, obj):
        """
        Get balance account data.
        """
        if obj.tk_du:
            return AccountModelSerializer(obj.tk_du).data
        return None

    def get_ma_kh9_data(self, obj):
        """
        Get customer 9 data.
        """
        if obj.ma_kh9:
            return CustomerModelSerializer(obj.ma_kh9).data
        return None

    def get_ma_tt_data(self, obj):
        """
        Get payment term data.
        """
        if obj.ma_tt:
            return HanThanhToanModelSerializer(obj.ma_tt).data
        return None

    def get_so_ct0_data(self, obj):
        """
        Get original document number data.
        """
        if obj.so_ct0:
            return ChungTuSerializer(obj.so_ct0).data
        return None

    def get_so_ct2_data(self, obj):
        """
        Get secondary document number data.
        """
        if obj.so_ct2:
            return ChungTuSerializer(obj.so_ct2).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):
        """
        Get case data.
        """
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_hd_data(self, obj):
        """
        Get contract data.
        """
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_dtt_data(self, obj):
        """
        Get payment batch data.
        """
        if obj.ma_dtt:
            return DotThanhToanModelSerializer(obj.ma_dtt).data
        return None

    def get_ma_ku_data(self, obj):
        """
        Get loan agreement data.
        """
        if obj.ma_ku:
            return KheUocModelSerializer(obj.ma_ku).data
        return None

    def get_ma_phi_data(self, obj):
        """
        Get fee data.
        """
        if obj.ma_phi:
            return PhiSerializer(obj.ma_phi).data
        return None

    def get_ma_sp_data(self, obj):
        """
        Get product data.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_cp0_data(self, obj):
        """
        Get invalid expense data.
        """
        if obj.ma_cp0:
            return ChiPhiKhongHopLeSerializer(obj.ma_cp0).data
        return None


    def validate(self, attrs):
        """
        Validate the serializer data.
        """
        # Check if this is an update or create operation
        is_update = self.instance is not None

        # Validate required fields for create operations
        if not is_update:
            required_fields = ['ma_thue', 'ma_kh', 'tk_thue_no', 'tk_du', 'line', 'ten_kh_thue', 'dia_chi', 'ten_tk_thue_no', 'ten_tk_du', 'ngay_ct0', 'thue_suat']
            for field in required_fields:
                if field not in attrs:
                    raise serializers.ValidationError({
                        field: _('This field is required.')
                    })

        # Validate line number is positive
        if 'line' in attrs and attrs['line'] <= 0:
            raise serializers.ValidationError({
                'line': _('Line number must be positive.')
            })

        # Validate decimal fields are positive
        decimal_fields = ['thue_suat', 't_tien_nt', 't_tien', 't_thue_nt', 't_thue']
        for field in decimal_fields:
            if field in attrs and attrs[field] is not None and attrs[field] < 0:
                raise serializers.ValidationError({
                    field: _('This field must be positive.')
                })

        # Validate tax rate is between 0 and 100
        if 'thue_suat' in attrs and attrs['thue_suat'] is not None:
            if attrs['thue_suat'] < 0 or attrs['thue_suat'] > 100:
                raise serializers.ValidationError({
                    'thue_suat': _('Tax rate must be between 0 and 100.')
                })

        # Validate id_tt is non-negative
        if 'id_tt' in attrs and attrs['id_tt'] is not None and attrs['id_tt'] < 0:
            raise serializers.ValidationError({
                'id_tt': _('Status ID must be non-negative.')
            })

        return attrs


    def validate_ma_so_thue(self, value):
        """
        Validate tax identification number format.
        """
        if value and len(value) > 0:
            # Basic validation for tax ID format (can be customized based on requirements)
            if not value.replace('-', '').isdigit():
                raise serializers.ValidationError(_('Tax ID must contain only digits and hyphens.'))

        return value

    def validate_ten_kh_thue(self, value):
        """
        Validate customer tax name is not empty.
        """
        if not value or not value.strip():
            raise serializers.ValidationError(_('Customer tax name cannot be empty.'))

        return value.strip()

    def validate_dia_chi(self, value):
        """
        Validate address is not empty.
        """
        if not value or not value.strip():
            raise serializers.ValidationError(_('Address cannot be empty.'))

        return value.strip()

    def validate_ten_tk_thue_no(self, value):
        """
        Validate tax debit account name is not empty.
        """
        if not value or not value.strip():
            raise serializers.ValidationError(_('Tax debit account name cannot be empty.'))

        return value.strip()

    def validate_ten_tk_du(self, value):
        """
        Validate balance account name is not empty.
        """
        if not value or not value.strip():
            raise serializers.ValidationError(_('Balance account name cannot be empty.'))

        return value.strip()
