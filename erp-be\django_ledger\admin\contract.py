"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Admin configuration for Contract model.
"""

from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from django_ledger.models import ContractModel, TienDoThanhToanModel


class TienDoThanhToanInline(admin.TabularInline):
    """
    Inline admin for TienDoThanhToanModel (Payment Progress).
    """
    model = TienDoThanhToanModel
    extra = 1
    fields = ['line', 'ma_dot', 'ten_dot', 'ngay_tt', 'ty_le', 'tien_nt', 'tien', 'han_tt', 'status']


class ContractModelAdmin(admin.ModelAdmin):
    """
    Admin class for the ContractModel (Contract) model.
    """
    list_display = [
        'ma_hd', 
        'ten_hd', 
        'so_hd', 
        'ngay_hd', 
        'loai_hd_display', 
        'ma_bp', 
        'ma_kh', 
        'status_display',
        'created', 
        'updated'
    ]
    list_filter = ['loai_hd', 'status', 'entity_model']
    search_fields = ['ma_hd', 'ten_hd', 'ten_hd2', 'so_hd']
    readonly_fields = ['uuid', 'created', 'updated']
    raw_id_fields = ['entity_model', 'nh_hd1', 'nh_hd2', 'nh_hd3', 'ma_bp', 'ma_kh']
    inlines = [TienDoThanhToanInline]
    
    fieldsets = [
        (
            _('Basic Information'), {
                'fields': [
                    'uuid',
                    'entity_model',
                    'ma_hd',
                    'ten_hd',
                    'ten_hd2',
                    'so_hd',
                    'ngay_hd',
                    'ngay_hd1',
                    'ngay_hd2',
                    'loai_hd',
                ]
            }
        ),
        (
            _('Financial Information'), {
                'fields': [
                    'tien_nt',
                    'tien',
                ]
            }
        ),
        (
            _('Relationships'), {
                'fields': [
                    'nh_hd1',
                    'nh_hd2',
                    'nh_hd3',
                    'ma_bp',
                    'ma_kh',
                ]
            }
        ),
        (
            _('Status'), {
                'fields': [
                    'status',
                ]
            }
        ),
        (
            _('Metadata'), {
                'fields': [
                    'created',
                    'updated',
                ],
                'classes': ['collapse']
            }
        ),
    ]
    
    def loai_hd_display(self, obj):
        """
        Display the contract type label instead of the value.
        """
        return obj.get_loai_hd_display()
    loai_hd_display.short_description = _('Contract Type')
    
    def status_display(self, obj):
        """
        Display the status label instead of the value.
        """
        return obj.get_status_display()
    status_display.short_description = _('Status')
