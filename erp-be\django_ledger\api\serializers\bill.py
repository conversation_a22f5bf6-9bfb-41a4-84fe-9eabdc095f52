"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Bill Serializers Module
"""
from rest_framework import serializers
from django.core.exceptions import ValidationError

from django_ledger.models import BillModel, ItemTransactionModel
from django_ledger.services.provider import service_provider
from django_ledger.api.serializers.invoice import ItemTransactionSerializer

class BillModelSerializer(serializers.ModelSerializer):
    """Serializer for bills."""
    
    items = ItemTransactionSerializer(many=True, required=False)
    amount_due = serializers.DecimalField(
        max_digits=20,
        decimal_places=2,
        read_only=True
    )
    
    class Meta:
        model = BillModel
        fields = [
            'uuid',
            'vendor',
            'terms',
            'cash_account',
            'prepaid_account',
            'unearned_account',
            'date_draft',
            'bill_number',
            'bill_status',
            'date_paid',
            'amount_due',
            'markdown_notes',
            'items'
        ]
        read_only_fields = [
            'uuid',
            'bill_number',
            'date_paid',
            'amount_due'
        ]

    def create(self, validated_data):
        """Create bill using service."""
        items_data = validated_data.pop('items', [])
        entity_slug = self.context['entity_slug']
        
        # Get service factory
        factory = service_provider.get_factory(entity_slug)
        
        # Create bill
        bill = factory.bill.create_bill(
            vendor=validated_data['vendor'],
            terms=validated_data.get('terms', 'net_30'),
            cash_account=validated_data.get('cash_account'),
            prepaid_account=validated_data.get('prepaid_account'),
            unearned_account=validated_data.get('unearned_account'),
            date_draft=validated_data.get('date_draft'),
            additional_info={
                'markdown_notes': validated_data.get('markdown_notes')
            }
        )
        
        # Add items if provided
        if items_data:
            factory.bill.update_bill_items(
                bill=bill,
                items_data=items_data
            )
            
        return bill
        
    def update(self, instance, validated_data):
        """Update bill."""
        if instance.bill_status != 'draft':
            raise ValidationError(
                'Can only update draft bills'
            )
            
        items_data = validated_data.pop('items', None)
        entity_slug = self.context['entity_slug']
        
        # Get service factory
        factory = service_provider.get_factory(entity_slug)
        
        # Update items if provided
        if items_data is not None:
            factory.bill.update_bill_items(
                bill=instance,
                items_data=items_data
            )
            
        # Update other fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        return instance

class BillModelListSerializer(BillModelSerializer):
    """Serializer for bill list view."""
    
    vendor_name = serializers.CharField(source='vendor.vendor_name', read_only=True)
    
    class Meta(BillModelSerializer.Meta):
        fields = [
            'uuid',
            'bill_number',
            'vendor',
            'vendor_name',
            'bill_status',
            'amount_due',
            'date_paid',
            'date_draft'
        ]
