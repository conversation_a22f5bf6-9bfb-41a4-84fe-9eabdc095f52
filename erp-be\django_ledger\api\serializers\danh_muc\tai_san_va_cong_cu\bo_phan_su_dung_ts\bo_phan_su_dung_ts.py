"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for BoPhanSuDungTS model.
"""
from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models.danh_muc import BoPhanSuDungTSModel
from django_ledger.api.serializers.organization import BoPhanModelSerializer


class BoPhanSuDungTSSerializer(serializers.ModelSerializer):
    """
    Serializer for BoPhanSuDungTS model.
    """
    ma_bp_phi_data = serializers.SerializerMethodField()

    class Meta:
        model = BoPhanSuDungTSModel
        fields = [
            'uuid',
            'entity_model',
            'ma_bp',
            'ten_bp',
            'ten_bp2',
            'ma_bp_phi',
            'ma_bp_phi_data',
            'ghi_chu',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'ma_bp_phi_data',
            'created',
            'updated'
        ]

    def get_ma_bp_phi_data(self, obj):
        """
        Get fee department data.
        """
        if obj.ma_bp_phi:
            return BoPhanModelSerializer(obj.ma_bp_phi).data
        return None
