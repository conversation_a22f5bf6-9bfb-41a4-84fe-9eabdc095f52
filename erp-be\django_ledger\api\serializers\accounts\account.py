"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Account serializer implementation.
"""

from django_ledger.api.serializers.accounts.account_constraint import (
    AccountConstraintSerializer,
)
from django_ledger.models import AccountModel
from rest_framework import serializers


class ParentAccountModelSerializer(serializers.ModelSerializer):
    """
    Serializer for parent account data to avoid circular references
    """

    tk_so_cai = serializers.SerializerMethodField(read_only=True)
    tk_chi_tiet = serializers.SerializerMethodField(read_only=True)
    bac_tk = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = AccountModel
        fields = [
            "uuid",
            "code",
            "name",
            "role",
            "balance_type",
            "active",
            "account_name_2",
            "account_name_3",
            "is_parent_account",
            "depth",
            "tk_so_cai",
            "tk_chi_tiet",
            "bac_tk",
        ]

    def get_tk_so_cai(self, obj):
        """
        Returns True if the account is a root parent account (tk_so_cai)
        Root parent accounts are accounts that have no parent but have children

        This method handles both model instances and dictionaries (during serialization)
        """
        # Handle the case when obj is a dictionary (OrderedDict)
        if isinstance(obj, dict) or hasattr(obj, "items"):
            # If tk_so_cai is in the dictionary
            if "tk_so_cai" in obj:
                return obj["tk_so_cai"]
            # Default to False if tk_so_cai is not available
            return False

        # Handle the case when obj is a model instance
        if hasattr(obj, "parent_account_code") and hasattr(obj, "get_children_count"):
            # Check if the account has no parent (is a root account) and has children
            return obj.parent_account_code is None and obj.get_children_count() > 0

        # Default to False if we can't determine
        return False

    def get_tk_chi_tiet(self, obj):
        """
        Returns True if the account is a leaf account (tk_chi_tiet)
        Leaf accounts are accounts that have no children

        This method handles both model instances and dictionaries (during serialization)
        """
        # Handle the case when obj is a dictionary (OrderedDict)
        if isinstance(obj, dict) or hasattr(obj, "items"):
            # If tk_chi_tiet is in the dictionary
            if "tk_chi_tiet" in obj:
                return obj["tk_chi_tiet"]
            # Default to True if tk_chi_tiet is not available
            return True

        # Handle the case when obj is a model instance
        if hasattr(obj, "get_children_count"):
            # An account is a leaf account if it has no children
            return obj.get_children_count() == 0

        # Default to True if we can't determine
        return True

    def get_bac_tk(self, obj):
        """
        Returns the account level (bac_tk) based on account code length:
        - Root accounts (8 digits like ********, ********): bac_tk = 0
        - 3 digits (like 111): bac_tk = 1
        - 4 digits (like 1111): bac_tk = 2
        - 5 digits (like 11111): bac_tk = 3

        This method handles both model instances and dictionaries (during serialization)
        """
        code = None
        role = None

        # Handle the case when obj is a dictionary (OrderedDict)
        if isinstance(obj, dict) or hasattr(obj, "items"):
            code = obj.get("code")
            role = obj.get("role")
        # Handle the case when obj is a model instance
        elif hasattr(obj, "code"):
            code = obj.code
            role = getattr(obj, "role", None)

        # Check if this is a root account by role
        if role and role.startswith("root_"):
            return 0

        # Calculate bac_tk based on code length
        if code:
            code_length = len(str(code))
            if code_length == 8:  # Root accounts like ********, ********
                return 0
            elif code_length == 3:  # Like 111
                return 1
            elif code_length == 4:  # Like 1111
                return 2
            elif code_length == 5:  # Like 11111
                return 3
            elif code_length == 6:  # Like 111111
                return 4
            else:
                # For other lengths, calculate as: length - 2
                return max(0, code_length - 2)

        # Default to 1 if we can't determine
        return 1


class AccountModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the AccountModel
    """

    constraints = AccountConstraintSerializer(many=True, read_only=True)
    parent_account_code_data = serializers.SerializerMethodField(read_only=True)
    account_depth = serializers.SerializerMethodField(read_only=True)
    bac_tk = serializers.SerializerMethodField(read_only=True)
    tk_so_cai = serializers.SerializerMethodField(read_only=True)
    tk_chi_tiet = serializers.SerializerMethodField(read_only=True)
    account_type = serializers.SerializerMethodField(read_only=True)
    account_prefix = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = AccountModel
        fields = [
            "uuid",
            "code",
            "name",
            "role",
            "balance_type",
            "active",
            "account_name_2",
            "account_name_3",
            "parent_account_code",
            "parent_account_code_data",
            "account_depth",
            "account_group_1",
            "short_name_1",
            "short_name_2",
            "is_parent_account",
            "currency_code",
            "ar_ap_tracking_type",
            "account_type_code",
            "fx_reval_debit_type",
            "fx_reval_credit_type",
            "notes",
            "constraints",
            "tk_so_cai",
            "tk_chi_tiet",
            "bac_tk",
            "account_type",
            "account_prefix",
        ]

    def get_parent_account_code_data(self, obj):
        """
        Returns the parent account data if available

        This method handles both model instances and dictionaries (during serialization)
        """
        # Handle the case when obj is a dictionary (OrderedDict)
        if isinstance(obj, dict) or hasattr(obj, "items"):
            # If parent_account_code is in the dictionary and is not None
            if "parent_account_code" in obj and obj["parent_account_code"]:
                # If parent_account_code is already a model instance
                if hasattr(obj["parent_account_code"], "uuid"):
                    return ParentAccountModelSerializer(obj["parent_account_code"]).data
                # If parent_account_code is a UUID string, we need to fetch the model
                elif isinstance(obj["parent_account_code"], str):
                    try:
                        from django_ledger.models import AccountModel

                        parent = AccountModel.objects.get(
                            uuid=obj["parent_account_code"]
                        )
                        return ParentAccountModelSerializer(parent).data
                    except Exception:
                        return None
            # Check if 'parent' is in the dictionary (alternative field name)
            elif "parent" in obj and obj["parent"]:
                # If parent is already a model instance
                if hasattr(obj["parent"], "uuid"):
                    return ParentAccountModelSerializer(obj["parent"]).data
                # If parent is a UUID string, we need to fetch the model
                elif isinstance(obj["parent"], str):
                    try:
                        from django_ledger.models import AccountModel

                        parent = AccountModel.objects.get(uuid=obj["parent"])
                        return ParentAccountModelSerializer(parent).data
                    except Exception:
                        return None
            return None

        # Handle the case when obj is a model instance
        if hasattr(obj, "parent_account_code") and obj.parent_account_code:
            return ParentAccountModelSerializer(obj.parent_account_code).data
        return None

    def get_account_depth(self, obj):
        """
        Returns the account depth in the hierarchy
        1 for root accounts, 2 for their children, and so on

        This method handles both model instances and dictionaries (during serialization)
        """
        # Handle the case when obj is a dictionary (OrderedDict)
        if isinstance(obj, dict) or hasattr(obj, "items"):
            # If depth is in the dictionary
            if "depth" in obj:
                return obj["depth"]
            # Default to 1 if depth is not available
            return 1

        # Handle the case when obj is a model instance
        if hasattr(obj, "depth"):
            return obj.depth

        # Default to 1 if depth is not available
        return 1

    def get_bac_tk(self, obj):
        """
        Returns the account level (bac_tk) based on account code length:
        - Root accounts (8 digits like ********, ********): bac_tk = 0
        - 3 digits (like 111): bac_tk = 1
        - 4 digits (like 1111): bac_tk = 2
        - 5 digits (like 11111): bac_tk = 3

        This method handles both model instances and dictionaries (during serialization)
        """
        code = None
        role = None

        # Handle the case when obj is a dictionary (OrderedDict)
        if isinstance(obj, dict) or hasattr(obj, "items"):
            code = obj.get("code")
            role = obj.get("role")
        # Handle the case when obj is a model instance
        elif hasattr(obj, "code"):
            code = obj.code
            role = getattr(obj, "role", None)

        # Check if this is a root account by role
        if role and role.startswith("root_"):
            return 0

        # Calculate bac_tk based on code length
        if code:
            code_length = len(str(code))
            if code_length == 8:  # Root accounts like ********, ********
                return 0
            elif code_length == 3:  # Like 111
                return 1
            elif code_length == 4:  # Like 1111
                return 2
            elif code_length == 5:  # Like 11111
                return 3
            elif code_length == 6:  # Like 111111
                return 4
            else:
                # For other lengths, calculate as: length - 2
                return max(0, code_length - 2)

        # Default to 1 if we can't determine
        return 1

    def get_tk_so_cai(self, obj):
        """
        Returns True if the account is a root parent account (tk_so_cai)
        Root parent accounts are accounts that have no parent but have children

        This method handles both model instances and dictionaries (during serialization)
        """
        # Handle the case when obj is a dictionary (OrderedDict)
        if isinstance(obj, dict) or hasattr(obj, "items"):
            # If tk_so_cai is in the dictionary
            if "tk_so_cai" in obj:
                return obj["tk_so_cai"]
            # Default to False if tk_so_cai is not available
            return False

        # Handle the case when obj is a model instance
        if hasattr(obj, "parent_account_code") and hasattr(obj, "get_children_count"):
            # Check if the account has no parent (is a root account) and has children
            return obj.parent_account_code is None and obj.get_children_count() > 0

        # Default to False if we can't determine
        return False

    def get_tk_chi_tiet(self, obj):
        """
        Returns True if the account is a leaf account (tk_chi_tiet)
        Leaf accounts are accounts that have no children

        This method handles both model instances and dictionaries (during serialization)
        """
        # Handle the case when obj is a dictionary (OrderedDict)
        if isinstance(obj, dict) or hasattr(obj, "items"):
            # If tk_chi_tiet is in the dictionary
            if "tk_chi_tiet" in obj:
                return obj["tk_chi_tiet"]
            # Default to True if tk_chi_tiet is not available
            return True

        # Handle the case when obj is a model instance
        if hasattr(obj, "get_children_count"):
            # An account is a leaf account if it has no children
            return obj.get_children_count() == 0

        # Default to True if we can't determine
        return True

    def get_account_prefix(self, obj):
        """
        Returns the account prefix (first digit of the code)

        This method handles both model instances and dictionaries (during serialization)
        """
        # Handle the case when obj is a dictionary (OrderedDict)
        if isinstance(obj, dict) or hasattr(obj, "items"):
            # If code is in the dictionary
            if "code" in obj and obj["code"]:
                return obj["code"][0]
            # Default to None if code is not available
            return None

        # Handle the case when obj is a model instance
        if hasattr(obj, "code") and obj.code:
            return obj.code[0]

        # Default to None if code is not available
        return None

    def get_account_type(self, obj):
        """
        Returns the account type based on the account prefix

        This method handles both model instances and dictionaries (during serialization)
        """
        # Get the prefix
        prefix = None

        # Handle the case when obj is a dictionary (OrderedDict)
        if isinstance(obj, dict) or hasattr(obj, "items"):
            # If code is in the dictionary
            if "code" in obj and obj["code"]:
                prefix = obj["code"][0]
        # Handle the case when obj is a model instance
        elif hasattr(obj, "code") and obj.code:
            prefix = obj.code[0]

        # If no prefix, return None
        if not prefix:
            return None

        # Determine the account type based on the prefix
        if prefix == "1":
            return "current_assets"  # Tài sản ngắn hạn
        elif prefix == "2":
            return "fixed_assets"  # Tài sản dài hạn
        elif prefix == "3":
            return "liabilities"  # Nợ phải trả
        elif prefix == "4":
            return "equity"  # Vốn chủ sở hữu
        elif prefix == "5":
            return "revenue"  # Doanh thu
        elif prefix == "6":
            return "cogs"  # Giá vốn hàng bán
        elif prefix == "7":
            return "expenses"  # Chi phí
        elif prefix == "8":
            return "expenses"  # Chi phí khác
        elif prefix == "9":
            return "profit_loss"  # Xác định kết quả kinh doanh

        return None
