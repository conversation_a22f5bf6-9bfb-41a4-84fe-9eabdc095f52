"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

KhaiBaoThongTinCCDC (Tool Information Declaration) serializer package initialization.
"""

from django_ledger.api.serializers.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc.khai_bao_thong_tin_ccdc import KhaiBaoThongTinCCDCModelSerializer
from django_ledger.api.serializers.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc.chi_tiet_doi_tuong_hach_toan_ccdc import ChiTietDoiTuongHachToanCCDCModelSerializer
from django_ledger.api.serializers.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc.chi_tiet_phu_tung_kem_theo_ccdc import ChiTietPhuTungKemTheoCCDCModelSerializer

__all__ = [
    'KhaiBaoThongTinCCDCModelSerializer',
    'ChiTietDoiTuongHachToanCCDCModelSerializer',
    'ChiTietPhuTungKemTheoCCDCModelSerializer',
]
