"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

KhaiBaoThongTinCCDC (Tool Information Declaration) router implementation.
"""

from django.urls import path

from django_ledger.api.views.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc import (
    KhaiBaoThongTinCCDCViewSet,
    ChiTietDoiTuongHachToanCCDCViewSet,
    ChiTietPhuTungKemTheoCCDCViewSet
)

urlpatterns = [
    # KhaiBaoThongTinCCDC routes
    path('', KhaiBaoThongTinCCDCViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='khai-bao-thong-tin-ccdc-list'),

    path('<uuid:pk>/', KhaiBaoThongTinCCDCViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='khai-bao-thong-tin-ccdc-detail'),

    # Nested routes for ChiTietDoiTuongHachToanCCDC
    path('<uuid:khai_bao_thong_tin_ccdc_uuid>/chi-tiet-doi-tuong-hach-toan/', ChiTietDoiTuongHachToanCCDCViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='chi-tiet-doi-tuong-hach-toan-ccdc-list'),

    path('<uuid:khai_bao_thong_tin_ccdc_uuid>/chi-tiet-doi-tuong-hach-toan/<uuid:uuid>/', ChiTietDoiTuongHachToanCCDCViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='chi-tiet-doi-tuong-hach-toan-ccdc-detail'),

    # Nested routes for ChiTietPhuTungKemTheoCCDC
    path('<uuid:khai_bao_thong_tin_ccdc_uuid>/chi-tiet-phu-tung-kem-theo/', ChiTietPhuTungKemTheoCCDCViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='chi-tiet-phu-tung-kem-theo-ccdc-list'),

    path('<uuid:khai_bao_thong_tin_ccdc_uuid>/chi-tiet-phu-tung-kem-theo/<uuid:uuid>/', ChiTietPhuTungKemTheoCCDCViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='chi-tiet-phu-tung-kem-theo-ccdc-detail'),
]
