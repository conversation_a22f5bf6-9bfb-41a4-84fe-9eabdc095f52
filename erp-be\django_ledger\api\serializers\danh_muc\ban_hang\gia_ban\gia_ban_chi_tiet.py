"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for GiaBanChiTiet model.
"""
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from django_ledger.models.danh_muc.ban_hang.gia_ban.gia_ban_chi_tiet import GiaBanChiTietModel
from django_ledger.api.serializers.base import GlobalModelSerializer
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.danh_muc.ke_toan import NgoaiTeSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.loai_gia_ban import LoaiGiaBanSerializer
from django_ledger.api.serializers.group import GroupModelSimpleSerializer


class GiaBanChiTietSerializer(GlobalModelSerializer):
    """
    Serializer class for GiaBanChiTietModel that handles data serialization/deserialization.
    Inherits from GlobalModelSerializer for common fields and behaviors.
    """
    # Read-only fields for related objects
    vat_tu_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    nh_kh1_data = serializers.SerializerMethodField(read_only=True)
    nh_kh2_data = serializers.SerializerMethodField(read_only=True)
    nh_kh3_data = serializers.SerializerMethodField(read_only=True)
    ma_loai_gb_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = GiaBanChiTietModel
        fields = [
            'uuid',
            'gia_ban_vat_tu',
            'ma_vt',
            'vat_tu_data',
            'ma_nt',
            'ma_nt_data',
            'dvt',
            'dvt_data',
            'ngay_hl',
            'ma_kh',
            'ma_kh_data',
            'nh_kh1',
            'nh_kh1_data',
            'nh_kh2',
            'nh_kh2_data',
            'nh_kh3',
            'nh_kh3_data',
            'ma_loai_gb',
            'ma_loai_gb_data',
            'gia_nt2',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'vat_tu_data',
            'ma_nt_data',
            'dvt_data',
            'ma_kh_data',
            'nh_kh1_data',
            'nh_kh2_data',
            'nh_kh3_data',
            'ma_loai_gb_data',
            'created',
            'updated'
        ]

    def get_vat_tu_data(self, obj):
        """
        Get basic information about the material using VatTuSerializer
        """
        if obj.ma_vt:
            # Import VatTuSerializer lazily to avoid circular imports
            from django_ledger.api.serializers.vat_tu import VatTuSerializer
            return VatTuSerializer(obj.ma_vt).data
        return None

    def get_dvt_data(self, obj):
        """
        Get basic information about the unit of measure using DonViTinhSerializer
        """
        if obj.dvt:
            return DonViTinhSerializer(obj.dvt).data
        return None

    def get_ma_nt_data(self, obj):
        """
        Get basic information about the currency using NgoaiTeModelSerializer
        """
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_ma_kh_data(self, obj):
        """
        Get basic information about the customer using CustomerModelSerializer
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_ma_loai_gb_data(self, obj):
        """
        Get basic information about the price type using LoaiGiaBanSerializer
        """
        if obj.ma_loai_gb:
            return LoaiGiaBanSerializer(obj.ma_loai_gb).data
        return None

    def get_nh_kh1_data(self, obj):
        """
        Get basic information about customer group 1 using GroupModelSimpleSerializer
        """
        if obj.nh_kh1:
            return GroupModelSimpleSerializer(obj.nh_kh1).data
        return None

    def get_nh_kh2_data(self, obj):
        """
        Get basic information about customer group 2 using GroupModelSimpleSerializer
        """
        if obj.nh_kh2:
            return GroupModelSimpleSerializer(obj.nh_kh2).data
        return None

    def get_nh_kh3_data(self, obj):
        """
        Get basic information about customer group 3 using GroupModelSimpleSerializer
        """
        if obj.nh_kh3:
            return GroupModelSimpleSerializer(obj.nh_kh3).data
        return None

    def validate(self, attrs):
        """
        Custom validation for GiaBanChiTiet data.

        Parameters
        ----------
        attrs: dict
            Dictionary of field values to validate

        Returns
        -------
        dict
            Validated data

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        attrs = super().validate(attrs)

        # Required fields validation
        required_fields = ['gia_ban_vat_tu', 'gia_nt2']
        for field in required_fields:
            if field not in attrs or attrs.get(field) is None:
                raise serializers.ValidationError({
                    field: _('This field is required.')
                })

        # Validate price is positive
        if 'gia_nt2' in attrs:
            try:
                # Convert to float for comparison if it's a string
                gia_nt2_value = float(attrs['gia_nt2']) if isinstance(attrs['gia_nt2'], str) else attrs['gia_nt2']
                if gia_nt2_value < 0:
                    raise serializers.ValidationError({
                        'gia_nt2': _('Price cannot be negative.')
                    })
            except (ValueError, TypeError):
                raise serializers.ValidationError({
                    'gia_nt2': _('Invalid price format.')
                })

        # Validate status
        if 'status' in attrs:
            # Convert status to string if it's an integer
            if isinstance(attrs['status'], int):
                attrs['status'] = str(attrs['status'])

            # Validate status value
            if attrs['status'] not in ['0', '1']:
                raise serializers.ValidationError({
                    'status': _('Invalid status value. Must be 0 or 1.')
                })

        return attrs


