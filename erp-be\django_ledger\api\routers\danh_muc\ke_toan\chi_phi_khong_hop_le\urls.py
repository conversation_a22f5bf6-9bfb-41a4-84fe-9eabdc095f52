"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the URL patterns for the ChiPhiKhongHopLe API.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.danh_muc.ke_toan.chi_phi_khong_hop_le import ChiPhiKhongHopLeViewSet

# Main router for ChiPhiKhongHopLe
router = DefaultRouter()
router.register('', ChiPhiKhongHopLeViewSet, basename='chi-phi-khong-hop-le')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
