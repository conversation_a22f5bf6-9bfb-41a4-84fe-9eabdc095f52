"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/danh-muc/mua-hang/gia-mua/page",{

/***/ "(app-pages-browser)/./src/hooks/queries/useGiaMua.ts":
/*!****************************************!*\
  !*** ./src/hooks/queries/useGiaMua.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGiaMua: function() { return /* binding */ useGiaMua; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants */ \"(app-pages-browser)/./src/constants/index.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\n\n\n/**\r\n * Hook for managing GiaMua (Purchase Price) data\r\n *\r\n * This hook provides functions to fetch, create, update, and delete purchase prices.\r\n */ const useGiaMua = function() {\n    let initialGiaMuas = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];\n    const [giaMuas, setGiaMuas] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialGiaMuas);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const { entity } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const fetchGiaMuas = async ()=>{\n        if (!(entity === null || entity === void 0 ? void 0 : entity.slug)) return;\n        setIsLoading(true);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/entities/\".concat(entity.slug, \"/erp/\").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.QUERY_KEYS.GIA_MUA, \"/\"));\n            setGiaMuas(response.data.results);\n        } catch (error) {\n            console.error(\"Error fetching purchase prices:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const addGiaMua = async (newGiaMua)=>{\n        if (!(entity === null || entity === void 0 ? void 0 : entity.slug)) throw new Error(\"Entity slug is required\");\n        setIsLoading(true);\n        try {\n            let payload;\n            // Check if input is GiaMuaFormData and validate/convert it\n            if (isGiaMuaFormData(newGiaMua)) {\n                validateGiaMuaFormData(newGiaMua);\n                payload = convertFormDataToInput(newGiaMua);\n            } else {\n                payload = newGiaMua;\n            }\n            // Thêm header Content-Type để đảm bảo server hiểu đúng định dạng dữ liệu\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/entities/\".concat(entity.slug, \"/erp/\").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.QUERY_KEYS.GIA_MUA, \"/\"), payload, {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const addedGiaMua = response.data;\n            setGiaMuas((prev)=>[\n                    ...prev,\n                    addedGiaMua\n                ]);\n            return addedGiaMua;\n        } catch (error) {\n            console.error(\"Error adding purchase price:\", error);\n            if (error.response) {\n                var _error_response_data, _error_response_data1;\n                // Tạo thông báo lỗi chi tiết\n                let errorMessage = ((_error_response_data = error.response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || \"\";\n                // Handle validation errors which come as an object with field names as keys\n                if (typeof error.response.data === \"object\" && !((_error_response_data1 = error.response.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.detail)) {\n                    const validationErrors = [];\n                    for(const field in error.response.data){\n                        if (Array.isArray(error.response.data[field])) {\n                            validationErrors.push(\"\".concat(field, \": \").concat(error.response.data[field].join(\", \")));\n                        }\n                    }\n                    if (validationErrors.length > 0) {\n                        errorMessage = validationErrors.join(\"\\n\");\n                    } else {\n                        errorMessage = JSON.stringify(error.response.data);\n                    }\n                }\n                if (!errorMessage) {\n                    errorMessage = error.message || \"Unknown error\";\n                }\n                throw new Error(errorMessage);\n            }\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const updateGiaMua = async (uuid, updatedGiaMua)=>{\n        if (!(entity === null || entity === void 0 ? void 0 : entity.slug)) throw new Error(\"Entity slug is required\");\n        setIsLoading(true);\n        try {\n            let payload;\n            // Check if input is GiaMuaFormData and validate/convert it\n            if (isGiaMuaFormData(updatedGiaMua)) {\n                validateGiaMuaFormData(updatedGiaMua);\n                payload = convertFormDataToInput(updatedGiaMua);\n            } else {\n                payload = updatedGiaMua;\n            }\n            // Thêm header Content-Type để đảm bảo server hiểu đúng định dạng dữ liệu\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].put(\"/entities/\".concat(entity.slug, \"/erp/\").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.QUERY_KEYS.GIA_MUA, \"/\").concat(uuid, \"/\"), payload, {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const updatedGiaMuaData = response.data;\n            // Cập nhật state giaMuas với dữ liệu mới\n            setGiaMuas((prev)=>prev.map((giaMua)=>giaMua.uuid === updatedGiaMuaData.uuid ? updatedGiaMuaData : giaMua));\n            return updatedGiaMuaData;\n        } catch (error) {\n            console.error(\"Error updating purchase price:\", error);\n            if (error.response) {\n                var _error_response_data, _error_response_data1;\n                // Tạo thông báo lỗi chi tiết\n                let errorMessage = ((_error_response_data = error.response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || \"\";\n                // Handle validation errors which come as an object with field names as keys\n                if (typeof error.response.data === \"object\" && !((_error_response_data1 = error.response.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.detail)) {\n                    const validationErrors = [];\n                    for(const field in error.response.data){\n                        if (Array.isArray(error.response.data[field])) {\n                            validationErrors.push(\"\".concat(field, \": \").concat(error.response.data[field].join(\", \")));\n                        }\n                    }\n                    if (validationErrors.length > 0) {\n                        errorMessage = validationErrors.join(\"\\n\");\n                    } else {\n                        errorMessage = JSON.stringify(error.response.data);\n                    }\n                }\n                if (!errorMessage) {\n                    errorMessage = error.message || \"Unknown error\";\n                }\n                throw new Error(errorMessage);\n            }\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const deleteGiaMua = async (uuid)=>{\n        if (!(entity === null || entity === void 0 ? void 0 : entity.slug)) throw new Error(\"Entity slug is required\");\n        setIsLoading(true);\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].delete(\"/entities/\".concat(entity.slug, \"/erp/purchase-prices/\").concat(uuid, \"/\"));\n            setGiaMuas((prev)=>prev.filter((giaMua)=>giaMua.uuid !== uuid));\n        } catch (error) {\n            console.error(\"Error deleting purchase price:\", error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const refreshGiaMuas = async ()=>{\n        await fetchGiaMuas();\n    };\n    // Helper function to check if data is GiaMuaFormData\n    const isGiaMuaFormData = (data)=>{\n        return \"formData\" in data && \"vatTu\" in data && \"donViTinh\" in data && \"nhaCungCap\" in data && \"ngoaiTe\" in data;\n    };\n    // Helper function to validate GiaMuaFormData\n    const validateGiaMuaFormData = (data)=>{\n        var _data_vatTu, _data_donViTinh, _data_ngoaiTe;\n        // 1. Mã vật tư - bắt buộc\n        if (!((_data_vatTu = data.vatTu) === null || _data_vatTu === void 0 ? void 0 : _data_vatTu.uuid)) {\n            throw new Error(\"M\\xe3 vật tư kh\\xf4ng được bỏ trống\");\n        }\n        // 2. Đơn vị tính - bắt buộc (có thể có giá trị mặc định)\n        if (!((_data_donViTinh = data.donViTinh) === null || _data_donViTinh === void 0 ? void 0 : _data_donViTinh.uuid)) {\n            throw new Error(\"Đơn vị t\\xednh kh\\xf4ng được bỏ trống\");\n        }\n        // 3. Ngày hiệu lực - bắt buộc\n        if (!data.formData.ngay_hieu_luc || data.formData.ngay_hieu_luc.trim() === \"\") {\n            throw new Error(\"Ng\\xe0y hiệu lực kh\\xf4ng được bỏ trống\");\n        }\n        // 4. Nhà cung cấp - không bắt buộc (đã được thay đổi theo yêu cầu)\n        // Removed validation for nhaCungCap as it's now optional\n        // 5. Ngoại tệ - bắt buộc (có thể có giá trị mặc định)\n        if (!((_data_ngoaiTe = data.ngoaiTe) === null || _data_ngoaiTe === void 0 ? void 0 : _data_ngoaiTe.uuid)) {\n            throw new Error(\"Ngoại tệ kh\\xf4ng được bỏ trống\");\n        }\n    // 6. Số lượng từ - không bắt buộc (đã được thay đổi theo yêu cầu)\n    // Removed validation for so_luong_tu as it's now optional\n    // 7. Giá mua - không bắt buộc (không có validation, đã optional từ trước)\n    };\n    // Helper function to convert GiaMuaFormData to GiaMuaInput\n    const convertFormDataToInput = (data)=>{\n        var _data_vatTu, _data_donViTinh, _data_nhaCungCap, _data_ngoaiTe;\n        return {\n            ma_vat_tu: ((_data_vatTu = data.vatTu) === null || _data_vatTu === void 0 ? void 0 : _data_vatTu.uuid) || \"\",\n            don_vi_tinh: ((_data_donViTinh = data.donViTinh) === null || _data_donViTinh === void 0 ? void 0 : _data_donViTinh.uuid) || \"\",\n            ngay_hieu_luc: data.formData.ngay_hieu_luc || null,\n            nha_cung_cap: ((_data_nhaCungCap = data.nhaCungCap) === null || _data_nhaCungCap === void 0 ? void 0 : _data_nhaCungCap.uuid) || null,\n            ngoai_te: ((_data_ngoaiTe = data.ngoaiTe) === null || _data_ngoaiTe === void 0 ? void 0 : _data_ngoaiTe.uuid) || \"\",\n            so_luong_tu: Number(data.formData.so_luong_tu) || 0,\n            gia_mua: Number(data.formData.gia_mua) || 0,\n            trang_thai: Number(data.formData.trang_thai) || 1\n        };\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        fetchGiaMuas();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        entity === null || entity === void 0 ? void 0 : entity.slug\n    ]);\n    return {\n        giaMuas,\n        isLoading,\n        addGiaMua,\n        updateGiaMua,\n        deleteGiaMua,\n        refreshGiaMuas\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/queries/useGiaMua.ts\n"));

/***/ })

});