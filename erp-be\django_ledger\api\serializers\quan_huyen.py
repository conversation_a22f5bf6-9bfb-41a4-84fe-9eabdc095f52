from rest_framework import serializers
from django_ledger.models.quan_huyen import QuanHuyenModel
from django_ledger.models.tinh_thanh import TinhThanhModel
from django_ledger.api.serializers.tinh_thanh import TinhThanhModelSerializer


class QuanHuyenModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the QuanHuyenModel (District) model.

    This serializer handles the conversion between QuanHuyenModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - ma_quan: District code
    - ten_quan: Primary name of the district
    - ten_quan2: Secondary/alternative name (optional)
    - ma_tinh: Reference to the province/city (ForeignKey to TinhThanhModel)
    - ma_tinh_data: Nested TinhThanhModel object (read-only)
    - tinh_thanh: Nested TinhThanhModel object (read-only) - kept for backward compatibility
    - status: Status indicator ('0'=inactive, '1'=active)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    """
    tinh_thanh = TinhThanhModelSerializer(source='ma_tinh', read_only=True)
    ma_tinh_data = TinhThanhModelSerializer(source='ma_tinh', read_only=True)

    class Meta:
        model = QuanHuyenModel
        fields = ['uuid', 'entity_model', 'ma_quan', 'ten_quan', 'ten_quan2', 'ma_tinh',
                  'ma_tinh_data', 'tinh_thanh', 'status', 'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated', 'ma_tinh_data', 'tinh_thanh']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_quan": "Q1",
                "ten_quan": "Quận 1",
                "ten_quan2": "District 1",
                "ma_tinh": "123e4567-e89b-12d3-a456-************",
                "ma_tinh_data": {
                    "uuid": "123e4567-e89b-12d3-a456-************",
                    "ma_tinh": "HCM",
                    "ten_tinh": "Hồ Chí Minh",
                    "ten_tinh2": "Ho Chi Minh City",
                    "status": "1",
                    "created": "2023-04-21T10:30:00Z",
                    "updated": "2023-04-21T10:30:00Z"
                },
                "tinh_thanh": {
                    "uuid": "123e4567-e89b-12d3-a456-************",
                    "ma_tinh": "HCM",
                    "ten_tinh": "Hồ Chí Minh",
                    "ten_tinh2": "Ho Chi Minh City",
                    "status": "1",
                    "created": "2023-04-21T10:30:00Z",
                    "updated": "2023-04-21T10:30:00Z"
                },
                "status": "1",
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z"
            }
        }

    def to_internal_value(self, data):
        """
        Override to_internal_value to handle ma_tinh field when it's provided as a string
        (either a UUID or a province code).
        """
        # Handle ma_tinh field if provided as a string
        if 'ma_tinh' in data and data['ma_tinh'] and isinstance(data['ma_tinh'], str):
            # Get entity_model from context or data
            entity_model = None
            if 'entity_model' in data:
                entity_model = data['entity_model']
            elif self.context and 'entity_model' in self.context:
                entity_model = self.context['entity_model']

            if entity_model:
                # Try to find the province by UUID first
                try:
                    tinh_thanh = TinhThanhModel.objects.filter(
                        entity_model=entity_model,
                        uuid=data['ma_tinh']
                    ).first()
                except (ValueError, TypeError):
                    tinh_thanh = None

                # If not found by UUID, try by ma_tinh code
                if not tinh_thanh:
                    tinh_thanh = TinhThanhModel.objects.filter(
                        entity_model=entity_model,
                        ma_tinh=data['ma_tinh']
                    ).first()

                # Update ma_tinh with the actual UUID if found
                if tinh_thanh:
                    data = data.copy()
                    data['ma_tinh'] = str(tinh_thanh.uuid)

        # Call the parent method to handle the rest of the fields
        return super().to_internal_value(data)