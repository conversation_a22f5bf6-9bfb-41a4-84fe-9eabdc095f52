"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Admin configuration for NganHangModel (Bank).
"""

from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from django_ledger.models import NganHangModel


class NganHangModelAdmin(admin.ModelAdmin):
    """
    Admin class for NganHangModel (Bank).
    """
    list_display = [
        'ma_ngan_hang',
        'ten_ngan_hang',
        'ten_ngan_hang2',
        'ma_so',
        'status',
        'entity_model',
    ]
    list_filter = ['status', 'entity_model']
    search_fields = ['ma_ngan_hang', 'ten_ngan_hang', 'ten_ngan_hang2', 'ma_so']
    readonly_fields = ['uuid', 'created', 'updated']
    fieldsets = [
        (_('Basic Information'), {
            'fields': [
                'uuid',
                'entity_model',
                'ma_ngan_hang',
                'ten_ngan_hang',
                'ten_ngan_hang2',
                'ma_so',
                'action',
                'param',
                'status',
            ]
        }),
        (_('Metadata'), {
            'fields': [
                'created',
                'updated',
            ]
        }),
    ]
