"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.
"""

from rest_framework import serializers
from django_ledger.models import EntityUnitModel
from django_ledger.api.serializers.group import GroupModelSimpleSerializer

class EntityUnitModelSimpleSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for EntityUnitModel to be used in nested representations.
    """
    class Meta:
        model = EntityUnitModel
        fields = [
            'uuid',
            'name',
            'slug',
            'ma_unit',
            'ten_unit',
            'active',
            'hidden',
            'address',
            'phone',
            'email'
        ]

class EntityUnitModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the EntityUnitModel
    """
    # Add nested data for reference fields
    nh_dv1_data = serializers.SerializerMethodField(read_only=True)
    entity_slug = serializers.SerializerMethodField(read_only=True)
    entity_name = serializers.SerializerMethodField(read_only=True)

    def get_nh_dv1_data(self, obj):
        """Method field for nh_dv1_data"""
        if obj.nh_dv1:
            return GroupModelSimpleSerializer(obj.nh_dv1).data
        return None

    def get_entity_slug(self, obj):
        """
        Get the entity slug from the entity relationship
        """
        return obj.entity_slug

    def get_entity_name(self, obj):
        """
        Get the entity name from the entity relationship
        """
        return obj.entity_name

    def create(self, validated_data):
        """
        Create a new EntityUnitModel instance.
        The entity is set from the URL context.
        """
        # Get entity from context
        entity_slug = self.context['view'].kwargs.get('entity_slug')
        if not entity_slug:
            raise serializers.ValidationError("Entity slug is required")

        # Create the unit
        return super().create(validated_data)

    class Meta:
        model = EntityUnitModel
        fields = [
            'uuid',
            'name',
            'slug',
            'entity',
            'entity_slug',
            'entity_name',
            'document_prefix',
            'active',
            'hidden',
            'unit_id',
            'ma_unit',
            'ten_unit',
            'ten_unit2',
            'ten_unit3',
            'ma_so_thue',
            'ngay_ks',
            'nh_dv1',
            'nh_dv1_data',
            'entity_line11',
            'entity_line12',
            'entity_line21',
            'entity_line22',
            'entity_line31',
            'entity_line32',
            'entity_line41',
            'entity_line42',
            'entity_line51',
            'entity_line52',
            'gia_ton',
            'bp_yn',
            'lsx_yn',
            'bankAccountName',
            'bankName',
            'chiefAccountantName',
            'directorName',
            'cashierName',
            'storeKeeperName',
            'signatureFullname',
            'signatureFullnameSeal',
            'ximage',
            'address',
            'district',
            'province',
            'phone',
            'fax',
            'email',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'slug', 'entity_slug', 'entity_name', 'created', 'updated']
