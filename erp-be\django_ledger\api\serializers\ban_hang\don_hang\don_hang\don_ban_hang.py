"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Don Ban Hang (Sales Order) serializer implementation.
"""

from rest_framework import serializers

from django_ledger.models.ban_hang.don_hang.don_hang.don_ban_hang import DonBanHangModel
from django_ledger.models.ban_hang.don_hang.don_hang.chi_tiet_don_ban_hang import ChiTietDonBanHangModel
from django_ledger.api.serializers.ban_hang.don_hang.don_hang.chi_tiet_don_ban_hang import ChiTietDonBanHangModelSerializer


class DonBanHangModelSerializer(serializers.ModelSerializer):
    """
    Don Ban Hang (Sales Order) serializer.
    """
    chi_tiet = ChiTietDonBanHangModelSerializer(many=True, required=False)
    ref_data = serializers.SerializerMethodField()

    class Meta:
        model = DonBanHangModel
        fields = [
            'uuid',
            'entity_model',
            'ma_ngv',
            'ma_kh',
            'ma_so_thue',
            'ong_ba',
            'dia_chi',
            'e_mail',
            'ma_nvbh',
            'ma_tt',
            'dien_giai',
            'ngay_hl',
            'unit_id',
            'i_so_ct',
            'ma_nk',
            'so_ct',
            'ngay_ct',
            'ngay_lct',
            'so_ct2',
            'ma_nt',
            'ty_gia',
            'status',
            'transfer_yn',
            'ma_dc',
            'ma_ptvc',
            'ma_pttt',
            'ma_ptgh',
            'ma_gd',
            'treo_dh',
            't_so_luong',
            't_tien_nt2',
            't_tien2',
            't_thue_nt',
            't_thue',
            't_ck_nt',
            't_ck',
            't_tt_nt',
            't_tt',
            'chi_tiet',
            'ref_data',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'ref_data', 'created', 'updated']

    def get_ref_data(self, instance):
        """
        Get referenced data for the instance.
        """
        data = {}

        # Add referenced data for ma_kh (customer)
        if instance.ma_kh:
            try:
                data['ma_kh'] = {
                    'uuid': str(instance.ma_kh.uuid),
                    'ma_kh': instance.ma_kh.ma_kh,
                    'ten_kh': instance.ma_kh.ten_kh,
                    'dia_chi': instance.ma_kh.dia_chi
                }
            except Exception as e:
                pass

        # Add referenced data for ma_nvbh (sales employee)
        if instance.ma_nvbh:
            try:
                data['ma_nvbh'] = {
                    'uuid': str(instance.ma_nvbh.uuid),
                    'ma_nv': instance.ma_nvbh.ma_nv,
                    'ten_nv': instance.ma_nvbh.ten_nv
                }
            except Exception as e:
                pass

        # Add referenced data for ma_nk (document book)
        if instance.ma_nk:
            try:
                data['ma_nk'] = {
                    'uuid': str(instance.ma_nk.uuid),
                    'ma_nk': instance.ma_nk.ma_quyen,
                    'ten_nk': instance.ma_nk.ten_quyen
                }
            except Exception as e:
                pass

        # Add referenced data for so_ct (document)
        if instance.so_ct:
            try:
                data['so_ct'] = {
                    'uuid': str(instance.so_ct.uuid),
                    'so_ct': instance.so_ct.so_ct,
                    'dien_giai': instance.so_ct.dien_giai
                }
            except Exception as e:
                pass

        # Add referenced data for ma_nt (currency)
        if instance.ma_nt:
            try:
                data['ma_nt'] = {
                    'uuid': str(instance.ma_nt.uuid),
                    'ma_nt': instance.ma_nt.ma_nt,
                    'ten_nt': instance.ma_nt.ten_nt
                }
            except Exception as e:
                pass

        # Add referenced data for ma_dc (address)
        if instance.ma_dc:
            try:
                data['ma_dc'] = {
                    'uuid': str(instance.ma_dc.uuid),
                    'ma_dc': instance.ma_dc.ma_dc,
                    'ten_dc': instance.ma_dc.ten_dc
                }
            except Exception as e:
                pass

        # Add referenced data for ma_ptvc (shipping method)
        if instance.ma_ptvc:
            try:
                data['ma_ptvc'] = {
                    'uuid': str(instance.ma_ptvc.uuid),
                    'ma_ptvc': instance.ma_ptvc.ma_ptvc,
                    'ten_ptvc': instance.ma_ptvc.ten_ptvc
                }
            except Exception as e:
                pass

        # Add referenced data for ma_pttt (payment method)
        if instance.ma_pttt:
            try:
                data['ma_pttt'] = {
                    'uuid': str(instance.ma_pttt.uuid),
                    'ma_pttt': instance.ma_pttt.ma_pttt,
                    'ten_pttt': instance.ma_pttt.ten_pttt
                }
            except Exception as e:
                pass

        # Add referenced data for ma_ptgh (delivery method)
        if instance.ma_ptgh:
            try:
                data['ma_ptgh'] = {
                    'uuid': str(instance.ma_ptgh.uuid),
                    'ma_ptgh': instance.ma_ptgh.ma_ptgh,
                    'ten_ptgh': instance.ma_ptgh.ten_ptgh
                }
            except Exception as e:
                pass

        return data


