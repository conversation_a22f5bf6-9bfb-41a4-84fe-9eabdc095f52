{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [        
        {
            "name": "DJL RunServer",
            "type": "python",
            "request": "launch",
            "python": "${workspaceFolder}/venv/bin/python",
            "program": "${workspaceFolder}/manage.py",
            "args": [
                "runserver"
            ],
            "django": true,
            "justMyCode": true,
            "presentation": {
                "hidden": false,
                "group": "django-ledger",
                "order": 1
            },
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "Starting development server at (https?://\\S+|[0-9]+\\.[0-9]+\\.[0-9]+\\.[0-9]+:\\d+)",
                "uriFormat": "%s"
            },
            "windows": {
                "python": "${workspaceFolder}/venv/Scripts/python.exe"
            }
        }
    ]
}
