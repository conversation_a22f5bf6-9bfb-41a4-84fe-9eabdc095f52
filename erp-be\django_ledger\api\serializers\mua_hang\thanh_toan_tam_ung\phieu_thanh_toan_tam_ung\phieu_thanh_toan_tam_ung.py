"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuThanhToanTamUng (Advance Payment Settlement Voucher) Serializer
"""

from rest_framework import serializers

from django_ledger.models.mua_hang.thanh_toan_tam_ung import PhieuThanhToanTamUngModel
from django_ledger.services.mua_hang.thanh_toan_tam_ung import PhieuThanhToanTamUngService


class PhieuThanhToanTamUngSerializer(serializers.ModelSerializer):
    """
    A serializer class for the PhieuThanhToanTamUngModel.
    """

    _data = serializers.SerializerMethodField()
    ma_kh_data = serializers.SerializerMethodField()
    tk_data = serializers.SerializerMethodField()
    ma_nt_data = serializers.SerializerMethodField()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.service = PhieuThanhToanTamUngService()

    class Meta:
        model = PhieuThanhToanTamUngModel
        fields = [
            'uuid',
            'ma_ngv',
            'ma_kh',
            'ma_kh_data',
            'ma_so_thue',
            'ong_ba',
            'dia_chi',
            'e_mail',
            'tk',
            'tk_data',
            'dien_giai',
            'unit_id',
            'i_so_ct',
            'ma_nk',
            'so_ct',
            'ngay_ct',
            'ngay_lct',
            'ma_nt',
            'ma_nt_data',
            'ty_gia',
            'status',
            'transfer_yn',
            't_tien_nt',
            't_tien',
            't_thue_nt',
            't_thue',
            't_tt_nt',
            't_tt',
            't_tien_cl_nt',
            't_tien_cl',
            'qt_tu_yn',
            '_data'
        ]
        read_only_fields = ['uuid']

    def get__data(self, instance):
        """
        Get referenced data for the instance.

        Parameters
        ----------
        instance: PhieuThanhToanTamUngModel
            The PhieuThanhToanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing referenced data.
        """
        data = {}

        if hasattr(instance, 'ma_kh') and instance.ma_kh:
            data['ma_kh'] = {
                'uuid': instance.ma_kh.uuid,
                'ma_kh': instance.ma_kh.ma_kh,
                'ten_kh': instance.ma_kh.ten_kh
            }

        if hasattr(instance, 'tk') and instance.tk:
            data['tk'] = {
                'uuid': instance.tk.uuid,
                'code': instance.tk.code,
                'name': instance.tk.name
            }

        if hasattr(instance, 'ma_nt') and instance.ma_nt:
            data['ma_nt'] = {
                'uuid': instance.ma_nt.uuid,
                'ma_nt': instance.ma_nt.ma_nt,
                'ten_nt': instance.ma_nt.ten_nt
            }

        return data

    def get_ma_kh_data(self, instance):
        """
        Get data for the ma_kh field.

        Parameters
        ----------
        instance: PhieuThanhToanTamUngModel
            The PhieuThanhToanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing the ma_kh data.
        """
        if hasattr(instance, 'ma_kh') and instance.ma_kh:
            return {
                'uuid': instance.ma_kh.uuid,
                'ma_kh': instance.ma_kh.ma_kh,
                'ten_kh': instance.ma_kh.ten_kh
            }
        return None

    def get_tk_data(self, instance):
        """
        Get data for the tk field.

        Parameters
        ----------
        instance: PhieuThanhToanTamUngModel
            The PhieuThanhToanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing the tk data.
        """
        if hasattr(instance, 'tk') and instance.tk:
            return {
                'uuid': instance.tk.uuid,
                'code': instance.tk.code,
                'name': instance.tk.name
            }
        return None

    def get_ma_nt_data(self, instance):
        """
        Get data for the ma_nt field.

        Parameters
        ----------
        instance: PhieuThanhToanTamUngModel
            The PhieuThanhToanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing the ma_nt data.
        """
        if hasattr(instance, 'ma_nt') and instance.ma_nt:
            return {
                'uuid': instance.ma_nt.uuid,
                'ma_nt': instance.ma_nt.ma_nt,
                'ten_nt': instance.ma_nt.ten_nt
            }
        return None

    def create(self, validated_data):
        """
        Create a new PhieuThanhToanTamUngModel instance.

        Parameters
        ----------
        validated_data: dict
            The validated data for the new instance.

        Returns
        -------
        PhieuThanhToanTamUngModel
            The newly created PhieuThanhToanTamUngModel instance.
        """
        entity_slug = self.context['entity_slug']
        user_model = self.context['request'].user

        instance = self.service.create(
            entity_slug=entity_slug,
            user_model=user_model,
            **validated_data
        )
        return instance

    def update(self, instance, validated_data):
        """
        Update an existing PhieuThanhToanTamUngModel instance.

        Parameters
        ----------
        instance: PhieuThanhToanTamUngModel
            The PhieuThanhToanTamUngModel instance to update.
        validated_data: dict
            The validated data for the update.

        Returns
        -------
        PhieuThanhToanTamUngModel
            The updated PhieuThanhToanTamUngModel instance.
        """
        entity_slug = self.context['entity_slug']
        user_model = self.context['request'].user

        instance = self.service.update(
            entity_slug=entity_slug,
            user_model=user_model,
            instance=instance,
            **validated_data
        )
        return instance
