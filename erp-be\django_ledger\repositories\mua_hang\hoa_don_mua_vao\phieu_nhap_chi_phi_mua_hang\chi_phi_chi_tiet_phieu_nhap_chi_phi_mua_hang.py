"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiPhiChiTietPhieuNhapChiPhiMuahangRepository, which handles database operations
for the ChiPhiChiTietPhieuNhapChiPhiMuahangModel.
"""

from typing import Dict, Any, Optional, List, Union
from uuid import UUID

from django.db.models import QuerySet

from django_ledger.models.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import ChiPhiChiTietPhieuNhapChiPhiMuahangModel, PhieuNhapChiPhiMuaHangModel
from django_ledger.repositories.base import BaseRepository
from django_ledger.models import ChiPhiModel, VatTuModel

class ChiPhiChiTietPhieuNhapChiPhiMuahangRepository(BaseRepository):
    """
    Repository class for ChiPhiChiTietPhieuNhapChiPhiMuahangModel.
    Handles database operations for the model.
    """

    def __init__(self):
        super().__init__(model_class=ChiPhiChiTietPhieuNhapChiPhiMuahangModel)

    def get_queryset(self) -> QuerySet:
        """
        Returns the base queryset for ChiPhiChiTietPhieuNhapChiPhiMuahangModel.

        Returns
        -------
        QuerySet
            The base queryset for ChiPhiChiTietPhieuNhapChiPhiMuahangModel.
        """
        return self.model_class.objects.all()

    def get_by_id(self, uuid: Union[str, UUID]) -> Optional[ChiPhiChiTietPhieuNhapChiPhiMuahangModel]:
        """
        Retrieves a ChiPhiChiTietPhieuNhapChiPhiMuahangModel by its UUID.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiPhiChiTietPhieuNhapChiPhiMuahangModel to retrieve.

        Returns
        -------
        Optional[ChiPhiChiTietPhieuNhapChiPhiMuahangModel]
            The ChiPhiChiTietPhieuNhapChiPhiMuahangModel with the given UUID, or None if not found.
        """
        try:
            return self.model_class.objects.get(uuid=uuid)
        except self.model_class.DoesNotExist:
            return None

    def list_for_phieu_nhap(self, phieu_nhap_id: Union[str, UUID], **kwargs) -> QuerySet:
        """
        Lists ChiPhiChiTietPhieuNhapChiPhiMuahangModel instances for a specific purchase expense receipt.

        Parameters
        ----------
        phieu_nhap_id : Union[str, UUID]
            The UUID of the PhieuNhapChiPhiMuaHangModel.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of ChiPhiChiTietPhieuNhapChiPhiMuahangModel instances.
        """
        return self.model_class.objects.for_phieu_nhap(phieu_nhap_id=phieu_nhap_id).filter(**kwargs)

    def create(self, phieu_nhap_id: Union[str, UUID], data: Dict[str, Any]) -> ChiPhiChiTietPhieuNhapChiPhiMuahangModel:
        """
        Creates a new ChiPhiChiTietPhieuNhapChiPhiMuahangModel instance.

        Parameters
        ----------
        phieu_nhap_id : Union[str, UUID]
            The UUID of the PhieuNhapChiPhiMuaHangModel.
        data : Dict[str, Any]
            The data for the new ChiPhiChiTietPhieuNhapChiPhiMuahangModel.

        Returns
        -------
        ChiPhiChiTietPhieuNhapChiPhiMuahangModel
            The created ChiPhiChiTietPhieuNhapChiPhiMuahangModel instance.
        """
        # Get the PhieuNhapChiPhiMuaHangModel instance
        phieu_nhap = PhieuNhapChiPhiMuaHangModel.objects.get(uuid=phieu_nhap_id)

        # Convert UUIDs to model instances
        data = self.convert_uuids_to_model_instances(data)

        # Create the ChiPhiChiTietPhieuNhapChiPhiMuahangModel instance
        instance = self.model_class(phieu_nhap=phieu_nhap, **data)
        instance.save()

        return instance

    def update(self, uuid: Union[str, UUID], data: Dict[str, Any]) -> Optional[ChiPhiChiTietPhieuNhapChiPhiMuahangModel]:
        """
        Updates an existing ChiPhiChiTietPhieuNhapChiPhiMuahangModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiPhiChiTietPhieuNhapChiPhiMuahangModel to update.
        data : Dict[str, Any]
            The data to update the ChiPhiChiTietPhieuNhapChiPhiMuahangModel with.

        Returns
        -------
        Optional[ChiPhiChiTietPhieuNhapChiPhiMuahangModel]
            The updated ChiPhiChiTietPhieuNhapChiPhiMuahangModel instance, or None if not found.
        """
        instance = self.get_by_id(uuid=uuid)
        if instance:
            # Convert UUIDs to model instances
            data = self.convert_uuids_to_model_instances(data)

            for key, value in data.items():
                setattr(instance, key, value)
            instance.save()
            return instance
        return None

    def delete(self, uuid: Union[str, UUID]) -> bool:
        """
        Deletes a ChiPhiChiTietPhieuNhapChiPhiMuahangModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiPhiChiTietPhieuNhapChiPhiMuahangModel to delete.

        Returns
        -------
        bool
            True if the ChiPhiChiTietPhieuNhapChiPhiMuahangModel was deleted, False otherwise.
        """
        instance = self.get_by_id(uuid=uuid)
        if instance:
            instance.delete()
            return True
        return False

    def convert_uuids_to_model_instances(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Converts UUID strings in the data to model instances.

        Parameters
        ----------
        data : Dict[str, Any]
            The data containing UUID strings.

        Returns
        -------
        Dict[str, Any]
            The data with UUID strings converted to model instances.
        """


        # Convert ma_cp UUID to ChiPhiModel instance
        if 'ma_cp' in data and data['ma_cp']:
            try:
                data['ma_cp'] = ChiPhiModel.objects.get(uuid=data['ma_cp'])
            except ChiPhiModel.DoesNotExist:
                data['ma_cp'] = None

        # Convert ma_vt UUID to VatTuModel instance
        if 'ma_vt' in data and data['ma_vt']:
            try:
                data['ma_vt'] = VatTuModel.objects.get(uuid=data['ma_vt'])
            except VatTuModel.DoesNotExist:
                data['ma_vt'] = None

        return data
