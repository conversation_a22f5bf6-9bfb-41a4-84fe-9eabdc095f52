"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Quyen Hoa Don Dien Tu (Electronic Invoice Permission) module.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.he_thong.ket_noi_hddt.quyen_hoa_don_dien_tu.quyen_hoa_don_dien_tu import QuyenHoaDonDienTuViewSet

# Main router for QuyenHoaDonDienTu
router = DefaultRouter()
router.register('', QuyenHoaDonDienTuViewSet, basename='quyen-hoa-don-dien-tu')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
