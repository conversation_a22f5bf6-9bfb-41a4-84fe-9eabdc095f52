"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuYeuCauXuatKho (Warehouse Export Request) serializer implementation.
"""

from rest_framework import serializers

from django_ledger.models.ton_kho.xuat_kho_noi_bo.phieu_yeu_cau_xuat_kho import PhieuYeuCauXuatKhoModel

# Import serializers for foreign key fields
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.quyen_chung_tu import QuyenChungTuListSerializer
from django_ledger.api.serializers.chung_tu import ChungTuSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer


class PhieuYeuCauXuatKhoModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the PhieuYeuCauXuatKhoModel.

    This serializer handles the conversion between PhieuYeuCauXuatKhoModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields (entity_model, ma_bp, ma_nk, so_ct, ma_nt)
    - Adds additional fields with "_data" suffix (entity_model_data, ma_bp_data, ma_nk_data, so_ct_data, ma_nt_data)
      that contain the complete nested object data with all available fields
    - When deserializing, accepts UUIDs for reference fields
    """
    # Add nested serializers for foreign key fields
    entity_model_data = EntityModelSerializer(source='entity_model', read_only=True)
    ma_bp_data = BoPhanModelSerializer(source='ma_bp', read_only=True)
    ma_nk_data = QuyenChungTuListSerializer(source='ma_nk', read_only=True)
    so_ct_data = ChungTuSerializer(source='so_ct', read_only=True)
    ma_nt_data = NgoaiTeSerializer(source='ma_nt', read_only=True)

    # Add chi_tiet field for nested serialization
    chi_tiet = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = PhieuYeuCauXuatKhoModel
        fields = [
            'uuid',
            'entity_model',
            'entity_model_data',
            'ma_gd',
            'ma_bp',
            'ma_bp_data',
            'ong_ba',
            'dien_giai',
            'id',
            'unit_id',
            'i_so_ct',
            'ten_nk',
            'ma_nk',
            'ma_nk_data',
            'so_ct',
            'so_ct_data',
            'ngay_ct',
            'ngay_lct',
            'xdatetime2',
            'ma_nt',
            'ma_nt_data',
            'ty_gia',
            'status',
            'transfer_yn',
            't_so_luong',
            'chi_tiet',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model_data',
            'ma_bp_data',
            'ma_nk_data',
            'so_ct_data',
            'ma_nt_data',
            'chi_tiet',
            'created',
            'updated'
        ]

    def get_chi_tiet(self, obj):
        """
        Get the chi_tiet (details) for the PhieuYeuCauXuatKho.
        This is a method field that returns a list of ChiTietPhieuYeuCauXuatKho objects.

        Parameters
        ----------
        obj : PhieuYeuCauXuatKhoModel
            The PhieuYeuCauXuatKhoModel instance

        Returns
        -------
        list
            List of serialized ChiTietPhieuYeuCauXuatKho objects
        """
        # Import here to avoid circular import
        from django_ledger.api.serializers.ton_kho.xuat_kho_noi_bo.phieu_yeu_cau_xuat_kho.chi_tiet_phieu_yeu_cau_xuat_kho import ChiTietPhieuYeuCauXuatKhoModelSerializer

        chi_tiet = obj.chi_tiet_phieu_yeu_cau_xuat_kho.all()
        return ChiTietPhieuYeuCauXuatKhoModelSerializer(chi_tiet, many=True).data
