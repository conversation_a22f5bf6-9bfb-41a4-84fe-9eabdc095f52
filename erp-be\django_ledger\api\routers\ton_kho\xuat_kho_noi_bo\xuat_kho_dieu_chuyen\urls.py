"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuXuatDieuChuyen (Warehouse Transfer) router implementation.
"""

from django.urls import path

from django_ledger.api.views.ton_kho.xuat_kho_noi_bo.xuat_kho_dieu_chuyen import PhieuXuatDieuChuyenViewSet, ChiTietPhieuXuatDieuChuyenViewSet

urlpatterns = [
    # PhieuXuatDieuChuyen routes
    path('', PhieuXuatDieuChuyenViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='phieu-xuat-dieu-chuyen-list'),

    path('<uuid:pk>/', PhieuXuatDieuChuyenViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='phieu-xuat-dieu-chuyen-detail'),

    # Nested routes for ChiTietPhieuXuatDieuChuyen
    path('<uuid:phieu_xuat_dieu_chuyen_uuid>/chi-tiet/', ChiTietPhieuXuatDieuChuyenViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='chi-tiet-phieu-xuat-dieu-chuyen-list'),

    path('<uuid:phieu_xuat_dieu_chuyen_uuid>/chi-tiet/<uuid:uuid>/', ChiTietPhieuXuatDieuChuyenViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='chi-tiet-phieu-xuat-dieu-chuyen-detail'),
]
