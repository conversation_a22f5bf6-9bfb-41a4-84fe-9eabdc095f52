"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for KhaiBaoNhomNguoiSuDung model.
"""
from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models import KhaiBaoNhomNguoiSuDungModel
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.he_thong.tai_khoan.khai_bao_nhom_nguoi_su_dung.chi_tiet_khai_bao_nhom_nguoi_su_dung import ChiTietKhaiBaoNhomNguoiSuDungSerializer


class KhaiBaoNhomNguoiSuDungSerializer(serializers.ModelSerializer):
    """
    Serializer for KhaiBaoNhomNguoiSuDung model.
    """
    # Read-only fields for related objects
    # Child data
    chi_tiet_data = serializers.SerializerMethodField(read_only=True)
    chi_tiet = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = KhaiBaoNhomNguoiSuDungModel
        fields = [
            'uuid',
            'entity_model',
            'group_id',
            'groupname',
            'dien_giai',
            'ds_username',
            'kh_yn',
            'nv_yn',
            'chi_tiet_data',
            'chi_tiet',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'chi_tiet_data',
            'created',
            'updated'
        ]


    def get_chi_tiet_data(self, obj):
        """
        Get child details data.
        """
        chi_tiet = obj.group_users.all()
        return ChiTietKhaiBaoNhomNguoiSuDungSerializer(chi_tiet, many=True).data




class KhaiBaoNhomNguoiSuDungListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for listing KhaiBaoNhomNguoiSuDung instances.
    """

    # Count of related users
    user_count = serializers.SerializerMethodField()
    # Child data
    chi_tiet_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = KhaiBaoNhomNguoiSuDungModel
        fields = [
            'uuid',
            'group_id',
            'groupname',
            'dien_giai',
            'kh_yn',
            'nv_yn',
            'user_count',
            'chi_tiet_data',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated', 'user_count', 'chi_tiet_data']

    def get_user_count(self, obj):
        """
        Get the count of users in this group.

        Parameters
        ----------
        obj : KhaiBaoNhomNguoiSuDungModel
            The user group instance

        Returns
        -------
        int
            The count of users in this group
        """
        if hasattr(obj, 'group_users'):
            return obj.group_users.count()
        return 0

    def get_chi_tiet_data(self, obj):
        """
        Get child details data.

        Parameters
        ----------
        obj : KhaiBaoNhomNguoiSuDungModel
            The user group instance

        Returns
        -------
        list
            List of child details data
        """
        chi_tiet = obj.group_users.all()
        return ChiTietKhaiBaoNhomNguoiSuDungSerializer(chi_tiet, many=True).data
