"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the serializers for the ChiPhiChiTietHoaDonMuaHangTrongNuocModel.
"""

from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc import (
    ChiPhiChiTietHoaDonMuaHangTrongNuocModel,
)
from rest_framework import serializers


class ChiPhiChiTietHoaDonMuaHangTrongNuocModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the ChiPhiChiTietHoaDonMuaHangTrongNuocModel.
    Used for read operations.
    """

    # Read-only fields for related objects
    hoa_don_data = serializers.SerializerMethodField(read_only=True)
    ma_cp_data = serializers.SerializerMethodField(read_only=True)
    ma_vt_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiPhiChiTietHoaDonMuaHangTrongNuocModel
        fields = [
            "uuid",
            "hoa_don",
            "hoa_don_data",
            "line",
            "ma_cp",
            "ma_cp_data",
            "ma_vt",
            "ma_vt_data",
            "tien_cp_nt",
            "tien_cp",
            "line_vt",
            "line_cp",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "hoa_don_data",
            "ma_cp_data",
            "ma_vt_data",
            "created",
            "updated",
        ]

    def get_hoa_don_data(self, obj):
        """
        Get basic information about the invoice
        """
        if not obj.hoa_don:
            return None

        # Get basic invoice data
        hoa_don_data = {
            "uuid": str(obj.hoa_don.uuid),
            "so_ct": getattr(obj.hoa_don, "so_ct_id", None),
            "ngay_ct": getattr(obj.hoa_don, "ngay_ct", None),
            "ten_kh": getattr(obj.hoa_don, "ten_kh", ""),
        }

        # Add document number if available
        if hasattr(obj.hoa_don, "so_ct") and obj.hoa_don.so_ct:
            hoa_don_data["so_ct_data"] = {
                "uuid": str(obj.hoa_don.so_ct.uuid),
                "ma_ct": getattr(obj.hoa_don.so_ct, "ma_ct", ""),
                "ten_ct": getattr(obj.hoa_don.so_ct, "ten_ct", ""),
            }

        return hoa_don_data

    def get_ma_cp_data(self, obj):
        """
        Get basic information about the expense
        """
        if not obj.ma_cp:
            return None

        return {"ma_cp": obj.ma_cp, "ten_cp": getattr(obj, "ten_cp", "")}

    def get_ma_vt_data(self, obj):
        """
        Get basic information about the material
        """
        if not obj.ma_vt:
            return None

        return {
            "ma_vt": obj.ma_vt,
            "ten_vt": getattr(obj, "ten_vt", ""),
            "dvt": getattr(obj, "dvt", ""),
        }


class ChiPhiChiTietHoaDonMuaHangTrongNuocModelCreateUpdateSerializer(
    serializers.ModelSerializer
):
    """
    Serializer for the ChiPhiChiTietHoaDonMuaHangTrongNuocModel.
    Used for create and update operations.
    """

    class Meta:
        model = ChiPhiChiTietHoaDonMuaHangTrongNuocModel
        fields = [
            "uuid",
            "hoa_don",
            "line",
            "ma_cp",
            "ma_vt",
            "tien_cp_nt",
            "tien_cp",
            "line_vt",
            "line_cp",
        ]
        read_only_fields = ["uuid"]

    def validate(self, data):
        """
        Validate the data before creating or updating the model instance.

        Parameters
        ----------
        data : dict
            The data to validate.

        Returns
        -------
        dict
            The validated data.
        """
        # Add any custom validation here
        return data
