"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for KhaiBaoHeSoPhanBo API.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.gia_thanh.khai_bao.khai_bao_he_so_phan_bo import KhaiBaoHeSoPhanBoViewSet

# Main router for KhaiBaoHeSoPhanBo
router = DefaultRouter()
router.register('', KhaiBaoHeSoPhanBoViewSet, basename='khai-bao-he-so-phan-bo')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
