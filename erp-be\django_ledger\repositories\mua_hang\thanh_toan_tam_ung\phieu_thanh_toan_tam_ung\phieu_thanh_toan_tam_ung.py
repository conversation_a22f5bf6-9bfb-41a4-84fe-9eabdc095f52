"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuThanhToanTamUng (Advance Payment Settlement Voucher) Repository
"""

from django.db.models import QuerySet
from django.utils.translation import gettext_lazy as _

from django_ledger.models.mua_hang.thanh_toan_tam_ung import PhieuThanhToanTamUngModel
from django_ledger.repositories.base import BaseRepository


class PhieuThanhToanTamUngRepository(BaseRepository):
    """
    A repository class for the PhieuThanhToanTamUngModel.
    """

    def __init__(self):
        self.model_class = PhieuThanhToanTamUngModel

    def get_by_id(self, entity_slug: str, uuid: str, user_model) -> PhieuThanhToanTamUngModel:
        """
        Get a PhieuThanhToanTamUngModel by its UUID.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug to filter by.
        uuid: str
            The UUID of the PhieuThanhToanTamUngModel to retrieve.
        user_model
            The authenticated Django UserModel making the request.

        Returns
        -------
        PhieuThanhToanTamUngModel
            The requested PhieuThanhToanTamUngModel instance.
        """
        qs = self.model_class.objects.for_entity(
            entity_slug=entity_slug,
            user_model=user_model
        )
        return qs.get(uuid__exact=uuid)

    def list(self, entity_slug: str, user_model, **kwargs) -> QuerySet:
        """
        Get a QuerySet of PhieuThanhToanTamUngModel instances.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug to filter by.
        user_model
            The authenticated Django UserModel making the request.
        kwargs
            Additional filtering parameters.

        Returns
        -------
        QuerySet
            A QuerySet of PhieuThanhToanTamUngModel instances.
        """
        qs = self.model_class.objects.for_entity(
            entity_slug=entity_slug,
            user_model=user_model
        )
        return qs

    def create(self, entity_slug: str, user_model, **kwargs) -> PhieuThanhToanTamUngModel:
        """
        Create a new PhieuThanhToanTamUngModel instance.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug to associate with the new instance.
        user_model
            The authenticated Django UserModel making the request.
        kwargs
            Additional parameters for the new instance.

        Returns
        -------
        PhieuThanhToanTamUngModel
            The newly created PhieuThanhToanTamUngModel instance.
        """
        from django_ledger.models import EntityModel
        entity_model = EntityModel.objects.get(slug__exact=entity_slug)
        
        # Convert UUIDs to model instances
        self.convert_uuids_to_model_instances(kwargs)
        
        instance = self.model_class(
            entity_model=entity_model,
            **kwargs
        )
        instance.save()
        return instance

    def update(self, entity_slug: str, user_model, instance: PhieuThanhToanTamUngModel, **kwargs) -> PhieuThanhToanTamUngModel:
        """
        Update an existing PhieuThanhToanTamUngModel instance.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug to filter by.
        user_model
            The authenticated Django UserModel making the request.
        instance: PhieuThanhToanTamUngModel
            The PhieuThanhToanTamUngModel instance to update.
        kwargs
            Additional parameters to update.

        Returns
        -------
        PhieuThanhToanTamUngModel
            The updated PhieuThanhToanTamUngModel instance.
        """
        # Convert UUIDs to model instances
        self.convert_uuids_to_model_instances(kwargs)
        
        for key, value in kwargs.items():
            setattr(instance, key, value)
        instance.save()
        return instance

    def delete(self, entity_slug: str, user_model, instance: PhieuThanhToanTamUngModel) -> bool:
        """
        Delete a PhieuThanhToanTamUngModel instance.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug to filter by.
        user_model
            The authenticated Django UserModel making the request.
        instance: PhieuThanhToanTamUngModel
            The PhieuThanhToanTamUngModel instance to delete.

        Returns
        -------
        bool
            True if the instance was deleted, False otherwise.
        """
        instance.delete()
        return True

    def convert_uuids_to_model_instances(self, data: dict) -> None:
        """
        Convert UUID strings to model instances in the data dictionary.

        Parameters
        ----------
        data: dict
            The data dictionary to process.
        """
        from django_ledger.models import AccountModel, CustomerModel, NgoaiTeModel
        
        if 'tk' in data and isinstance(data['tk'], str):
            try:
                data['tk'] = AccountModel.objects.get(uuid__exact=data['tk'])
            except AccountModel.DoesNotExist:
                pass
                
        if 'ma_kh' in data and isinstance(data['ma_kh'], str):
            try:
                data['ma_kh'] = CustomerModel.objects.get(uuid__exact=data['ma_kh'])
            except CustomerModel.DoesNotExist:
                pass
                
        if 'ma_nt' in data and isinstance(data['ma_nt'], str):
            try:
                data['ma_nt'] = NgoaiTeModel.objects.get(uuid__exact=data['ma_nt'])
            except NgoaiTeModel.DoesNotExist:
                pass
