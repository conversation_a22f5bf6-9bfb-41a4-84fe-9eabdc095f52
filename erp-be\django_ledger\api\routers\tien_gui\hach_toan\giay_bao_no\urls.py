"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for Bank Transfer Document (Giay Bao No) API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.tien_gui.hach_toan.giay_bao_no import (
    BankTransferDocumentViewSet,
    BankTransferDetailViewSet,
    TaxDetailViewSet,
    BankFeeDetailViewSet
)

# Main router for BankTransferDocument
router = DefaultRouter()
router.register('', BankTransferDocumentViewSet, basename='giay-bao-no')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),

    # Nested routes for giay-bao-no
    path('<uuid:giay_bao_no_uuid>/', include([
        # Chi tiet giay bao no routes
        path('chi-tiet/', BankTransferDetailViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-tiet-giay-bao-no-list'),

        path('chi-tiet/<uuid:uuid>/', BankTransferDetailViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-tiet-giay-bao-no-detail'),

        # Thue giay bao no routes
        path('thue/', TaxDetailViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='thue-giay-bao-no-list'),

        path('thue/<uuid:uuid>/', TaxDetailViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='thue-giay-bao-no-detail'),

        # Phi ngan hang giay bao no routes
        path('phi-ngan-hang/', BankFeeDetailViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='phi-ngan-hang-giay-bao-no-list'),

        path('phi-ngan-hang/<uuid:uuid>/', BankFeeDetailViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='phi-ngan-hang-giay-bao-no-detail'),
    ])),
]
