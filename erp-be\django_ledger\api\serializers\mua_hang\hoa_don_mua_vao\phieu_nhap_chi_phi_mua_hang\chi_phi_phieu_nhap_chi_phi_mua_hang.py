"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiPhiPhieuNhapChiPhiMuaHangSerializer, which handles serialization
for the ChiPhiPhieuNhapChiPhiMuaHangModel.
"""

from rest_framework import serializers

from django_ledger.models.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import ChiPhiPhieuNhapChiPhiMuaHangModel


class ChiPhiPhieuNhapChiPhiMuaHangSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiPhiPhieuNhapChiPhiMuaHangModel.
    """

    # Reference data fields
    ma_cp_data = serializers.SerializerMethodField()

    class Meta:
        model = ChiPhiPhieuNhapChiPhiMuaHangModel
        fields = [
            'uuid', 'phieu_nhap', 'line', 'ma_cp', 'tien_cp_nt', 'tien_cp',
            'ma_cp_data'
        ]
        read_only_fields = ['uuid', 'phieu_nhap']

    def get_ma_cp_data(self, obj):
        """
        Returns the expense data for the ma_cp field.
        """
        if obj.ma_cp:
            return {
                'uuid': obj.ma_cp.uuid,
                'ma_cp': obj.ma_cp.ma_cp,
                'ten_cp': obj.ma_cp.ten_cp
            }
        return None


