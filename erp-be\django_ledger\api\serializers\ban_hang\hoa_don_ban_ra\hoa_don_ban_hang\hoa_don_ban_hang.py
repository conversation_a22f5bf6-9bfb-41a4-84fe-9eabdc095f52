"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Hoa Don Ban Hang (Sales Invoice) serializer implementation.
"""

from rest_framework import serializers

from django_ledger.api.serializers.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang.chi_tiet_hoa_don_ban_hang import (
    ChiTietHoaDonBanHangSerializer,
)
from django_ledger.api.serializers.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang.thong_tin_thanh_toan_hoa_don_ban_hang import (
    ThongTinThanhToanHoaDonBanHangSerializer,
)
from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang import (
    HoaDonBanHangModel,
)
from django_ledger.services.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang import (
    HoaDonBanHangService,
)

# Import serializers for foreign key fields
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.nhan_vien import NhanVienModelSerializer
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.chung_tu import ChungTuSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer
from django_ledger.api.serializers.dia_chi import DiaChiSerializer
from django_ledger.api.serializers.phuong_tien_van_chuyen import PhuongTienVanChuyenModelSerializer
from django_ledger.api.serializers.phuong_tien_giao_hang import PhuongTienGiaoHangModelSerializer
from django_ledger.api.serializers.bank_account import BankAccountModelSerializer
from django_ledger.api.serializers.unit import EntityUnitModelSimpleSerializer


class HoaDonBanHangSerializer(serializers.ModelSerializer):
    """
    Serializer for HoaDonBanHangModel.
    """

    ma_kh_data = serializers.SerializerMethodField()
    ma_nvbh_data = serializers.SerializerMethodField()
    tk_data = serializers.SerializerMethodField()
    ma_tt_data = serializers.SerializerMethodField()
    ma_nk_data = serializers.SerializerMethodField()
    so_ct_data = serializers.SerializerMethodField()
    so_ct2_data = serializers.SerializerMethodField()
    so_px_data = serializers.SerializerMethodField()
    ma_nt_data = serializers.SerializerMethodField()
    ma_dc_data = serializers.SerializerMethodField()
    ma_ptvc_data = serializers.SerializerMethodField()
    ma_ptgh_data = serializers.SerializerMethodField()
    tknh_data = serializers.SerializerMethodField()
    ma_kh9_data = serializers.SerializerMethodField()
    unit_id_data = serializers.SerializerMethodField()
    chi_tiet = ChiTietHoaDonBanHangSerializer(many=True, read_only=True)
    thong_tin_thanh_toan = ThongTinThanhToanHoaDonBanHangSerializer(
        many=True, read_only=True
    )

    class Meta:
        model = HoaDonBanHangModel
        fields = "__all__"
        read_only_fields = ["uuid", "created", "updated", "entity_model"]

    def get_ma_kh_data(self, obj):
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_ma_nvbh_data(self, obj):
        if obj.ma_nvbh:
            return NhanVienModelSerializer(obj.ma_nvbh).data
        return None

    def get_tk_data(self, obj):
        if obj.tk:
            return AccountModelSerializer(obj.tk).data
        return None

    def get_ma_tt_data(self, obj):
        if obj.ma_tt:
            return {
                "uuid": obj.ma_tt.uuid,
                "ma_tt": obj.ma_tt.ma_tt,
                "ten_tt": obj.ma_tt.ten_tt,
            }
        return None

    def get_ma_nk_data(self, obj):
        if obj.ma_nk:
            return {
                "uuid": obj.ma_nk.uuid,
                "ma_nk": obj.ma_nk.ma_nk,
                "ten_nk": obj.ma_nk.ten_nk,
            }
        return None

    def get_so_ct_data(self, obj):
        if obj.so_ct:
            return ChungTuSerializer(obj.so_ct).data
        return None

    def get_so_ct2_data(self, obj):
        if obj.so_ct2:
            return ChungTuSerializer(obj.so_ct2).data
        return None

    def get_so_px_data(self, obj):
        if obj.so_px:
            return ChungTuSerializer(obj.so_px).data
        return None

    def get_ma_nt_data(self, obj):
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_ma_dc_data(self, obj):
        if obj.ma_dc:
            return DiaChiSerializer(obj.ma_dc).data
        return None

    def get_ma_ptvc_data(self, obj):
        if obj.ma_ptvc:
            return PhuongTienVanChuyenModelSerializer(obj.ma_ptvc).data
        return None

    def get_ma_ptgh_data(self, obj):
        if obj.ma_ptgh:
            return PhuongTienGiaoHangModelSerializer(obj.ma_ptgh).data
        return None

    def get_tknh_data(self, obj):
        if obj.tknh:
            return BankAccountModelSerializer(obj.tknh).data
        return None

    def get_ma_kh9_data(self, obj):
        if obj.ma_kh9:
            return CustomerModelSerializer(obj.ma_kh9).data
        return None

    def get_unit_id_data(self, obj):
        if obj.unit_id:
            return EntityUnitModelSimpleSerializer(obj.unit_id).data
        return None

    def create(self, validated_data):
        """
        Create a new HoaDonBanHangModel instance.
        """
        chi_tiet_data = self.context.get("chi_tiet", [])
        thanh_toan_data = self.context.get("thong_tin_thanh_toan", [])
        entity_model = self.context.get("entity_model")

        service = HoaDonBanHangService()
        hoa_don = service.create_invoice(
            entity_model=entity_model,
            hoa_don_data=validated_data,
            chi_tiet_data=chi_tiet_data,
            thanh_toan_data=thanh_toan_data,
        )
        return hoa_don

    def update(self, instance, validated_data):
        """
        Update an existing HoaDonBanHangModel instance.
        """
        chi_tiet_data = self.context.get("chi_tiet", [])
        thanh_toan_data = self.context.get("thong_tin_thanh_toan", [])

        service = HoaDonBanHangService()
        hoa_don = service.update_invoice(
            uuid=instance.uuid,
            hoa_don_data=validated_data,
            chi_tiet_data=chi_tiet_data,
            thanh_toan_data=thanh_toan_data,
        )
        return hoa_don
