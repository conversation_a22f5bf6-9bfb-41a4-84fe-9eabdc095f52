"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL configuration for NgoaiTe (Currency) API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.danh_muc.ke_toan.ngoai_te import NgoaiTeViewSet

# Main router for NgoaiTe
router = DefaultRouter()
router.register('', NgoaiTeViewSet, basename='ngoai-te')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
