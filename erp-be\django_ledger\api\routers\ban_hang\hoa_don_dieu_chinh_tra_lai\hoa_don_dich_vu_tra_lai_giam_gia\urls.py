"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Hoa Don Dich Vu Tra Lai Giam Gia (Service Invoice Return/Discount) module.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dich_vu_tra_lai_giam_gia import (
    HoaDonDichVuTraLaiGiamGiaViewSet,
    ChiTietHoaDonDichVuTraLaiGiamGiaViewSet
)

# Main router for HoaDonDichVuTraLaiGiamGia
router = DefaultRouter()
router.register('', HoaDonDichVuTraLaiGiamGiaViewSet, basename='hoa-don-dich-vu-tra-lai-giam-gia')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
    
    # Nested routes for hoa-don-dich-vu-tra-lai-giam-gia
    path('hoa-don-dich-vu-tra-lai-giam-gia/<uuid:hoa_don_uuid>/', include([
        # Chi tiet hoa don dich vu tra lai giam gia routes
        path('chi-tiet/', ChiTietHoaDonDichVuTraLaiGiamGiaViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-tiet-hoa-don-dich-vu-tra-lai-giam-gia-list'),

        path('chi-tiet/<uuid:uuid>/', ChiTietHoaDonDichVuTraLaiGiamGiaViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-tiet-hoa-don-dich-vu-tra-lai-giam-gia-detail'),
    ])),
]
