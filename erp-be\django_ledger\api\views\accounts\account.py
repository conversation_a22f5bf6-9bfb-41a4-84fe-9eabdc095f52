"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Account view implementation.
"""

from django_ledger.api.permissions import IsEntityAdminOrManager
from django_ledger.api.serializers.accounts.account import AccountModelSerializer
from django_ledger.api.views.common import ERPPagination
from django_ledger.services.accounts.account import AccountService
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response


class AccountModelViewSet(viewsets.ModelViewSet):
    """
    A viewset for viewing and editing accounts.

    Query Parameters:
    - role: Filter accounts by role (e.g. asset_ca_cash, asset_ppe_build)
    - prefix: Filter accounts by code prefix (e.g. 1, 2, 3)
    - account_type: Filter accounts by type (e.g. fixed_assets, current_assets, liabilities)
    """

    permission_classes = [IsAuthenticated, IsEntityAdminOrManager]
    serializer_class = AccountModelSerializer
    lookup_field = "uuid"
    pagination_class = ERPPagination

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.service = AccountService()

    def get_queryset(self):
        """
        Get QuerySet of AccountModel instances for the entity and chart of accounts

        Returns
        -------
        QuerySet
            QuerySet of AccountModel instances for the entity and chart of accounts
        """
        entity_slug = self.kwargs.get("entity_slug")
        coa_slug = self.kwargs.get("slug")  # Lấy slug từ URL thay vì coa_slug
        user_model = self.request.user

        # Get filter parameters from query params
        role = self.request.query_params.get("role", None)
        prefix = self.request.query_params.get("prefix", None)
        account_type = self.request.query_params.get("account_type", None)

        # Get queryset from service
        return self.service.list(
            entity_slug=entity_slug,
            user_model=user_model,
            coa_slug=coa_slug,
            role=role,
            prefix=prefix,
            account_type=account_type,
        )

    def list(self, request, *args, **kwargs):
        """
        Override list method to provide sorted accounts by code
        and exclude root accounts (accounts with codes like ********, ********, etc.)
        with pagination in the format:
        {
            "count": 0,
            "next": null,
            "previous": null,
            "results": []
        }
        """
        queryset = self.filter_queryset(self.get_queryset())

        # Check if pagination is required
        page_size = request.query_params.get("page_size", None)
        page = request.query_params.get("page", None)

        if page_size is not None or page is not None:
            # Use pagination
            page = self.paginate_queryset(queryset)
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        else:
            # No pagination, but still return in paginated format
            serializer = self.get_serializer(queryset, many=True)
            return Response(
                {
                    "count": len(serializer.data),
                    "next": None,
                    "previous": None,
                    "results": serializer.data,
                }
            )

    def retrieve(self, request, *args, **kwargs):
        """
        Override retrieve method to handle root accounts
        If the account is a root account, return 404
        """
        entity_slug = self.kwargs.get("entity_slug")
        uuid = self.kwargs.get("uuid")
        coa_slug = self.kwargs.get("slug")  # Lấy slug từ URL thay vì coa_slug

        # Get the account from service
        instance = self.service.get_by_id(
            entity_slug=entity_slug, uuid=uuid, coa_slug=coa_slug
        )

        if not instance:
            return Response(
                {"error": "Account not found"}, status=status.HTTP_404_NOT_FOUND
            )

        # If the account is a root account, return 404
        if instance.is_root_account():
            return Response(
                {"error": "Root accounts are not accessible"},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        """
        Create a new account
        """
        entity_slug = self.kwargs.get("entity_slug")
        coa_slug = self.kwargs.get("slug")  # Lấy slug từ URL thay vì coa_slug

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Get validated data
        data = serializer.validated_data

        # Remove entity_model from data if it exists
        if "entity_model" in data:
            data.pop("entity_model")

        try:
            # Create the account using the service
            instance = self.service.create(
                entity_slug=entity_slug, coa_slug=coa_slug, data=data
            )

            # Return the created account
            serializer = self.get_serializer(instance)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def update(self, request, *args, **kwargs):
        """
        Update an existing account
        """
        entity_slug = self.kwargs.get("entity_slug")
        uuid = self.kwargs.get("uuid")
        coa_slug = self.kwargs.get("slug")  # Lấy slug từ URL thay vì coa_slug

        # Get the account from service
        instance = self.service.get_by_id(
            entity_slug=entity_slug, uuid=uuid, coa_slug=coa_slug
        )

        if not instance:
            return Response(
                {"error": "Account not found"}, status=status.HTTP_404_NOT_FOUND
            )

        serializer = self.get_serializer(
            instance, data=request.data, partial=kwargs.get("partial", False)
        )
        serializer.is_valid(raise_exception=True)

        # Get validated data
        data = serializer.validated_data

        # Remove entity_model from data if it exists
        if "entity_model" in data:
            data.pop("entity_model")

        try:
            # Update the account using the service
            updated_instance = self.service.update(
                entity_slug=entity_slug, uuid=uuid, data=data, coa_slug=coa_slug
            )

            # Return the updated account
            serializer = self.get_serializer(updated_instance)
            return Response(serializer.data)
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def destroy(self, request, *args, **kwargs):
        """
        Delete an account
        """
        entity_slug = self.kwargs.get("entity_slug")
        uuid = self.kwargs.get("uuid")
        coa_slug = self.kwargs.get("slug")  # Lấy slug từ URL thay vì coa_slug

        try:
            # Delete the account using the service
            success = self.service.delete(
                entity_slug=entity_slug, uuid=uuid, coa_slug=coa_slug
            )

            if success:
                return Response(status=status.HTTP_204_NO_CONTENT)
            else:
                return Response(
                    {"error": "Account not found"}, status=status.HTTP_404_NOT_FOUND
                )
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    # Specialized endpoints for filtering accounts by type
    @action(detail=False, methods=["get"])
    def by_prefix(self, request, *args, **kwargs):
        """
        Filter accounts by prefix
        """
        prefix = request.query_params.get("prefix", None)
        if not prefix:
            return Response({"error": "Prefix parameter is required"}, status=400)

        # Set the prefix in the query params and call list
        request.query_params._mutable = True
        request.query_params["prefix"] = prefix
        request.query_params._mutable = False
        return self.list(request, *args, **kwargs)

    @action(detail=False, methods=["get"])
    def fixed_assets(self, request, *args, **kwargs):
        """
        Get fixed assets accounts (2xx)
        """
        request.query_params._mutable = True
        request.query_params["account_type"] = "fixed_assets"
        request.query_params._mutable = False
        return self.list(request, *args, **kwargs)

    @action(detail=False, methods=["get"])
    def current_assets(self, request, *args, **kwargs):
        """
        Get current assets accounts (1xx)
        """
        request.query_params._mutable = True
        request.query_params["account_type"] = "current_assets"
        request.query_params._mutable = False
        return self.list(request, *args, **kwargs)

    @action(detail=False, methods=["get"])
    def liabilities(self, request, *args, **kwargs):
        """
        Get liabilities accounts (3xx)
        """
        request.query_params._mutable = True
        request.query_params["account_type"] = "liabilities"
        request.query_params._mutable = False
        return self.list(request, *args, **kwargs)

    @action(detail=False, methods=["get"])
    def equity(self, request, *args, **kwargs):
        """
        Get equity accounts (4xx)
        """
        request.query_params._mutable = True
        request.query_params["account_type"] = "equity"
        request.query_params._mutable = False
        return self.list(request, *args, **kwargs)

    @action(detail=False, methods=["get"])
    def revenue(self, request, *args, **kwargs):
        """
        Get revenue accounts (5xx)
        """
        request.query_params._mutable = True
        request.query_params["account_type"] = "revenue"
        request.query_params._mutable = False
        return self.list(request, *args, **kwargs)

    @action(detail=False, methods=["get"])
    def expenses(self, request, *args, **kwargs):
        """
        Get expenses accounts (7xx, 8xx)
        """
        request.query_params._mutable = True
        request.query_params["account_type"] = "expenses"
        request.query_params._mutable = False
        return self.list(request, *args, **kwargs)
