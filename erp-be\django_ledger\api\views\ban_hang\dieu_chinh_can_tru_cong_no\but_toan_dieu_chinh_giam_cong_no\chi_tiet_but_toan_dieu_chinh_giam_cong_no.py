"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Views for ChiTietButToanDieuChinhGiamCongNo model.
"""

from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import permissions, status
from rest_framework.response import Response

from django_ledger.api.serializers.ban_hang.dieu_chinh_can_tru_cong_no.but_toan_dieu_chinh_giam_cong_no import ChiTietButToanDieuChinhGiamCongNoSerializer
from django_ledger.api.views.common import ERPPagination
from django_ledger.models.ban_hang.dieu_chinh_can_tru_cong_no.but_toan_dieu_chinh_giam_cong_no import ChiTietButToanDieuChinhGiamCongNoModel
from django_ledger.services.ban_hang.dieu_chinh_can_tru_cong_no.but_toan_dieu_chinh_giam_cong_no import ChiTietButToanD<PERSON><PERSON>hinhGiamCongNoService
from django_ledger.api.viewsets import EntityRelatedViewSet


@extend_schema_view(
    list=extend_schema(tags=["ChiTietButToanDieuChinhGiamCongNo"]),
    create=extend_schema(tags=["ChiTietButToanDieuChinhGiamCongNo"]),
    retrieve=extend_schema(tags=["ChiTietButToanDieuChinhGiamCongNo"]),
    update=extend_schema(tags=["ChiTietButToanDieuChinhGiamCongNo"]),
    partial_update=extend_schema(tags=["ChiTietButToanDieuChinhGiamCongNo"]),
    destroy=extend_schema(tags=["ChiTietButToanDieuChinhGiamCongNo"])
)
class ChiTietButToanDieuChinhGiamCongNoViewSet(EntityRelatedViewSet):
    """
    A ViewSet for ChiTietButToanDieuChinhGiamCongNo model.
    """
    queryset = ChiTietButToanDieuChinhGiamCongNoModel.objects.all()
    serializer_class = ChiTietButToanDieuChinhGiamCongNoSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination
    lookup_field = 'uuid'
    http_method_names = ['get', 'post', 'put', 'delete']

    def __init__(self, *args, **kwargs):
        """
        Initialize the ViewSet.

        Parameters
        ----------
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments
        """
        super().__init__(*args, **kwargs)
        self.service = ChiTietButToanDieuChinhGiamCongNoService()

    def get_queryset(self):
        """
        Get the queryset for the viewset.

        Returns
        -------
        QuerySet
            The queryset for the viewset.
        """
        but_toan_uuid = self.kwargs.get('but_toan_dieu_chinh_giam_cong_no_uuid')
        if but_toan_uuid:
            return self.service.get_for_but_toan(but_toan_uuid=but_toan_uuid)
        return self.queryset

    def list(self, request, *args, **kwargs):
        """
        List all ChiTietButToanDieuChinhGiamCongNo instances for a specific ButToanDieuChinhGiamCongNo.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get the parent UUID from the URL
        but_toan_uuid = self.kwargs.get('but_toan_dieu_chinh_giam_cong_no_uuid')

        # Get data from service
        instances = self.service.get_for_but_toan(but_toan_uuid=but_toan_uuid)

        # Always paginate the queryset
        page = self.paginate_queryset(instances)
        serializer = self.get_serializer(page, many=True)
        return self.get_paginated_response(serializer.data)

    def retrieve(self, request, *args, **kwargs):
        """
        Retrieve a specific ChiTietButToanDieuChinhGiamCongNo instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get the instance
        instance = self.service.get_by_uuid(uuid=kwargs['uuid'])

        if not instance:
            return Response(
                {'detail': 'Not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Serialize data
        serializer = self.get_serializer(instance)

        # Return response
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        """
        Create a new ChiTietButToanDieuChinhGiamCongNo instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get parent_uuid
        but_toan_uuid = self.kwargs.get('but_toan_dieu_chinh_giam_cong_no_uuid')

        # Validate data
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Create instance
        instance = self.service.create(
            but_toan_uuid=but_toan_uuid,
            **serializer.validated_data
        )

        # Serialize response
        response_serializer = self.get_serializer(instance)

        # Return response
        return Response(
            response_serializer.data,
            status=status.HTTP_201_CREATED
        )

    def update(self, request, *args, **kwargs):
        """
        Update an existing ChiTietButToanDieuChinhGiamCongNo instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Validate data
        serializer = self.get_serializer(data=request.data, partial=kwargs.get('partial', False))
        serializer.is_valid(raise_exception=True)

        try:
            # Update instance
            instance = self.service.update(
                uuid=kwargs['uuid'],
                **serializer.validated_data
            )
        except Exception:
            return Response(
                {'detail': 'Not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

        if not instance:
            return Response(
                {'detail': 'Not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Serialize response
        response_serializer = self.get_serializer(instance)

        # Return response
        return Response(response_serializer.data)

    def partial_update(self, request, *args, **kwargs):
        """
        Partially update an existing ChiTietButToanDieuChinhGiamCongNo instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        """
        Delete an existing ChiTietButToanDieuChinhGiamCongNo instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        try:
            # Delete instance
            success = self.service.delete(uuid=kwargs['uuid'])
            if not success:
                return Response(
                    {'detail': 'Not found.'},
                    status=status.HTTP_404_NOT_FOUND
                )
        except Exception:
            return Response(
                {'detail': 'Not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Return response
        return Response(status=status.HTTP_204_NO_CONTENT)
