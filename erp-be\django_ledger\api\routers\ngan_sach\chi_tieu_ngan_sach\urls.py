"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for ChiTieuNganSach API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.ngan_sach.chi_tieu_ngan_sach import (
    ChiTieuNganSachViewSet,
    ChiTietChiTieuNganSachViewSet
)

# Main router for ChiTieuNganSach
router = DefaultRouter()
router.register('', ChiTieuNganSachViewSet, basename='chi-tieu-ngan-sach')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),

    # Nested routes for chi-tieu-ngan-sach
    path('<uuid:chi_tieu_uuid>/', include([
        # Chi tiet chi tieu ngan sach routes
        path('chi-tiet/', ChiTietChiTieuNganSachViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-tiet-chi-tieu-ngan-sach-list'),

        path('chi-tiet/<uuid:uuid>/', ChiTietChiTieuNganSachViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-tiet-chi-tieu-ngan-sach-detail'),
    ])),
]
