"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

A GiaMua (Purchase Price) model represents the pricing information for products from specific vendors.
This allows tracking different prices from different suppliers and can include volume pricing.
"""

from uuid import uuid4

from django.db import models
from django.db.models import Q, QuerySet, Manager
from django.utils.translation import gettext_lazy as _

from django_ledger.models.mixins import CreateUpdateMixIn
from django_ledger.models.utils import lazy_loader


class GiaMuaModelQueryset(QuerySet):
    """
    A custom defined QuerySet for the GiaMuaModel.
    """

    def active(self) -> QuerySet:
        """
        Active purchase prices that are currently valid.

        Returns
        -------
        GiaMuaModelQueryset
            A QuerySet of active Purchase Prices.
        """
        return self.filter(trang_thai__gt=0)

    def for_product(self, item_model_id) -> QuerySet:
        """
        Purchase prices for a specific product.

        Returns
        -------
        GiaMuaModelQueryset
            A QuerySet of Purchase Prices for a specific product.
        """
        return self.filter(ma_vat_tu=item_model_id)

    def for_vendor(self, vendor_model_id) -> QuerySet:
        """
        Purchase prices from a specific vendor.

        Returns
        -------
        GiaMuaModelQueryset
            A QuerySet of Purchase Prices from a specific vendor.
        """
        return self.filter(nha_cung_cap=vendor_model_id)

    def for_entity(self, entity_slug: str, user_model):
        """
        Fetches all purchase prices for a specific EntityModel.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug to filter by.
        user_model
            The request user to check for permissions.

        Returns
        -------
        GiaMuaModelQueryset
            A filtered QuerySet of purchase prices for the specified entity.
        """
        from django_ledger.models import EntityModel

        # If entity_slug is an EntityModel instance, use it directly
        if isinstance(entity_slug, EntityModel):
            entity_model = entity_slug
            return self.filter(
                Q(entity_model=entity_model) &
                (
                    Q(entity_model__admin=user_model) |
                    Q(entity_model__managers__in=[user_model])
                )
            )

        # Otherwise, filter by slug
        return self.filter(
            Q(entity_model__slug__exact=entity_slug) &
            (
                Q(entity_model__admin=user_model) |
                Q(entity_model__managers__in=[user_model])
            )
        )


class GiaMuaModelManager(Manager):
    """
    A custom defined GiaMuaModelManager that will act as an interface to handling the DB queries to the
    GiaMuaModel.
    """

    def for_user(self, user_model):
        """
        Fetches a QuerySet of GiaMuaModels that the UserModel has access to.
        May include GiaMuaModels from multiple Entities.

        Parameters
        __________
        user_model
            Logged in and authenticated django UserModel instance.
        """
        qs = self.get_queryset()
        if user_model.is_superuser:
            return qs
        return qs.filter(
            Q(entity_model__admin=user_model) |
            Q(entity_model__managers__in=[user_model])
        )

    def for_entity(self, entity_slug, user_model) -> GiaMuaModelQueryset:
        """
        Fetches a QuerySet of GiaMuaModel for a specific EntityModel & UserModel.
        May pass an instance of EntityModel or a String representing the EntityModel slug.

        Parameters
        __________
        entity_slug: str or EntityModel
            The entity slug or EntityModel used for filtering the QuerySet.
        user_model
            Logged in and authenticated django UserModel instance.


        Returns
        -------
        GiaMuaModelQueryset
            A filtered GiaMuaModel QuerySet.
        """
        qs = self.for_user(user_model)

        if isinstance(entity_slug, lazy_loader.get_entity_model()):
            return qs.filter(
                Q(entity_model=entity_slug)
            )
        return qs.filter(
            Q(entity_model__slug__exact=entity_slug)
        )

    def get_price(self, item_model_id, vendor_model_id=None, unit_of_measure_id=None, quantity=1):
        """
        Gets the applicable purchase price for a product from a vendor with a specific quantity.

        Parameters
        ----------
        item_model_id: UUID
            The product ID
        vendor_model_id: UUID, optional
            The vendor ID. If None, returns the best price from any vendor.
        unit_of_measure_id: UUID, optional
            The unit of measure ID. If None, uses the default unit.
        quantity: int, optional
            The quantity to be purchased. Defaults to 1.

        Returns
        -------
        GiaMuaModel
            The best applicable price model
        """
        qs = self.get_queryset().filter(
            ma_vat_tu=item_model_id,
            trang_thai__gt=0,
        ).filter(
            so_luong_tu__lte=quantity
        ).order_by('-so_luong_tu')  # Start with the highest threshold

        if vendor_model_id:
            qs = qs.filter(nha_cung_cap=vendor_model_id)

        if unit_of_measure_id:
            qs = qs.filter(don_vi_tinh=unit_of_measure_id)

        return qs.first()  # Return the first (best) price


class GiaMuaModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the GiaMuaModel database will inherit from.
    The GiaMuaModel inherits functionality from the following MixIns:

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    __________
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().

    ma_vat_tu: VatTuModel
        The product this price applies to.

    don_vi_tinh: DonViTinhModel
        The unit of measure for this price.

    ngay_hieu_luc: datetime
        The date from which this price is effective.

    nha_cung_cap: CustomerModel
        The vendor offering this price.

    ngoai_te: NgoaiTeModel
        The currency for this price.

    so_luong_tu: int
        The minimum quantity for this price to apply (for volume discounts).

    gia_mua: Decimal
        The purchase price.

    trang_thai: int
        The status of the purchase price (active, inactive, etc).
    """

    uuid = models.UUIDField(default=uuid4, editable=False, primary_key=True)
    entity_model = models.ForeignKey('django_ledger.EntityModel',
                                     editable=False,
                                     on_delete=models.CASCADE,
                                     verbose_name=_('Entity Model'), default=None)
    ma_vat_tu = models.ForeignKey('django_ledger.VatTuModel',
                                 on_delete=models.CASCADE,
                                 verbose_name=_('Product'),
                                 related_name='purchase_prices')
    don_vi_tinh = models.ForeignKey('django_ledger.DonViTinhModel',
                                   on_delete=models.CASCADE,
                                   verbose_name=_('Unit of Measure'),
                                   related_name='product_prices')
    ngay_hieu_luc = models.DateTimeField(verbose_name=_('Effective Date'))
    nha_cung_cap = models.ForeignKey('django_ledger.CustomerModel',
                                    on_delete=models.CASCADE,
                                    verbose_name=_('Vendor'),
                                    related_name='product_prices',
                                    null=True,
                                    blank=True)
    ngoai_te = models.ForeignKey('django_ledger.NgoaiTeModel',
                                on_delete=models.CASCADE,
                                verbose_name=_('Currency'),
                                related_name='product_prices')
    so_luong_tu = models.IntegerField(default=1, verbose_name=_('Minimum Quantity'))
    gia_mua = models.DecimalField(max_digits=15, decimal_places=2, verbose_name=_('Purchase Price'))
    trang_thai = models.IntegerField(verbose_name=_('Status'))

    objects = GiaMuaModelManager.from_queryset(GiaMuaModelQueryset)()

    class Meta:
        abstract = True
        verbose_name = _('Purchase Price')
        verbose_name_plural = _('Purchase Prices')
        indexes = [
            models.Index(fields=['entity_model']),
            models.Index(fields=['ma_vat_tu']),
            models.Index(fields=['nha_cung_cap']),
            models.Index(fields=['trang_thai']),
        ]
        unique_together = [
            ('entity_model', 'ma_vat_tu', 'don_vi_tinh', 'nha_cung_cap', 'so_luong_tu')
        ]

    def __str__(self):
        return f'{self.ma_vat_tu} - {self.nha_cung_cap} - {self.gia_mua} {self.ngoai_te} ({self.so_luong_tu}+)'

    def is_applicable(self, quantity):
        """
        Checks if this price is applicable for the given quantity

        Parameters
        ----------
        quantity: int
            The quantity to check against

        Returns
        -------
        bool
            True if this price applies to the quantity
        """
        return quantity >= self.so_luong_tu and self.trang_thai > 0

    def get_total_price(self, quantity):
        """
        Calculates the total price for a given quantity

        Parameters
        ----------
        quantity: int or Decimal
            The quantity to calculate for

        Returns
        -------
        Decimal
            The total price
        """
        if self.is_applicable(quantity):
            return self.gia_mua * quantity
        return None


class GiaMuaModel(GiaMuaModelAbstract):
    """
    Base Purchase Price Model Implementation
    """

    class Meta(GiaMuaModelAbstract.Meta):
        # swappable ='DJANGO_LEDGER_GIAMUA_MODEL'
        abstract = False
