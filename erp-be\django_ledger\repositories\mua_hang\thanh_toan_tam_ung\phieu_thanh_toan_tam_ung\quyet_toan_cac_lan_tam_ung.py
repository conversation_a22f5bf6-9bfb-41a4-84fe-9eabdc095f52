"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

QuyetToanCacLanTamUng (Advance Payment Settlement) Repository
"""

from django.db.models import QuerySet
from django.utils.translation import gettext_lazy as _

from django_ledger.models.mua_hang.thanh_toan_tam_ung import QuyetToanCacLanTamUngModel, PhieuThanhToanTamUngModel
from django_ledger.repositories.base import BaseRepository


class QuyetToanCacLanTamUngRepository(BaseRepository):
    """
    A repository class for the QuyetToanCacLanTamUngModel.
    """

    def __init__(self):
        self.model_class = QuyetToanCacLanTamUngModel

    def get_by_id(self, entity_slug: str, uuid: str, user_model) -> QuyetToanCacLanTamUngModel:
        """
        Get a QuyetToanCacLanTamUngModel by its UUID.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug to filter by.
        uuid: str
            The UUID of the QuyetToanCacLanTamUngModel to retrieve.
        user_model
            The authenticated Django UserModel making the request.

        Returns
        -------
        QuyetToanCacLanTamUngModel
            The requested QuyetToanCacLanTamUngModel instance.
        """
        return self.model_class.objects.get(uuid__exact=uuid)

    def list(self, entity_slug: str, user_model, phieu_thanh_toan_uuid=None, **kwargs) -> QuerySet:
        """
        Get a QuerySet of QuyetToanCacLanTamUngModel instances.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug to filter by.
        user_model
            The authenticated Django UserModel making the request.
        phieu_thanh_toan_uuid: str
            Optional UUID of the PhieuThanhToanTamUngModel to filter by.
        kwargs
            Additional filtering parameters.

        Returns
        -------
        QuerySet
            A QuerySet of QuyetToanCacLanTamUngModel instances.
        """
        qs = self.model_class.objects.all()
        if phieu_thanh_toan_uuid:
            qs = qs.filter(phieu_thanh_toan__uuid__exact=phieu_thanh_toan_uuid)
        return qs

    def create(self, entity_slug: str, user_model, phieu_thanh_toan_uuid=None, **kwargs) -> QuyetToanCacLanTamUngModel:
        """
        Create a new QuyetToanCacLanTamUngModel instance.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug to associate with the new instance.
        user_model
            The authenticated Django UserModel making the request.
        phieu_thanh_toan_uuid: str
            Optional UUID of the PhieuThanhToanTamUngModel to associate with the new instance.
        kwargs
            Additional parameters for the new instance.

        Returns
        -------
        QuyetToanCacLanTamUngModel
            The newly created QuyetToanCacLanTamUngModel instance.
        """
        if phieu_thanh_toan_uuid:
            phieu_thanh_toan = PhieuThanhToanTamUngModel.objects.get(uuid__exact=phieu_thanh_toan_uuid)
            kwargs['phieu_thanh_toan'] = phieu_thanh_toan
        
        # Convert UUIDs to model instances
        self.convert_uuids_to_model_instances(kwargs)
        
        instance = self.model_class(**kwargs)
        instance.save()
        return instance

    def update(self, entity_slug: str, user_model, instance: QuyetToanCacLanTamUngModel, **kwargs) -> QuyetToanCacLanTamUngModel:
        """
        Update an existing QuyetToanCacLanTamUngModel instance.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug to filter by.
        user_model
            The authenticated Django UserModel making the request.
        instance: QuyetToanCacLanTamUngModel
            The QuyetToanCacLanTamUngModel instance to update.
        kwargs
            Additional parameters to update.

        Returns
        -------
        QuyetToanCacLanTamUngModel
            The updated QuyetToanCacLanTamUngModel instance.
        """
        # Convert UUIDs to model instances
        self.convert_uuids_to_model_instances(kwargs)
        
        for key, value in kwargs.items():
            setattr(instance, key, value)
        instance.save()
        return instance

    def delete(self, entity_slug: str, user_model, instance: QuyetToanCacLanTamUngModel) -> bool:
        """
        Delete a QuyetToanCacLanTamUngModel instance.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug to filter by.
        user_model
            The authenticated Django UserModel making the request.
        instance: QuyetToanCacLanTamUngModel
            The QuyetToanCacLanTamUngModel instance to delete.

        Returns
        -------
        bool
            True if the instance was deleted, False otherwise.
        """
        instance.delete()
        return True

    def convert_uuids_to_model_instances(self, data: dict) -> None:
        """
        Convert UUID strings to model instances in the data dictionary.

        Parameters
        ----------
        data: dict
            The data dictionary to process.
        """
        from django_ledger.models import CustomerModel, PhieuThanhToanTamUngModel
        
        if 'phieu_thanh_toan' in data and isinstance(data['phieu_thanh_toan'], str):
            try:
                data['phieu_thanh_toan'] = PhieuThanhToanTamUngModel.objects.get(uuid__exact=data['phieu_thanh_toan'])
            except PhieuThanhToanTamUngModel.DoesNotExist:
                pass
                
        if 'ma_kh' in data and isinstance(data['ma_kh'], str):
            try:
                data['ma_kh'] = CustomerModel.objects.get(uuid__exact=data['ma_kh'])
            except CustomerModel.DoesNotExist:
                pass
