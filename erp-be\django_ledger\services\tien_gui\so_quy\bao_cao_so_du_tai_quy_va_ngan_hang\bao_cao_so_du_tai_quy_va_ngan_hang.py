"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for Bao Cao So Du <PERSON>a <PERSON> (Cash and Bank Balance Report).
"""

from typing import Dict, List, Any
from decimal import Decimal
from datetime import datetime, date

from django.db.models import Sum
from django_ledger.io.roles import ASSET_CA_CASH
from django_ledger.models import TransactionModel, BankAccountModel
from django_ledger.services.base import BaseService


class BaoCaoSoDuTaiQuyVaNganHangService(BaseService):
    """
    Service class for handling Cash and Bank Balance Report business logic.
    This service orchestrates calls to other services to generate cash and bank balance reports.
    """

    def __init__(self):
        """
        Initialize the service with required services.
        """
        super().__init__()
        # Initialize services lazily to avoid circular imports
        self._account_service = None
        self._bank_account_service = None
        self._transaction_service = None
        self._ngan_hang_service = None

    @property
    def account_service(self):
        """Lazy initialization of account_service."""
        if self._account_service is None:
            from django_ledger.services.account import AccountService
            self._account_service = AccountService()
        return self._account_service

    @property
    def bank_account_service(self):
        """Lazy initialization of bank_account_service."""
        if self._bank_account_service is None:
            from django_ledger.services.bank_account import BankAccountService
            self._bank_account_service = BankAccountService()
        return self._bank_account_service

    @property
    def transaction_service(self):
        """Lazy initialization of transaction_service."""
        if self._transaction_service is None:
            from django_ledger.services.transactions import TransactionService
            self._transaction_service = TransactionService()
        return self._transaction_service

    @property
    def ngan_hang_service(self):
        """Lazy initialization of ngan_hang_service."""
        if self._ngan_hang_service is None:
            from django_ledger.services.ngan_hang import NganHangService
            self._ngan_hang_service = NganHangService()
        return self._ngan_hang_service

    def generate_report(self, entity_slug: str, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate cash and bank balance report with filtering.

        Parameters
        ----------
        entity_slug : str
            The entity slug
        filters : Dict[str, Any]
            Filter parameters from POST body data

        Returns
        -------
        List[Dict[str, Any]]
            List of cash and bank balance report data
        """
        try:
            # Extract filter parameters
            tk = filters.get('tk', '')  # Account code filter
            ngay_ct2 = filters.get('ngay_ct2', '')  # End date filter
            ma_unit = filters.get('ma_unit', '')  # Unit filter

            # Convert date string to date object if provided
            end_date = None
            if ngay_ct2:
                try:
                    end_date = datetime.strptime(ngay_ct2, '%Y%m%d').date()
                except ValueError:
                    end_date = date.today()
            else:
                end_date = date.today()

            # Generate real report data from database
            report_data = self._generate_real_data(entity_slug, tk, end_date, ma_unit)

            return report_data

        except Exception as e:
            import traceback
            print(f"ERROR in generate_report: {str(e)}")
            print(f"TRACEBACK: {traceback.format_exc()}")
            raise ValueError(f"Failed to generate report: {str(e)}")

    def _generate_real_data(self, entity_slug: str, tk_filter: str, end_date: date, ma_unit: str) -> List[Dict[str, Any]]:
        """
        Generate real report data from database queries.
        """
        try:
            # Get cash and bank accounts using account service
            accounts_query = self.account_service.list(
                entity_slug=entity_slug,
                role=ASSET_CA_CASH,  # Cash and bank accounts
                active=True
            )

            # Apply account code filter if provided
            if tk_filter:
                accounts_query = accounts_query.filter(code=tk_filter)

            # TODO: Implement unit filtering if needed
            # if ma_unit:
            #     accounts_query = accounts_query.filter(unit__ma_unit=ma_unit)

            # Optimize query with select_related to avoid N+1 problem
            accounts = accounts_query.select_related('coa_model', 'coa_model__entity').order_by('code')

            if not accounts.exists():
                print(f"WARNING: No cash/bank accounts found for entity {entity_slug}")
                return []

            report_data = []
            stt = 1

            for account in accounts:
                # Calculate balances for this account
                balance_data = self._calculate_account_balance(account.coa_model.entity, account, end_date)

                # Get bank account info if exists
                bank_info = self._get_bank_account_info_real(account)

                # Create report record matching cURL response fields
                # Fields: stt, tk, ct_yn, du_no, du_no_nt, du_co, du_co_nt, ten_tk, du
                closing_balance = float(balance_data['so_du_cuoi_ky'])

                record = {
                    'stt': stt,
                    'tk': account.code,
                    'ct_yn': 'Y' if bank_info.get('so_tai_khoan') else 'N',  # Y if bank account, N if cash
                    'du_no': closing_balance if closing_balance > 0 else 0.0,  # Debit balance
                    'du_no_nt': 0.0,  # Foreign currency debit balance (TODO: implement if needed)
                    'du_co': abs(closing_balance) if closing_balance < 0 else 0.0,  # Credit balance
                    'du_co_nt': 0.0,  # Foreign currency credit balance (TODO: implement if needed)
                    'ten_tk': account.name,
                    'du': closing_balance,  # Net balance
                }

                report_data.append(record)
                stt += 1

            return report_data

        except Exception as e:
            import traceback
            print(f"ERROR generating real report data: {str(e)}")
            print(f"TRACEBACK: {traceback.format_exc()}")
            # Return empty list if real data fails
            return []

    def _calculate_account_balance(self, entity, account, end_date: date) -> Dict[str, Decimal]:
        """
        Calculate real account balance from database transactions.
        """
        try:
            # Convert end_date to datetime for comparison
            end_datetime = datetime.combine(end_date, datetime.min.time())

            # Get all transactions for this account
            transactions = TransactionModel.objects.filter(
                account=account,
                journal_entry__ledger__entity=entity,
                journal_entry__posted=True,
                journal_entry__timestamp__lte=end_datetime
            )

            # Calculate opening balance (transactions before start of year)
            start_of_year = datetime(end_date.year, 1, 1)
            opening_transactions = transactions.filter(
                journal_entry__timestamp__lt=start_of_year
            )

            opening_debit = opening_transactions.filter(tx_type='debit').aggregate(
                total=Sum('amount')
            )['total'] or Decimal('0.00')

            opening_credit = opening_transactions.filter(tx_type='credit').aggregate(
                total=Sum('amount')
            )['total'] or Decimal('0.00')

            so_du_dau_ky = opening_debit - opening_credit

            # Calculate period transactions (from start of year to end_date)
            period_transactions = transactions.filter(
                journal_entry__timestamp__gte=start_of_year,
                journal_entry__timestamp__lte=end_datetime
            )

            phat_sinh_no = period_transactions.filter(tx_type='debit').aggregate(
                total=Sum('amount')
            )['total'] or Decimal('0.00')

            phat_sinh_co = period_transactions.filter(tx_type='credit').aggregate(
                total=Sum('amount')
            )['total'] or Decimal('0.00')

            # Calculate closing balance
            so_du_cuoi_ky = so_du_dau_ky + phat_sinh_no - phat_sinh_co

            return {
                'so_du_dau_ky': so_du_dau_ky,
                'phat_sinh_no': phat_sinh_no,
                'phat_sinh_co': phat_sinh_co,
                'so_du_cuoi_ky': so_du_cuoi_ky,
            }

        except Exception as e:
            print(f"ERROR calculating account balance for {account.code}: {str(e)}")
            # Return zero balances if calculation fails
            return {
                'so_du_dau_ky': Decimal('0.00'),
                'phat_sinh_no': Decimal('0.00'),
                'phat_sinh_co': Decimal('0.00'),
                'so_du_cuoi_ky': Decimal('0.00'),
            }

    def _get_bank_account_info_real(self, account) -> Dict[str, str]:
        """
        Get real bank account information from database.
        """
        try:
            # Find bank account linked to this account
            bank_account = BankAccountModel.objects.filter(
                account_model=account,
                active=True
            ).select_related('ma_ngan_hang').first()

            if bank_account and bank_account.ma_ngan_hang:
                return {
                    'ma_ngan_hang': bank_account.ma_ngan_hang.ma_ngan_hang or '',
                    'ten_ngan_hang': bank_account.ma_ngan_hang.ten_ngan_hang or '',
                    'so_tai_khoan': bank_account.ma_tai_khoan or '',
                }
            else:
                # No bank account linked (cash account)
                return {
                    'ma_ngan_hang': '',
                    'ten_ngan_hang': '',
                    'so_tai_khoan': '',
                }

        except Exception as e:
            print(f"ERROR getting bank account info for {account.code}: {str(e)}")
            return {
                'ma_ngan_hang': '',
                'ten_ngan_hang': '',
                'so_tai_khoan': '',
            }
