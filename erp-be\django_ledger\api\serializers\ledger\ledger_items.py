from django_ledger.models.ledger.ledger_items import LedgerItemModel
from rest_framework import serializers


class LedgerItemModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = LedgerItemModel
        fields = [
            "uuid",
            "don_vi",
            "chung_tu",
            "ngay_ctu",
            "customer",
            "account",
            "doi_ung_account",
            "ngoai_te",
            "ty_gia",
            "ps_no",
            "ps_co",
            "sua",
            "dien_giai",
            "bo_phan",
            "vu_viec",
            "contract",
            "khe_uoc",
            "phi",
            "chi_phi_khong_hop_le",
            "ma_chung_tu",
            "trang_thai",
            "phan_loai",
            "created",
            "updated",
        ]
        read_only_fields = ["uuid", "created", "updated"]


class LedgerItemModelCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = LedgerItemModel
        fields = [
            "don_vi",
            "chung_tu",
            "ngay_ctu",
            "customer",
            "account",
            "doi_ung_account",
            "ngoai_te",
            "ty_gia",
            "ps_no",
            "ps_co",
            "sua",
            "dien_giai",
            "bo_phan",
            "vu_viec",
            "contract",
            "khe_uoc",
            "phi",
            "chi_phi_khong_hop_le",
            "ma_chung_tu",
            "trang_thai",
            "phan_loai",
        ]


class LedgerItemModelUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = LedgerItemModel
        fields = [
            "ngay_ctu",
            "ty_gia",
            "ps_no",
            "ps_co",
            "sua",
            "dien_giai",
            "trang_thai",
            "phan_loai",
        ]
