"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Views for Bao Cao So Du Tai Quy Va Ngan Hang (Cash and Bank Balance Report) API.
"""

from django_ledger.api.serializers.tien_gui.so_quy.bao_cao_so_du_tai_quy_va_ngan_hang import (
    BaoCaoSoDuTaiQuyVaNganHangRequestSerializer,
    BaoCaoSoDuTaiQuyVaNganHangResponseSerializer,
)
from django_ledger.api.views.common import ERPPagination
from django_ledger.services.tien_gui.so_quy.bao_cao_so_du_tai_quy_va_ngan_hang import BaoCaoSoDuTaiQuyVaNganHangService
from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import status, viewsets, permissions
from rest_framework.response import Response


@extend_schema_view(
    list=extend_schema(
        summary="Get Cash and Bank Balance Report",
        description="Get cash and bank balance report (<PERSON><PERSON> Du Tai Quy Va Ngan Hang) with filtering and pagination via POST body data",
        request=BaoCaoSoDuTaiQuyVaNganHangRequestSerializer,
        responses={200: BaoCaoSoDuTaiQuyVaNganHangResponseSerializer(many=True)},
    )
)
class BaoCaoSoDuTaiQuyVaNganHangViewSet(viewsets.ViewSet):
    """
    ViewSet for Cash and Bank Balance Report (Bao Cao So Du Tai Quy Va Ngan Hang).

    Provides single endpoint for getting cash and bank balance reports with filtering via POST body data.
    """

    permission_classes = [
        permissions.IsAuthenticated
    ]
    pagination_class = ERPPagination

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.service = None  # Will be initialized when needed

    def get_report(self, request, entity_slug):
        """
        Generate cash and bank balance report with filtering via POST body data.

        Parameters
        ----------
        request : Request
            The request object containing POST body data for filtering
        entity_slug : str
            The entity slug

        Returns
        -------
        Response
            The response containing the report data
        """
        try:
            # Initialize service if not already done
            if self.service is None:
                self.service = BaoCaoSoDuTaiQuyVaNganHangService()

            # Validate request data
            request_serializer = BaoCaoSoDuTaiQuyVaNganHangRequestSerializer(data=request.data)
            if not request_serializer.is_valid():
                return Response(
                    {
                        "detail": "Invalid request data",
                        "errors": request_serializer.errors
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get validated data
            validated_data = request_serializer.validated_data

            # Generate report using service
            report_data = self.service.generate_report(
                entity_slug=entity_slug,
                filters=validated_data
            )

            # Serialize response data
            response_serializer = BaoCaoSoDuTaiQuyVaNganHangResponseSerializer(
                report_data, many=True
            )

            # Apply pagination
            paginator = self.pagination_class()
            paginated_data = paginator.paginate_queryset(
                response_serializer.data, request
            )

            return paginator.get_paginated_response(paginated_data)

        except Exception as e:
            return Response(
                {
                    "detail": f"Error generating cash and bank balance report: {str(e)}",
                    "error_type": type(e).__name__
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def list(self, request, entity_slug):
        """
        Handle GET requests by redirecting to POST method.
        This maintains compatibility with the existing API structure.
        """
        return Response(
            {
                "detail": "This endpoint requires POST method with filter parameters in request body",
                "example": {
                    "tk": "",
                    "ngay_ct2": "********",
                    "ma_unit": "",
                    "mau_bc": 20
                }
            },
            status=status.HTTP_405_METHOD_NOT_ALLOWED,
        )

    def create(self, request, entity_slug):
        """
        Handle POST requests for generating reports.
        """
        return self.get_report(request, entity_slug)
