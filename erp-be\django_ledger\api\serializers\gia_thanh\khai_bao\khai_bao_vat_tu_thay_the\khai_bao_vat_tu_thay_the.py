
from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models import KhaiBaoVatTuThayTheModel, EntityModel, EntityUnitModel, VatTuModel
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.unit import EntityUnitModelSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer


class KhaiBaoVatTuThayTheSerializer(serializers.ModelSerializer):
    """
    Serializer for KhaiBaoVatTuThayThe model.
    """
    # Read-only fields for related objects
    entity_model_data = serializers.SerializerMethodField(read_only=True)
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    ma_vt_data = serializers.SerializerMethodField(read_only=True)
    ma_vt0_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = KhaiBaoVatTuThayTheModel
        fields = [
            'uuid',
            'entity_model',
            'entity_model_data',
            'unit_id',
            'unit_id_data',
            'ky',
            'nam',
            'ma_vt',
            'ma_vt_data',
            'ma_vt0',
            'ma_vt0_data',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'entity_model_data',
            'unit_id_data',
            'ma_vt_data',
            'ma_vt0_data',
            'created',
            'updated'
        ]

    def get_entity_model_data(self, obj):
        """
        Get entity model data.
        """
        if obj.entity_model:
            return EntityModelSerializer(obj.entity_model).data
        return None

    def get_unit_id_data(self, obj):
        """
        Get unit data.
        """
        if obj.unit_id:
            return EntityUnitModelSerializer(obj.unit_id).data
        return None

    def get_ma_vt_data(self, obj):
        """
        Get material data.
        """
        if obj.ma_vt:
            return VatTuSerializer(obj.ma_vt).data
        return None

    def get_ma_vt0_data(self, obj):
        """
        Get original material data.
        """
        if obj.ma_vt0:
            return VatTuSerializer(obj.ma_vt0).data
        return None

    def validate(self, attrs):
        """
        Validate the serializer data.
        """
        # Check if this is an update or create operation
        is_update = self.instance is not None

        # Validate required fields for create operations
        if not is_update:
            required_fields = ['ky', 'nam']
            for field in required_fields:
                if field not in attrs:
                    raise serializers.ValidationError({
                        field: _('This field is required.')
                    })

        # Validate that ma_vt and ma_vt0 are different
        ma_vt = attrs.get('ma_vt', getattr(self.instance, 'ma_vt', None) if self.instance else None)
        ma_vt0 = attrs.get('ma_vt0', getattr(self.instance, 'ma_vt0', None) if self.instance else None)

        if ma_vt and ma_vt0 and ma_vt.uuid == ma_vt0.uuid:
            raise serializers.ValidationError({
                'ma_vt0': _('Original material must be different from replacement material.')
            })

        return attrs
