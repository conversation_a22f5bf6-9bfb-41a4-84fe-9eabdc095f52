"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Chi Tiet Hoa Don Ban Hang (Sales Invoice Detail) serializer implementation.
"""

from rest_framework import serializers

from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang import ChiTietHoaDonBanHangModel

# Import serializers for foreign key fields
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.warehouse import KhoHangModelSerializer
from django_ledger.api.serializers.lo import LoModelSerializer
from django_ledger.api.serializers.vi_tri_kho_hang import ViTriKhoHangModelSerializer
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.tien_do_thanh_toan import DotThanhToanModelSerializer
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import KheUocModelSerializer
from django_ledger.api.serializers.danh_muc import PhiSerializer
from django_ledger.api.serializers.chi_phi import ChiPhiModelSerializer


class ChiTietHoaDonBanHangSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietHoaDonBanHangModel.
    """
    ma_vt_data = serializers.SerializerMethodField()
    dvt_data = serializers.SerializerMethodField()
    ma_kho_data = serializers.SerializerMethodField()
    ma_lo_data = serializers.SerializerMethodField()
    ma_vi_tri_data = serializers.SerializerMethodField()
    tk_thue_co_data = serializers.SerializerMethodField()
    tk_dt_data = serializers.SerializerMethodField()
    tk_gv_data = serializers.SerializerMethodField()
    tk_vt_data = serializers.SerializerMethodField()
    tk_ck_data = serializers.SerializerMethodField()
    tk_km_data = serializers.SerializerMethodField()
    ma_bp_data = serializers.SerializerMethodField()
    ma_vv_data = serializers.SerializerMethodField()
    ma_hd_data = serializers.SerializerMethodField()
    ma_dtt_data = serializers.SerializerMethodField()
    ma_ku_data = serializers.SerializerMethodField()
    ma_phi_data = serializers.SerializerMethodField()
    ma_sp_data = serializers.SerializerMethodField()
    ma_cp0_data = serializers.SerializerMethodField()

    class Meta:
        model = ChiTietHoaDonBanHangModel
        fields = '__all__'
        read_only_fields = ['uuid', 'created', 'updated']

    def get_ma_vt_data(self, obj):
        if obj.ma_vt:
            return VatTuSerializer(obj.ma_vt).data
        return None

    def get_dvt_data(self, obj):
        if obj.dvt:
            return DonViTinhSerializer(obj.dvt).data
        return None

    def get_ma_kho_data(self, obj):
        if obj.ma_kho:
            return KhoHangModelSerializer(obj.ma_kho).data
        return None

    def get_ma_lo_data(self, obj):
        if obj.ma_lo:
            return LoModelSerializer(obj.ma_lo).data
        return None

    def get_ma_vi_tri_data(self, obj):
        if obj.ma_vi_tri:
            return ViTriKhoHangModelSerializer(obj.ma_vi_tri).data
        return None

    def get_tk_thue_co_data(self, obj):
        if obj.tk_thue_co:
            return AccountModelSerializer(obj.tk_thue_co).data
        return None

    def get_tk_dt_data(self, obj):
        if obj.tk_dt:
            return AccountModelSerializer(obj.tk_dt).data
        return None

    def get_tk_gv_data(self, obj):
        if obj.tk_gv:
            return AccountModelSerializer(obj.tk_gv).data
        return None

    def get_tk_vt_data(self, obj):
        if obj.tk_vt:
            return AccountModelSerializer(obj.tk_vt).data
        return None

    def get_tk_ck_data(self, obj):
        if obj.tk_ck:
            return AccountModelSerializer(obj.tk_ck).data
        return None

    def get_tk_km_data(self, obj):
        if obj.tk_km:
            return AccountModelSerializer(obj.tk_km).data
        return None

    def get_ma_bp_data(self, obj):
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_hd_data(self, obj):
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_dtt_data(self, obj):
        if obj.ma_dtt:
            return DotThanhToanModelSerializer(obj.ma_dtt).data
        return None

    def get_ma_ku_data(self, obj):
        if obj.ma_ku:
            return KheUocModelSerializer(obj.ma_ku).data
        return None

    def get_ma_phi_data(self, obj):
        if obj.ma_phi:
            return PhiSerializer(obj.ma_phi).data
        return None

    def get_ma_sp_data(self, obj):
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_cp0_data(self, obj):
        if obj.ma_cp0:
            return ChiPhiModelSerializer(obj.ma_cp0).data
        return None
