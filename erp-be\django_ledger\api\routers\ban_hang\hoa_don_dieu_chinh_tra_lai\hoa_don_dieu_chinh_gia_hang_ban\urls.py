"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for HoaDonDieuChinhGiaHangBan (Price Adjustment Invoice) module.
"""

from django.urls import include, path
from django_ledger.api.views.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dieu_chinh_gia_hang_ban import (
    ChiTietHoaDonDieuChinhGiaHangBanViewSet,
    HoaDonDieuChinhGiaHangBanViewSet,
)
from rest_framework.routers import DefaultRouter

# Main router for HoaDonDieuChinhGiaHangBan
router = DefaultRouter()
router.register(
    "", HoaDonDieuChinhGiaHangBanViewSet, basename="hoa-don-dieu-chinh-gia-hang-ban"
)

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path("", include(router.urls)),
    # Nested routes for chi_tiet under hoa_don
    path(
        "<uuid:hoa_don_uuid>/",
        include(
            [
                # ChiTiet routes
                path(
                    "chi-tiet/",
                    ChiTietHoaDonDieuChinhGiaHangBanViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="chi-tiet-hoa-don-dieu-chinh-gia-hang-ban-list",
                ),
                path(
                    "chi-tiet/<uuid:uuid>/",
                    ChiTietHoaDonDieuChinhGiaHangBanViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="chi-tiet-hoa-don-dieu-chinh-gia-hang-ban-detail",
                ),
            ]
        ),
    ),
]
