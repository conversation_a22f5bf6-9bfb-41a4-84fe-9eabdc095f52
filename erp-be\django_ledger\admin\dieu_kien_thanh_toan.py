"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Customized by TTMI Office.
"""

from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from django_ledger.models import DieuKienThanhToanModel, ChiTietDieuKienThanhToanModel


class ChiTietDieuKienThanhToanInline(admin.TabularInline):
    """
    Inline admin for ChiTietDieuKienThanhToanModel
    """
    model = ChiTietDieuKienThanhToanModel
    extra = 1
    readonly_fields = ['uuid', 'created', 'updated']


class DieuKienThanhToanModelAdmin(admin.ModelAdmin):
    """
    Admin class for the DieuKienThanhToanModel (Payment Condition) model.
    """
    list_display = ['ma_dk', 'ten_dk', 'ma_kh', 'loai_dk', 'status', 'created', 'updated']
    search_fields = ['ma_dk', 'ten_dk', 'ten_dk2']
    list_filter = ['status', 'loai_dk']
    readonly_fields = ['uuid', 'created', 'updated']
    raw_id_fields = ['entity_model', 'ma_kh']
    inlines = [ChiTietDieuKienThanhToanInline]
    fieldsets = [
        (
            _('Basic Information'), {
                'fields': [
                    'entity_model',
                    'ma_dk',
                    'ten_dk',
                    'ten_dk2',
                    'ma_kh',
                    'loai_dk',
                    'ngay_gom_hd1',
                    'ngay_gom_hd2',
                    'ngay_tt_thang',
                    'ngay_tt_tuan',
                    'tuan_tt_thu',
                    'so_ngay',
                    'status',
                    'created_by',
                    'updated_by',
                ]
            }
        ),
        (
            _('Metadata'), {
                'fields': [
                    'uuid',
                    'created',
                    'updated',
                ],
                'classes': ['collapse']
            }
        ),
    ]


class ChiTietDieuKienThanhToanModelAdmin(admin.ModelAdmin):
    """
    Admin class for the ChiTietDieuKienThanhToanModel (Payment Condition Detail) model.
    """
    list_display = ['dieu_kien_thanh_toan', 'ma_dk', 'ma_kh', 'line', 'created', 'updated']
    search_fields = ['ma_dk', 'dieu_kien_thanh_toan__ma_dk', 'dieu_kien_thanh_toan__ten_dk']
    list_filter = ['dieu_kien_thanh_toan__status']
    readonly_fields = ['uuid', 'created', 'updated']
    raw_id_fields = ['dieu_kien_thanh_toan', 'ma_kh']
    fieldsets = [
        (
            _('Basic Information'), {
                'fields': [
                    'dieu_kien_thanh_toan',
                    'ma_kh',
                    'ma_dk',
                    'line',
                    'ngay_gom_hd1',
                    'ngay_gom_hd2',
                    'created_by',
                    'updated_by',
                ]
            }
        ),
        (
            _('Metadata'), {
                'fields': [
                    'uuid',
                    'created',
                    'updated',
                ],
                'classes': ['collapse']
            }
        ),
    ]
