"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Ban Hang (Sales) module.
"""

from django.urls import path, include

# URL patterns for the module
urlpatterns = [
    path("don-hang/", include("django_ledger.api.routers.ban_hang.don_hang.urls")),
    path(
        "hoa-don-ban-ra/",
        include("django_ledger.api.routers.ban_hang.hoa_don_ban_ra.urls"),
    ),
    path(
        "hoa-don-ban-hang-ipos/",
        include("django_ledger.api.routers.ban_hang.hoa_don_ban_hang_ipos.urls"),
    ),
    path(
        "hoa-don-dieu-chinh-tra-lai/",
        include("django_ledger.api.routers.ban_hang.hoa_don_dieu_chinh_tra_lai.urls"),
    ),
    path(
        "dieu-chinh-can-tru-cong-no/",
        include("django_ledger.api.routers.ban_hang.dieu_chinh_can_tru_cong_no.urls"),
    ),
]
