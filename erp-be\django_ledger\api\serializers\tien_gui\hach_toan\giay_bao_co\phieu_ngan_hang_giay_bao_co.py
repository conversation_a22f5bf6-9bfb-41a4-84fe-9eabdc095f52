"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for PhieuNganHangGiayBaoCo model.
"""

from rest_framework import serializers

from django.utils.translation import gettext_lazy as _

from django_ledger.models import PhieuNganHangGiayBaoCoModel
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.accounts import AccountModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc import KheUocModelSerializer
from django_ledger.api.serializers.danh_muc import PhiSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.danh_muc import ChiPhiKhongHopLeSerializer


class PhieuNganHangGiayBaoCoSerializer(serializers.ModelSerializer):
    """
    Serializer for PhieuNganHangGiayBaoCo model.
    """
    # Read-only fields for related objects
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    tk_cpnh_data = serializers.SerializerMethodField(read_only=True)
    tk_du_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_cp0_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = PhieuNganHangGiayBaoCoModel
        fields = [
            'uuid',
            'entity_model',
            'giay_bao_co',
            'line',
            'ma_kh',
            'ma_kh_data',
            'ma_cpnh',
            'tien_cp_nt',
            'so_ct0',
            'so_ct2',
            'ngay_ct0',
            'dien_giai',
            'tk_cpnh',
            'tk_cpnh_data',
            'ten_tk_cpnh',
            'tk_du',
            'tk_du_data',
            'ten_tk_du',
            'tk_thue',
            'tk_thue_data',
            'ma_thue',
            'thue_suat',
            't_thue_nt',
            'tien_cp',
            't_thue',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_dtt',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_sp_data',
            'ma_lsx',
            'ma_cp0',
            'ma_cp0_data',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'giay_bao_co',
            'ma_kh_data',
            'tk_cpnh_data',
            'tk_du_data',
            'tk_thue_data',
            'ma_bp_data',
            'ma_vv_data',
            'ma_ku_data',
            'ma_phi_data',
            'ma_sp_data',
            'ma_cp0_data',
            'created',
            'updated'
        ]

    def get_ma_kh_data(self, obj):
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_tk_cpnh_data(self, obj):
        """
        Get bank fee account data.
        """
        if obj.tk_cpnh:
            return AccountModelSerializer(obj.tk_cpnh).data
        return None

    def get_tk_du_data(self, obj):
        """
        Get offset account data.
        """
        if obj.tk_du:
            return AccountModelSerializer(obj.tk_du).data
        return None

    def get_tk_thue_data(self, obj):
        """
        Get tax account data.
        """
        if obj.tk_thue:
            return AccountModelSerializer(obj.tk_thue).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):
        """
        Get case data.
        """
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_ku_data(self, obj):
        """
        Get loan data.
        """
        if obj.ma_ku:
            return KheUocModelSerializer(obj.ma_ku).data
        return None

    def get_ma_phi_data(self, obj):
        """
        Get fee data.
        """
        if obj.ma_phi:
            return PhiSerializer(obj.ma_phi).data
        return None

    def get_ma_sp_data(self, obj):
        """
        Get product data.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_cp0_data(self, obj):
        """
        Get invalid expense data.
        """
        if obj.ma_cp0:
            return ChiPhiKhongHopLeSerializer(obj.ma_cp0).data
        return None
