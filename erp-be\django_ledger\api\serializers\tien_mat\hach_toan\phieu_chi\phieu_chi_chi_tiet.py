"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for PhieuChiChiTiet model.
"""

from django.utils.translation import gettext_lazy as _
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.danh_muc import ChiPhiKhongHopLeSerializer
from django_ledger.api.serializers.chung_tu import ChungTuSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.models import PhieuChiChiTietModel
from rest_framework import serializers


class PhieuChiChiTietSerializer(serializers.ModelSerializer):
    """
    Serializer for PhieuChiChiTiet model.
    """

    # Read-only fields for related objects
    phieu_chi_data = serializers.SerializerMethodField(read_only=True)
    tk_no_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    so_ct0_data = serializers.SerializerMethodField(read_only=True)
    so_ct2_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_cp0_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = PhieuChiChiTietModel
        fields = [
            "uuid",
            "phieu_chi",
            "phieu_chi_data",
            "id",
            "line",
            "dien_giai",
            "ma_kh",
            "ma_kh_data",
            "tknh2",
            "id_hd",
            "tk_no",
            "tk_no_data",
            "so_ct0",
            "so_ct0_data",
            "so_ct2",
            "so_ct2_data",
            "ty_gia2",
            "tien_nt",
            "tien",
            "ma_loai_hd",
            "ma_thue",
            "ten_thue",
            "thue_suat",
            "tk_thue",
            "ten_tk_thue",
            "so_ct0",
            "so_ct2",
            "ngay_ct0",
            "ma_mau_ct",
            "ma_mau_bc",
            "ma_tc_thue",
            "ma_kh_thue",
            "ten_kh_thue",
            "dia_chi",
            "ma_so_thue",
            "ten_vt_thue",
            "thue_nt",
            "thue",
            "ma_kh9",
            "ten_kh9",
            "id_dn",
            "line_dn",
            "ghi_chu",
            "ma_bp",
            "ma_vv",
            "ma_hd",
            "ma_hd_data",
            "ma_dtt",
            "ma_ku",
            "ma_phi",
            "ma_sp",
            "ma_sp_data",
            "ma_lsx",
            "ma_cp0",
            "ma_cp0_data",
            "id_tb",
            "line_tb",
            "id_tt",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "phieu_chi",
            "phieu_chi_data",
            "tk_no_data",
            "ma_kh_data",
            "so_ct0_data",
            "so_ct2_data",
            "ma_hd_data",
            "ma_sp_data",
            "ma_cp0_data",
            "created",
            "updated",
        ]

    def get_phieu_chi_data(self, obj):
        """
        Get payment voucher data.
        """
        if obj.phieu_chi:
            return {
                "uuid": obj.phieu_chi.uuid,
                "i_so_ct": obj.phieu_chi.i_so_ct,
                "dien_giai": obj.phieu_chi.dien_giai,
                "ngay_ct": obj.phieu_chi.ngay_ct,
            }
        return None

    def get_tk_no_data(self, obj):
        """
        Get debit account data.
        """
        if obj.tk_no:
            return AccountModelSerializer(obj.tk_no).data
        return None

    def get_ma_kh_data(self, obj):
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_so_ct0_data(self, obj):
        """
        Get original document data.
        """
        if obj.so_ct0:
            return ChungTuSerializer(obj.so_ct0).data
        return None

    def get_so_ct2_data(self, obj):
        """
        Get document 2 data.
        """
        if obj.so_ct2:
            return ChungTuSerializer(obj.so_ct2).data
        return None

    def get_ma_hd_data(self, obj):
        """
        Get contract data.
        """
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_sp_data(self, obj):
        """
        Get product data.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_cp0_data(self, obj):
        """
        Get invalid expense data.
        """
        if obj.ma_cp0:
            return ChiPhiKhongHopLeSerializer(obj.ma_cp0).data
        return None


class PhieuChiChiTietListSerializer(serializers.ModelSerializer):
    """
    Serializer for PhieuChiChiTiet list view.
    """

    # Read-only fields for related objects
    tk_no_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = PhieuChiChiTietModel
        fields = [
            "uuid",
            "phieu_chi",
            "line",
            "dien_giai",
            "ma_kh",
            "tk_no",
            "tk_no_data",
            "tien_nt",
            "tien",
            "thue_nt",
            "thue",
            "ma_bp",
            "ma_vv",
            "ghi_chu",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "tk_no_data",
            "created",
            "updated",
        ]

    def get_tk_no_data(self, obj):
        """
        Get debit account data.
        """
        if obj.tk_no:
            return {
                "uuid": obj.tk_no.uuid,
                "code": obj.tk_no.code,
                "name": obj.tk_no.name,
            }
        return None
