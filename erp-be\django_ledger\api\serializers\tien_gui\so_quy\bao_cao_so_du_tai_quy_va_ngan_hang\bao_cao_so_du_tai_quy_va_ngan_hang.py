"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Bao Cao So Du <PERSON> (Cash and Bank Balance Report) API.
"""

from rest_framework import serializers


class BaoCaoSoDuTaiQuyVaNganHangRequestSerializer(serializers.Serializer):
    """
    Serializer for validating cash and bank balance report POST body data.
    Validates all POST body parameters from cURL request.
    """

    # Filter parameters from the cURL request
    tk = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Mã tài khoản filter (account code filter)"
    )
    
    ngay_ct2 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Ng<PERSON><PERSON> kết thúc báo cáo (format: YYYYMMDD)"
    )
    
    ma_unit = serializers.Char<PERSON>ield(
        required=False,
        allow_blank=True,
        help_text="UUID của đơn vị (optional, UUID)"
    )
    
    # Standard report parameters
    mau_bc = serializers.IntegerField(
        required=False,
        default=20,
        help_text="Mẫu báo cáo (optional, integer, default=20)"
    )
    
    data_analysis_struct = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Cấu trúc phân tích (optional, string)"
    )

    def validate_ngay_ct2(self, value):
        """
        Validate date format for ngay_ct2.
        """
        if value and len(value) == 8:
            try:
                # Validate date format YYYYMMDD
                year = int(value[:4])
                month = int(value[4:6])
                day = int(value[6:8])
                
                if not (1900 <= year <= 9999):
                    raise serializers.ValidationError("Năm phải từ 1900 đến 9999")
                if not (1 <= month <= 12):
                    raise serializers.ValidationError("Tháng phải từ 1 đến 12")
                if not (1 <= day <= 31):
                    raise serializers.ValidationError("Ngày phải từ 1 đến 31")
                    
            except ValueError:
                raise serializers.ValidationError("Định dạng ngày không hợp lệ. Sử dụng YYYYMMDD")
        elif value and len(value) != 8:
            raise serializers.ValidationError("Định dạng ngày phải là YYYYMMDD (8 ký tự)")
            
        return value


class BaoCaoSoDuTaiQuyVaNganHangResponseSerializer(serializers.Serializer):
    """
    Serializer for formatting cash and bank balance report response data.
    Matches exact cURL response fields: stt, tk, ct_yn, du_no, du_no_nt, du_co, du_co_nt, ten_tk, du
    """

    stt = serializers.IntegerField(
        help_text="Số thứ tự"
    )

    tk = serializers.CharField(
        help_text="Mã tài khoản"
    )

    ct_yn = serializers.CharField(
        help_text="Chứng từ Y/N"
    )

    du_no = serializers.FloatField(
        help_text="Dư nợ"
    )

    du_no_nt = serializers.FloatField(
        help_text="Dư nợ ngoại tệ"
    )

    du_co = serializers.FloatField(
        help_text="Dư có"
    )

    du_co_nt = serializers.FloatField(
        help_text="Dư có ngoại tệ"
    )

    ten_tk = serializers.CharField(
        help_text="Tên tài khoản"
    )

    du = serializers.FloatField(
        help_text="Số dư"
    )
