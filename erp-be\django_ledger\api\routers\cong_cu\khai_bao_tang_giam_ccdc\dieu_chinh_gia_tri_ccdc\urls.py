"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for DieuChinhGiaTriCCDC (Tool Value Adjustment) module.
"""

from django.urls import path

from django_ledger.api.views.cong_cu.khai_bao_tang_giam_ccdc.dieu_chinh_gia_tri_ccdc import DieuChinhGiaTriCCDCViewSet

# DieuChinhGiaTriCCDC routes
urlpatterns = [
    # DieuChinhGiaTriCCDC routes
    path('', DieuChinhGiaTriCCDCViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='dieu-chinh-gia-tri-ccdc-list'),

    path('<uuid:pk>/', DieuChinhGiaTriCCDCViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='dieu-chinh-gia-tri-ccdc-detail'),
]
