"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiTietPhieuNhapKhoSerializer, which handles serialization
for the ChiTietPhieuNhapKhoModel.
"""

from rest_framework import serializers

from django_ledger.models.ton_kho.nhap_kho_noi_bo.phieu_nhap_kho import ChiTietPhieuNhapKhoModel
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.warehouse import KhoHangModelSerializer
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.nhap_xuat import NhapXuatModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.tien_do_thanh_toan import DotThanhToanModelSerializer
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import KheUocModelSerializer
from django_ledger.api.serializers.danh_muc import PhiSerializer, ChiPhiKhongHopLeSerializer


class ChiTietPhieuNhapKhoSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietPhieuNhapKhoModel.
    """

    # Reference data fields
    ma_vt_data = serializers.SerializerMethodField()
    dvt_data = serializers.SerializerMethodField()
    ma_kho_data = serializers.SerializerMethodField()
    tk_vt_data = serializers.SerializerMethodField()
    ma_nx_data = serializers.SerializerMethodField()
    tk_du_data = serializers.SerializerMethodField()
    ma_bp_data = serializers.SerializerMethodField()
    ma_vv_data = serializers.SerializerMethodField()
    ma_hd_data = serializers.SerializerMethodField()
    ma_dtt_data = serializers.SerializerMethodField()
    ma_ku_data = serializers.SerializerMethodField()
    ma_phi_data = serializers.SerializerMethodField()
    ma_sp_data = serializers.SerializerMethodField()
    ma_cp0_data = serializers.SerializerMethodField()

    class Meta:
        model = ChiTietPhieuNhapKhoModel
        fields = [
            'uuid', 'phieu_nhap_kho', 'line', 'ma_vt', 'dvt', 'ten_dvt', 'ma_kho', 'ten_kho',
            'ma_lo', 'ten_lo', 'lo_yn', 'ma_vi_tri', 'ten_vi_tri', 'vi_tri_yn', 'he_so',
            'qc_yn', 'so_luong', 'pn_tb', 'gia_nt', 'tien_nt', 'tk_vt', 'ma_nx', 'tk_du',
            'ma_bp', 'ma_vv', 'ma_hd', 'ma_dtt', 'ma_ku', 'ma_phi', 'ma_sp', 'ma_lsx',
            'ma_cp0', 'gia', 'tien', 'sl_pn', 'id_pn', 'line_pn', 'id_sx1', 'line_sx1',
            'created', 'updated',
            'ma_vt_data', 'dvt_data', 'ma_kho_data', 'tk_vt_data', 'ma_nx_data', 'tk_du_data',
            'ma_bp_data', 'ma_vv_data', 'ma_hd_data', 'ma_dtt_data', 'ma_ku_data', 'ma_phi_data',
            'ma_sp_data', 'ma_cp0_data'
        ]
        read_only_fields = ['uuid', 'phieu_nhap_kho', 'created', 'updated']

    def get_ma_vt_data(self, obj):
        """
        Returns the material data for the ma_vt field.
        """
        if obj.ma_vt:
            return VatTuSerializer(obj.ma_vt).data
        return None

    def get_dvt_data(self, obj):
        """
        Returns the unit data for the dvt field.
        """
        if obj.dvt:
            return DonViTinhSerializer(obj.dvt).data
        return None

    def get_ma_kho_data(self, obj):
        """
        Returns the warehouse data for the ma_kho field.
        """
        if obj.ma_kho:
            return KhoHangModelSerializer(obj.ma_kho).data
        return None

    def get_tk_vt_data(self, obj):
        """
        Returns the account data for the tk_vt field.
        """
        if obj.tk_vt:
            return AccountModelSerializer(obj.tk_vt).data
        return None

    def get_ma_nx_data(self, obj):
        """
        Returns the import/export data for the ma_nx field.
        """
        if obj.ma_nx:
            return NhapXuatModelSerializer(obj.ma_nx).data
        return None

    def get_tk_du_data(self, obj):
        """
        Returns the account data for the tk_du field.
        """
        if obj.tk_du:
            return AccountModelSerializer(obj.tk_du).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Returns the department data for the ma_bp field.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):
        """
        Returns the task data for the ma_vv field.
        """
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_hd_data(self, obj):
        """
        Returns the contract data for the ma_hd field.
        """
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_dtt_data(self, obj):
        """
        Returns the payment phase data for the ma_dtt field.
        """
        if obj.ma_dtt:
            return DotThanhToanModelSerializer(obj.ma_dtt).data
        return None

    def get_ma_ku_data(self, obj):
        """
        Returns the loan agreement data for the ma_ku field.
        """
        if obj.ma_ku:
            return KheUocModelSerializer(obj.ma_ku).data
        return None

    def get_ma_phi_data(self, obj):
        """
        Returns the fee data for the ma_phi field.
        """
        if obj.ma_phi:
            return PhiSerializer(obj.ma_phi).data
        return None

    def get_ma_sp_data(self, obj):
        """
        Returns the product data for the ma_sp field.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_cp0_data(self, obj):
        """
        Returns the invalid expense data for the ma_cp0 field.
        """
        if obj.ma_cp0:
            return ChiPhiKhongHopLeSerializer(obj.ma_cp0).data
        return None
