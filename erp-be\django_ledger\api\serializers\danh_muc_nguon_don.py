"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for the DanhMucNguonDon (Order Source) model.
"""

from rest_framework import serializers
from django_ledger.models.danh_muc_nguon_don import DanhMucNguonDonModel


class DanhMucNguonDonModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the DanhMucNguonDonModel (Order Source) model.

    This serializer handles the conversion between DanhMucNguonDonModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - entity_model: Reference to the entity (ForeignKey to EntityModel)
    - ma_nguondon: Order source code
    - ten_nguondon: Name of the order source
    - ghi_chu: Additional notes (optional)
    - status: Status indicator ('0'=inactive, '1'=active)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    """
    class Meta:
        model = DanhMucNguonDonModel
        fields = ['uuid', 'entity_model', 'ma_nguondon', 'ten_nguondon', 'ghi_chu', 'status',
                  'created', 'updated']
        read_only_fields = ['uuid', 'entity_model', 'created', 'updated']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_nguondon": "WEB",
                "ten_nguondon": "Đơn hàng từ website",
                "ghi_chu": "Đơn hàng được tạo tự động từ website",
                "status": "1",
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z"
            }
        }

    def validate_ma_nguondon(self, value):
        """
        Validate ma_nguondon field

        Parameters
        ----------
        value : str
            The ma_nguondon value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Mã nguồn đơn không được để trống')
        if len(value) > 50:
            raise serializers.ValidationError('Mã nguồn đơn không được vượt quá 50 ký tự')
        return value.strip()

    def validate_ten_nguondon(self, value):
        """
        Validate ten_nguondon field

        Parameters
        ----------
        value : str
            The ten_nguondon value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Tên nguồn đơn không được để trống')
        if len(value) > 200:
            raise serializers.ValidationError('Tên nguồn đơn không được vượt quá 200 ký tự')
        return value.strip()
