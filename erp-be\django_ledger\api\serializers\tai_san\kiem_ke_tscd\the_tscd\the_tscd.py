"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for The TSCD (Fixed Asset Report) API.
"""

from django.core.validators import MaxValueValidator, MinValueValidator
from rest_framework import serializers


class TheTSCDRequestSerializer(serializers.Serializer):
    """
    Serializer for validating fixed asset report POST body data.
    Validates all POST body parameters from cURL request.
    """

    # Required parameters
    ky = serializers.IntegerField(
        required=True,
        validators=[MinValueValidator(1), MaxValueValidator(12)],
        help_text="Kỳ báo cáo (1-12)",
    )
    nam = serializers.IntegerField(
        required=True,
        validators=[MinValueValidator(1900), MaxValueValidator(9999)],
        help_text="Năm báo cáo",
    )

    # Optional filter parameters (UUID or string for model references)
    ma_ts = serializers.Char<PERSON>ield(
        required=False, allow_blank=True, help_text="UUID hoặc mã tài sản filter"
    )
    ma_lts = serializers.CharField(
        required=False, allow_blank=True, help_text="UUID hoặc mã loại tài sản filter"
    )
    ma_bp = serializers.CharField(
        required=False, allow_blank=True, help_text="UUID hoặc mã bộ phận filter"
    )
    nh_ts1 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="UUID hoặc mã nhóm tài sản 1 filter",
    )
    nh_ts2 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="UUID hoặc mã nhóm tài sản 2 filter",
    )
    nh_ts3 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="UUID hoặc mã nhóm tài sản 3 filter",
    )
    mau_bc = serializers.IntegerField(
        required=False, default=20, help_text="Mẫu báo cáo"
    )
    ma_unit = serializers.UUIDField(
        required=False, allow_null=True, help_text="UUID của đơn vị filter"
    )
    data_analysis_struct = serializers.CharField(
        required=False, allow_blank=True, help_text="Cấu trúc phân tích dữ liệu"
    )

    def validate_ky(self, value):
        """
        Validate period (ky) is between 1 and 12.
        """
        if not (1 <= value <= 12):
            raise serializers.ValidationError("Kỳ phải từ 1 đến 12")
        return value

    def validate_nam(self, value):
        """
        Validate year is reasonable.
        """
        if not (1900 <= value <= 9999):
            raise serializers.ValidationError("Năm phải từ 1900 đến 9999")
        return value

    def validate(self, attrs):
        """
        Cross-field validation if needed.
        """
        # Add any cross-field validation here
        return attrs


class TheTSCDResponseSerializer(serializers.Serializer):
    """
    Serializer for formatting single fixed asset report record.
    """

    stt = serializers.IntegerField(help_text="Số thứ tự")
    ma_ts = serializers.CharField(help_text="Mã tài sản")
    ngay_kh0 = serializers.CharField(
        help_text="Ngày khấu hao lần đầu", allow_blank=True
    )
    so_ky_kh = serializers.IntegerField(help_text="Số kỳ khấu hao")
    tk_ts = serializers.CharField(help_text="Tài khoản tài sản")
    tk_kh = serializers.CharField(help_text="Tài khoản khấu hao")
    tk_cp = serializers.CharField(help_text="Tài khoản chi phí")
    ma_bp = serializers.CharField(help_text="Mã bộ phận")
    nh_ts1 = serializers.CharField(help_text="Nhóm tài sản 1", allow_blank=True)
    nh_ts2 = serializers.CharField(help_text="Nhóm tài sản 2", allow_blank=True)
    nh_ts3 = serializers.CharField(help_text="Nhóm tài sản 3", allow_blank=True)
    nuoc_sx = serializers.CharField(help_text="Nước sản xuất", allow_blank=True)
    nam_sx = serializers.CharField(help_text="Năm sản xuất", allow_blank=True)
    ngay_ct = serializers.CharField(help_text="Ngày chứng từ", allow_blank=True)
    ngay_giam = serializers.CharField(help_text="Ngày giảm", allow_blank=True)
    ngay_mua = serializers.CharField(help_text="Ngày mua", allow_blank=True)
    so_ct = serializers.CharField(help_text="Số chứng từ")
    so_ct_giam = serializers.CharField(help_text="Số chứng từ giảm", allow_blank=True)
    ly_do_giam = serializers.CharField(help_text="Lý do giảm", allow_blank=True)
    ngay_dc = serializers.CharField(help_text="Ngày điều chỉnh", allow_blank=True)
    ly_do_dc = serializers.CharField(help_text="Lý do điều chỉnh", allow_blank=True)
    so_hieu_ts = serializers.CharField(help_text="Số hiệu tài sản", allow_blank=True)
    ma_nt = serializers.CharField(help_text="Mã ngoại tệ", allow_blank=True)
    ten_ts = serializers.CharField(help_text="Tên tài sản")
    ten_bp = serializers.CharField(help_text="Tên bộ phận", allow_blank=True)
