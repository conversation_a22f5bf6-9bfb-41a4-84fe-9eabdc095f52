"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for the QuyDoiDonViTinhChung (Unit Conversion) model.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from django_ledger.models.quy_doi_don_vi_tinh_chung import QuyDoiDonViTinhChungModel
from django_ledger.models.don_vi_tinh import DonViTinhModel
from django_ledger.api.serializers.base import GlobalModelSerializer
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer

class QuyDoiDonViTinhChungModelSerializer(GlobalModelSerializer):
    """
    Serializer for the QuyDoiDonViTinhChungModel (Unit Conversion) model.

    This serializer handles the conversion between QuyDoiDonViTinhChungModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - dvt: Unit of measurement code to convert from
    - dvt_obj: Unit of measurement object (read-only)
    - he_so: Conversion factor between the unit and the base unit
    - dvt0: Base unit of measurement code to convert to
    - dvt0_obj: Base unit of measurement object (read-only)
    - status: Status indicator ('1'=active, '0'=inactive)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    """
    # Add nested serializers for related DonViTinh objects
    dvt_data = serializers.SerializerMethodField(read_only=True)
    dvt0_data = serializers.SerializerMethodField(read_only=True)
    class Meta:
        model = QuyDoiDonViTinhChungModel
        fields = [
            'uuid',
            'dvt',
            'dvt_data',
            'he_so',
            'dvt0',
            'dvt0_data',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated', 'dvt_data', 'dvt0_data']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "dvt": "kg",
                "dvt_data": {
                    "uuid": "123e4567-e89b-12d3-a456-************",
                    "dvt": "kg",
                    "ten_dvt": "Kilogram"
                },
                "he_so": 1000.00,
                "dvt0": "g",
                "dvt0_data": {
                    "uuid": "123e4567-e89b-12d3-a456-************",
                    "dvt": "g",
                    "ten_dvt": "Gram"
                },
                "status": "1",
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z"
            }
        }

    def get_dvt_data(self, obj):
        """
        Get the DonViTinh data for the dvt field
        """
        # If dvt is a UUID string, try to get the DonViTinh object
        try:
            dvt_obj = DonViTinhModel.objects.filter(uuid=obj.dvt).first()
            if dvt_obj:
                return DonViTinhSerializer(dvt_obj).data
        except (ValueError, TypeError):
            pass

        # If dvt is just a code string, return a simple object
        return {
            "dvt": obj.dvt,
            "ten_dvt": obj.dvt  # Use the code as the name if no object is found
        }

    def get_dvt0_data(self, obj):
        """
        Get the DonViTinh data for the dvt0 field
        """
        # If dvt0 is a UUID string, try to get the DonViTinh object
        try:
            dvt0_obj = DonViTinhModel.objects.filter(uuid=obj.dvt0).first()
            if dvt0_obj:
                return DonViTinhSerializer(dvt0_obj).data
        except (ValueError, TypeError):
            pass

        # If dvt0 is just a code string, return a simple object
        return {
            "dvt": obj.dvt0,
            "ten_dvt": obj.dvt0  # Use the code as the name if no object is found
        }
