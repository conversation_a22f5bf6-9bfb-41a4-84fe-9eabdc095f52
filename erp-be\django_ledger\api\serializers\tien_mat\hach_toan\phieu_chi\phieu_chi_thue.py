"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for PhieuChiThue model.
"""

from django.utils.translation import gettext_lazy as _
from django_ledger.api.serializers.danh_muc import ChiPhiKhongHopLeSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.models import PhieuChiThueModel
from rest_framework import serializers


class PhieuChiThueSerializer(serializers.ModelSerializer):
    """
    Serializer for PhieuChiThue model.
    """

    # Read-only fields for related objects
    phieu_chi_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_cp0_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = PhieuChiThueModel
        fields = [
            "uuid",
            "phieu_chi",
            "phieu_chi_data",
            "id",
            "line",
            "so_ct0",
            "so_ct2",
            "ngay_ct0",
            "ma_thue",
            "thue_suat",
            "ma_mau_ct",
            "ma_mau_bc",
            "ma_tc_thue",
            "ma_kh",
            "ma_kh_data",
            "ten_kh_thue",
            "dia_chi",
            "ma_so_thue",
            "ten_vt_thue",
            "t_tien_nt",
            "t_tien",
            "tk_thue_no",
            "ten_tk_thue_no",
            "tk_du",
            "ten_tk_du",
            "t_thue_nt",
            "t_thue",
            "ma_kh9",
            "ten_kh9",
            "ma_tt",
            "ten_tt",
            "ghi_chu",
            "id_tt",
            "ma_bp",
            "ma_vv",
            "ma_hd",
            "ma_hd_data",
            "ma_dtt",
            "ma_ku",
            "ma_phi",
            "ma_sp",
            "ma_sp_data",
            "ma_lsx",
            "ma_cp0",
            "ma_cp0_data",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "phieu_chi",
            "phieu_chi_data",
            "ma_kh_data",
            "ma_hd_data",
            "ma_sp_data",
            "ma_cp0_data",
            "created",
            "updated",
        ]

    def get_phieu_chi_data(self, obj):
        """
        Get payment voucher data.
        """
        if obj.phieu_chi:
            return {
                "uuid": obj.phieu_chi.uuid,
                "i_so_ct": obj.phieu_chi.i_so_ct,
                "dien_giai": obj.phieu_chi.dien_giai,
                "ngay_ct": obj.phieu_chi.ngay_ct,
            }
        return None

    def get_ma_kh_data(self, obj):
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_ma_hd_data(self, obj):
        """
        Get contract data.
        """
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_sp_data(self, obj):
        """
        Get product data.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_cp0_data(self, obj):
        """
        Get invalid expense data.
        """
        if obj.ma_cp0:
            return ChiPhiKhongHopLeSerializer(obj.ma_cp0).data
        return None


class PhieuChiThueListSerializer(serializers.ModelSerializer):
    """
    Serializer for PhieuChiThue list view.
    """

    # Read-only fields for related objects

    class Meta:
        model = PhieuChiThueModel
        fields = [
            "uuid",
            "phieu_chi",
            "line",
            "ma_thue",
            "thue_suat",
            "ma_kh",
            "ten_kh_thue",
            "t_tien_nt",
            "t_tien",
            "t_thue_nt",
            "t_thue",
            "ma_bp",
            "ma_vv",
            "ghi_chu",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "created",
            "updated",
        ]
