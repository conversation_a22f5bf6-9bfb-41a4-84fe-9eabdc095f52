"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietHoaDonDichVuTraLaiGiamGia (Service Invoice Return/Discount Detail) model.
"""

from rest_framework import serializers

from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dich_vu_tra_lai_giam_gia import ChiTietHoaDonDichVuTraLaiGiamGiaModel
from django_ledger.services.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dich_vu_tra_lai_giam_gia import ChiTietHoaDonDichVuTraLaiGiamGiaService


class ChiTietHoaDonDichVuTraLaiGiamGiaModelSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietHoaDonDichVuTraLaiGiamGia model.
    """
    
    hoa_don_data = serializers.SerializerMethodField()
    tk_no_data = serializers.SerializerMethodField()
    tk_thue_no_data = serializers.SerializerMethodField()
    ma_bp_data = serializers.SerializerMethodField()
    ma_vv_data = serializers.SerializerMethodField()
    ma_hd_data = serializers.SerializerMethodField()
    ma_dtt_data = serializers.SerializerMethodField()
    ma_ku_data = serializers.SerializerMethodField()
    ma_phi_data = serializers.SerializerMethodField()
    ma_sp_data = serializers.SerializerMethodField()
    ma_cp0_data = serializers.SerializerMethodField()
    
    class Meta:
        model = ChiTietHoaDonDichVuTraLaiGiamGiaModel
        fields = '__all__'
        read_only_fields = ['uuid', 'created', 'updated']
    
    def get_hoa_don_data(self, obj):
        """
        Get hoa_don data.
        """
        if obj.hoa_don:
            return {
                'uuid': obj.hoa_don.uuid,
                'so_ct': obj.hoa_don.so_ct.so_ct if obj.hoa_don.so_ct else None,
                'ngay_ct': obj.hoa_don.ngay_ct
            }
        return None
    
    def get_tk_no_data(self, obj):
        """
        Get tk_no data.
        """
        if obj.tk_no:
            return {
                'uuid': obj.tk_no.uuid,
                'code': obj.tk_no.code,
                'name': obj.tk_no.name
            }
        return None
    
    def get_tk_thue_no_data(self, obj):
        """
        Get tk_thue_no data.
        """
        if obj.tk_thue_no:
            return {
                'uuid': obj.tk_thue_no.uuid,
                'code': obj.tk_thue_no.code,
                'name': obj.tk_thue_no.name
            }
        return None
    
    def get_ma_bp_data(self, obj):
        """
        Get ma_bp data.
        """
        if obj.ma_bp:
            return {
                'uuid': obj.ma_bp.uuid,
                'ma_bp': obj.ma_bp.ma_bp,
                'ten_bp': obj.ma_bp.ten_bp
            }
        return None
    
    def get_ma_vv_data(self, obj):
        """
        Get ma_vv data.
        """
        if obj.ma_vv:
            return {
                'uuid': obj.ma_vv.uuid,
                'ma_vv': obj.ma_vv.ma_vv,
                'ten_vv': obj.ma_vv.ten_vv
            }
        return None
    
    def get_ma_hd_data(self, obj):
        """
        Get ma_hd data.
        """
        if obj.ma_hd:
            return {
                'uuid': obj.ma_hd.uuid,
                'ma_hd': obj.ma_hd.ma_hd,
                'ten_hd': obj.ma_hd.ten_hd
            }
        return None
    
    def get_ma_dtt_data(self, obj):
        """
        Get ma_dtt data.
        """
        if obj.ma_dtt:
            return {
                'uuid': obj.ma_dtt.uuid,
                'ma_dtt': obj.ma_dtt.ma_dtt,
                'ten_dtt': obj.ma_dtt.ten_dtt
            }
        return None
    
    def get_ma_ku_data(self, obj):
        """
        Get ma_ku data.
        """
        if obj.ma_ku:
            return {
                'uuid': obj.ma_ku.uuid,
                'ma_ku': obj.ma_ku.ma_ku,
                'ten_ku': obj.ma_ku.ten_ku
            }
        return None
    
    def get_ma_phi_data(self, obj):
        """
        Get ma_phi data.
        """
        if obj.ma_phi:
            return {
                'uuid': obj.ma_phi.uuid,
                'ma_phi': obj.ma_phi.ma_phi,
                'ten_phi': obj.ma_phi.ten_phi
            }
        return None
    
    def get_ma_sp_data(self, obj):
        """
        Get ma_sp data.
        """
        if obj.ma_sp:
            return {
                'uuid': obj.ma_sp.uuid,
                'ma_vt': obj.ma_sp.ma_vt,
                'ten_vt': obj.ma_sp.ten_vt
            }
        return None
    
    def get_ma_cp0_data(self, obj):
        """
        Get ma_cp0 data.
        """
        if obj.ma_cp0:
            return {
                'uuid': obj.ma_cp0.uuid,
                'ma_cp': obj.ma_cp0.ma_cp,
                'ten_cp': obj.ma_cp0.ten_cp
            }
        return None


class ChiTietHoaDonDichVuTraLaiGiamGiaModelCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating ChiTietHoaDonDichVuTraLaiGiamGia model.
    """
    
    class Meta:
        model = ChiTietHoaDonDichVuTraLaiGiamGiaModel
        fields = '__all__'
        read_only_fields = ['uuid', 'created', 'updated']
    
    def create(self, validated_data):
        """
        Create a new ChiTietHoaDonDichVuTraLaiGiamGia instance.
        """
        service = ChiTietHoaDonDichVuTraLaiGiamGiaService()
        instance = service.create(validated_data)
        
        return instance
    
    def update(self, instance, validated_data):
        """
        Update an existing ChiTietHoaDonDichVuTraLaiGiamGia instance.
        """
        service = ChiTietHoaDonDichVuTraLaiGiamGiaService()
        instance = service.update(instance.uuid, validated_data)
        
        return instance
