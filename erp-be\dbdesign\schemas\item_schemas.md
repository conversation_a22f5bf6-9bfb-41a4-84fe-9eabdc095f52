# Sơ Đồ Cơ Sở Dữ Liệu Hàng Hóa - D<PERSON>go Ledger

Tài liệu này mô tả chi tiết cấu trúc cơ sở dữ liệu cho các thực thể liên quan đến hàng hóa và dịch vụ trong hệ thống Django Ledger.

## <PERSON><PERSON><PERSON>

- [Sơ Đồ Cơ Sở Dữ Liệu Hàng Hóa - Django Ledger](#sơ-đồ-cơ-sở-dữ-liệu-hàng-hóa---django-ledger)
  - [<PERSON><PERSON><PERSON>](#mục-lục)
  - [UnitOfMeasureModel](#unitofmeasuremodel)
  - [ItemModel](#itemmodel)
  - [ItemTransactionModel](#itemtransactionmodel)
  - [ImportJobModel](#importjobmodel)
  - [StagedTransactionModel](#stagedtransactionmodel)

## UnitOfMeasureModel

Đại diện cho một đơn vị đo lường của hàng hóa/dịch vụ.

| Tr<PERSON>ờng | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| name | CharField | Tên đơn vị đo lường |
| unit_abbr | SlugField | Viết tắt đơn vị |
| entity | ForeignKey(EntityModel) | Đơn vị liên quan |
| is_active | BooleanField | Đơn vị đo lường có đang hoạt động |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: UnitOfMeasureModel định nghĩa các đơn vị đo lường được sử dụng để mô tả số lượng của hàng hóa và dịch vụ. Ví dụ: cái, kg, giờ, v.v.

## ItemModel

Đại diện cho một mặt hàng, sản phẩm hoặc dịch vụ.

| Trường | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| name | CharField | Tên mặt hàng |
| uom | ForeignKey(UnitOfMeasureModel) | Đơn vị đo lường |
| sku | CharField | Mã hàng hóa |
| item_type | CharField | Loại (sản phẩm, dịch vụ, v.v.) |
| item_role | CharField | Vai trò (hàng tồn kho, dịch vụ, chi phí) |
| entity | ForeignKey(EntityModel) | Đơn vị liên quan |
| active | BooleanField | Mặt hàng có đang hoạt động |
| inventory_account | ForeignKey(AccountModel) | Tài khoản hàng tồn kho |
| cogs_account | ForeignKey(AccountModel) | Tài khoản giá vốn hàng bán |
| earnings_account | ForeignKey(AccountModel) | Tài khoản doanh thu |
| expense_account | ForeignKey(AccountModel) | Tài khoản chi phí |
| markdown_notes | TextField | Ghi chú định dạng Markdown |
| inventory_received | DecimalField | Số lượng nhận vào |
| inventory_received_value | DecimalField | Giá trị nhận vào |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: ItemModel đại diện cho các mặt hàng, sản phẩm, dịch vụ hoặc chi phí. ItemModel được sử dụng để theo dõi hàng tồn kho và liên kết với các tài khoản thích hợp cho kế toán tự động.

**Điểm đặc biệt**:
- ItemModel có thể có các vai trò khác nhau (hàng tồn kho, dịch vụ, chi phí)
- Mỗi vai trò ItemModel liên kết với các tài khoản khác nhau để kế toán tự động
- ItemModel theo dõi số lượng và giá trị hàng tồn kho

## ItemTransactionModel

Đại diện cho một giao dịch liên quan đến mặt hàng.

| Trường | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| item_model | ForeignKey(ItemModel) | Mặt hàng liên quan |
| entity_unit | ForeignKey(EntityUnitModel) | Đơn vị liên quan |
| document_model | ContentTypeField | Loại chứng từ liên quan |
| document_id | UUIDField | ID của chứng từ liên quan |
| quantity | DecimalField | Số lượng mặt hàng |
| unit_cost | DecimalField | Chi phí đơn vị |
| total_amount | DecimalField | Tổng số tiền |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: ItemTransactionModel liên kết các mặt hàng với các chứng từ như hóa đơn, hóa đơn mua hàng và đơn đặt hàng. Nó lưu trữ thông tin về số lượng và giá trị của mặt hàng trong mỗi giao dịch.

## ImportJobModel

Đại diện cho một công việc nhập dữ liệu vào hệ thống.

| Trường | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| entity | ForeignKey(EntityModel) | Đơn vị liên quan |
| ledger | ForeignKey(LedgerModel) | Sổ cái liên quan (tùy chọn) |
| description | CharField | Mô tả công việc nhập |
| filename | CharField | Tên tệp nhập |
| status | CharField | Trạng thái công việc nhập |
| error | TextField | Thông báo lỗi (nếu có) |
| processed | BooleanField | Công việc đã được xử lý hay chưa |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: ImportJobModel theo dõi các công việc nhập dữ liệu từ bên ngoài (ví dụ: tệp OFX) vào hệ thống. Nó lưu trữ thông tin về trạng thái và kết quả của công việc nhập.

## StagedTransactionModel

Đại diện cho một giao dịch được chuẩn bị từ nguồn bên ngoài trước khi được chuyển thành giao dịch thực.

| Trường | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| import_job | ForeignKey(ImportJobModel) | Công việc nhập liên quan |
| date_posted | DateField | Ngày ghi sổ |
| date_due | DateField | Ngày đến hạn |
| name | CharField | Tên giao dịch |
| description | TextField | Mô tả |
| amount | DecimalField | Số tiền giao dịch |
| tx_type | CharField | Loại giao dịch |
| activity | CharField | Loại hoạt động (hoạt động, đầu tư, tài chính) |
| bundle_split | CharField | Mã nhóm giao dịch |
| stagedid | CharField | ID giao dịch từ nguồn bên ngoài |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: StagedTransactionModel lưu trữ các giao dịch được nhập từ nguồn bên ngoài trước khi được xác nhận và chuyển thành TransactionModel thực. Nó cho phép kiểm tra và xác nhận dữ liệu trước khi đưa vào hệ thống kế toán.
