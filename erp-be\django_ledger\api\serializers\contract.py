from rest_framework import serializers
from django.db import transaction
from django_ledger.models import ContractModel, TienDoThanhToanModel
from django_ledger.api.serializers.tien_do_thanh_toan import TienDoThanhToanModelCreateSerializer
from django_ledger.api.serializers.group import GroupModelSimpleSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.erp import CustomerModelSerializer
class ContractModelSerializer(serializers.ModelSerializer):
    tien_do_thanh_toan = TienDoThanhToanModelCreateSerializer(many=True)

    # Add nested serializers for foreign key fields
    nh_hd1_data = GroupModelSimpleSerializer(source='nh_hd1', read_only=True)
    nh_hd2_data = GroupModelSimpleSerializer(source='nh_hd2', read_only=True)
    nh_hd3_data = GroupModelSimpleSerializer(source='nh_hd3', read_only=True)
    ma_bp_data = BoPhanModelSerializer(source='ma_bp', read_only=True)
    ma_kh_data = CustomerModelSerializer(source='ma_kh', read_only=True)

    # Display human-readable choices
    loai_hd_display = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()

    class Meta:
        model = ContractModel
        fields = [
            'uuid', 'entity_model',
            'ma_hd', 'ten_hd', 'ten_hd2', 'so_hd',
            'ngay_hd', 'ngay_hd1', 'ngay_hd2',
            'loai_hd', 'loai_hd_display', 'tien_nt', 'tien',
            'nh_hd1', 'nh_hd1_data', 'nh_hd2', 'nh_hd2_data', 'nh_hd3', 'nh_hd3_data',
            'ma_bp', 'ma_bp_data', 'ma_kh', 'ma_kh_data',
            'status', 'status_display',
            'tien_do_thanh_toan',
            'created', 'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated', 'loai_hd_display', 'status_display',
                           'nh_hd1_data', 'nh_hd2_data', 'nh_hd3_data', 'ma_bp_data', 'ma_kh_data']

    def validate_tien(self, value):
        """
        Validate contract amount
        """
        if value <= 0:
            raise serializers.ValidationError("Contract amount must be greater than 0")
        return value

    def validate_tien_nt(self, value):
        """
        Validate foreign currency amount
        """
        if value <= 0:
            raise serializers.ValidationError("Foreign currency amount must be greater than 0")
        return value

    def validate(self, data):
        """
        Custom validation for the entire model
        """
        # Validate contract dates
        if data.get('ngay_hd2') and data.get('ngay_hd1'):
            if data['ngay_hd2'] < data['ngay_hd1']:
                raise serializers.ValidationError({
                    "ngay_hd2": "Contract end date cannot be before start date"
                })

        # Validate payment progress total percentage
        tien_do_data = data.get('tien_do_thanh_toan', [])
        if tien_do_data:
            total_ty_le = sum(item['ty_le'] for item in tien_do_data)
            if total_ty_le != 100:
                raise serializers.ValidationError({
                    "tien_do_thanh_toan": "Total payment percentages must equal 100%"
                })

            # Validate payment amounts match contract amount
            total_tien = sum(item['tien'] for item in tien_do_data)
            total_tien_nt = sum(item['tien_nt'] for item in tien_do_data)

            if abs(total_tien - data['tien']) > 0.01:  # Allow small rounding differences
                raise serializers.ValidationError({
                    "tien_do_thanh_toan": "Total payment amounts must equal contract amount"
                })

            if abs(total_tien_nt - data['tien_nt']) > 0.01:  # Allow small rounding differences
                raise serializers.ValidationError({
                    "tien_do_thanh_toan": "Total foreign currency payment amounts must equal contract foreign currency amount"
                })

        return data

    @transaction.atomic
    def create(self, validated_data):
        """
        Create a contract with its payment progress records
        """
        tien_do_data = validated_data.pop('tien_do_thanh_toan')
        contract = ContractModel.objects.create(**validated_data)

        # Create payment progress records
        for tien_do in tien_do_data:
            # Set entity_model from contract
            tien_do['entity_model'] = contract.entity_model
            TienDoThanhToanModel.objects.create(hop_dong=contract, **tien_do)

        return contract

    @transaction.atomic
    def update(self, instance, validated_data):
        """
        Update a contract - payment progress records can only be updated through their own endpoints
        """
        validated_data.pop('tien_do_thanh_toan', None)  # Ignore payment progress updates
        return super().update(instance, validated_data)

    def get_loai_hd_display(self, obj):
        """
        Get the display value for loai_hd field
        """
        return obj.get_loai_hd_display()

    def get_status_display(self, obj):
        """
        Get the display value for status field
        """
        return obj.get_status_display()
