"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the URL patterns for the LoaiTaiSanCongCu API.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.danh_muc.tai_san_va_cong_cu.loai_tai_san_cong_cu import LoaiTaiSanCongCuViewSet

# Main router for LoaiTaiSanCongCu
router = DefaultRouter()
router.register('', LoaiTaiSanCongCuViewSet, basename='loai-tai-san-cong-cu')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
