"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the QuyenHoaDonDienTuSerializer, which handles serialization
for the QuyenHoaDonDienTuModel.
"""

from rest_framework import serializers

from django_ledger.models.he_thong.ket_noi_hddt.quyen_hoa_don_dien_tu import QuyenHoaDonDienTuModel
from django_ledger.api.serializers.entity import EntityModelSerializer


class QuyenHoaDonDienTuSerializer(serializers.ModelSerializer):
    """
    Serializer for QuyenHoaDonDienTuModel.
    """
    entity_model = EntityModelSerializer(read_only=True)

    # Reference data fields
    unit_id_data = serializers.SerializerMethodField()

    class Meta:
        model = QuyenHoaDonDienTuModel
        fields = [
            'uuid',
            'entity_model',
            'ma_nkhddt',
            'ten_nkhddt',
            'ten_nkhddt2',
            'mau_so',
            'ky_hieu',
            'loai_hd',
            'unit_id',
            'unit_id_data',
            'status',
            'created',
            'updated',
            'created_by',
            'updated_by'
        ]
        read_only_fields = ['uuid', 'entity_model', 'created', 'updated', 'created_by', 'updated_by']

    def get_unit_id_data(self, obj):
        """
        Returns the unit data for the unit_id field.
        """
        if obj.unit_id:
            return {
                'uuid': str(obj.unit_id.uuid),
                'name': obj.unit_id.name,
                'slug': obj.unit_id.slug
            }
        return None
