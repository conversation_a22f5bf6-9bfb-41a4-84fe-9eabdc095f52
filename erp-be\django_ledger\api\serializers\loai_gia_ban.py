"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Dedicated Serializer for LoaiGiaBan model.
"""
from rest_framework import serializers

from django_ledger.models import LoaiGiaBan
from django_ledger.api.serializers.base import GlobalModelSerializer

class LoaiGiaBanSerializer(GlobalModelSerializer):
    """
    Serializer class for LoaiGiaBan model that handles data serialization/deserialization.
    Inherits from GlobalModelSerializer for common fields and behaviors.
    """

    class Meta:
        model = LoaiGiaBan
        fields = [
            'uuid',
            'entity_model',
            'ma_loai_gb',
            'ten_loai_gb',
            'ten_loai_gb2',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid','created', 'updated']

    def validate_ma_loai_gb(self, value):
        """
        Validate ma_loai_gb field
        
        Parameters
        ----------
        value : str
            The ma_loai_gb value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Mã loại giá bán không được để trống')
        return value.strip()

    def validate_ten_loai_gb(self, value):
        """
        Validate ten_loai_gb field
        
        Parameters
        ----------
        value : str
            The ten_loai_gb value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Tên loại giá bán không được để trống')
        return value.strip()

    def validate_status(self, value):
        """
        Validate status field
        
        Parameters
        ----------
        value : int
            The status value to validate

        Returns
        -------
        int
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if value not in [0, 1]:
            raise serializers.ValidationError('Trạng thái không hợp lệ')
        return value
