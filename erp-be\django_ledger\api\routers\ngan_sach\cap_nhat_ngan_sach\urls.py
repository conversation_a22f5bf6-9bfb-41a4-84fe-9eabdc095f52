# In g:\Code\erp2\erp-be\django_ledger\api\routers\ngan_sach\cap_nhat_ngan_sach\urls.py
"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Cap Nhat Ngan Sach (Budget Update) module.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.ngan_sach.cap_nhat_ngan_sach import (
    CapNhatNganSachViewSet,
    ChiTietCapNhatNganSachViewSet
)

# Main router for CapNhatNganSach
router = DefaultRouter()
router.register('', CapNhatNganSachViewSet, basename='cap-nhat-ngan-sach')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),

    # Nested routes for chi-tiet-cap-nhat-ngan-sach
    path('<uuid:ngan_sach_uuid>/chi-tiet/', ChiTietCapNhatNganSachViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='chi-tiet-cap-nhat-ngan-sach-list'),

    path('<uuid:ngan_sach_uuid>/chi-tiet/<uuid:uuid>/', ChiTietCapNhatNganSachViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='chi-tiet-cap-nhat-ngan-sach-detail'),
]