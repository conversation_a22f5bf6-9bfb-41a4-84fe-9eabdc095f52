"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuXuatKho (Warehouse Export) serializer implementation.
"""

from rest_framework import serializers

from django_ledger.models.ton_kho.xuat_kho_noi_bo.phieu_xuat_kho import PhieuXuatKhoModel
from django_ledger.api.serializers.ton_kho.xuat_kho_noi_bo.phieu_xuat_kho.chi_tiet_phieu_xuat_kho import ChiTietPhieuXuatKhoModelSerializer

# Import serializers for foreign key fields
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer
from django_ledger.api.serializers.quyen_chung_tu import QuyenChungTuListSerializer
from django_ledger.api.serializers.chung_tu import ChungTuSerializer


class PhieuXuatKhoModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the PhieuXuatKhoModel.

    This serializer handles the conversion between PhieuXuatKhoModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields (entity_model, ma_nk, so_ct, ma_kh, ma_nt)
    - Adds additional fields with "_data" suffix (entity_model_data, ma_nk_data, so_ct_data, ma_kh_data, ma_nt_data)
      that contain the complete nested object data with all available fields
    - When deserializing, accepts UUIDs for reference fields
    """
    # Add nested serializers for foreign key fields
    ma_nk_data = QuyenChungTuListSerializer(source='ma_nk', read_only=True)
    so_ct_data = ChungTuSerializer(source='so_ct', read_only=True)
    ma_kh_data = CustomerModelSerializer(source='ma_kh', read_only=True)
    ma_nt_data = NgoaiTeSerializer(source='ma_nt', read_only=True)

    # Add chi_tiet field for nested serialization
    chi_tiet = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = PhieuXuatKhoModel
        fields = [
            'uuid',
            'entity_model',
            'nguoi_tao',
            'ngay_tao',
            'ma_gd',
            'ma_nk',
            'ma_nk_data',
            'so_ct',
            'so_ct_data',
            'ma_kh',
            'ma_kh_data',
            'ngay_ct',
            'ngay_lct',
            'ma_ngv',
            'unit_id',
            'dien_giai',
            'ma_nt',
            'ma_nt_data',
            'ty_gia',
            't_so_luong',
            't_tien_nt',
            't_tien',
            'status',
            'transfer_yn',
            'chi_tiet',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',  # Auto-set from entity_slug in URL
            'ma_nk_data',
            'so_ct_data',
            'ma_kh_data',
            'ma_nt_data',
            'chi_tiet',
            'created',
            'updated'
        ]

    def get_chi_tiet(self, obj):
        """
        Get the chi_tiet (details) for the PhieuXuatKho.
        This is a method field that returns a list of ChiTietPhieuXuatKho objects.

        Parameters
        ----------
        obj : PhieuXuatKhoModel
            The PhieuXuatKhoModel instance

        Returns
        -------
        list
            List of serialized ChiTietPhieuXuatKho objects
        """
        chi_tiet = obj.chi_tiet_phieu_xuat_kho.all()
        return ChiTietPhieuXuatKhoModelSerializer(chi_tiet, many=True).data
