from rest_framework import serializers
from decimal import Decimal
from django_ledger.api.serializers.base import GlobalModelSerializer
from django_ledger.models.mua_hang.don_mua_hang.don_mua_hang import DonMuaHangModel
from django_ledger.api.serializers.mua_hang.don_mua_hang.chi_tiet_don_mua_hang import ChiTietDonMuaHangSerializer
from django.utils.translation import gettext_lazy as _

class DonMuaHangSerializer(GlobalModelSerializer):
    """
    Serializer for DonMuaHangModel
    """
    chi_tiet = serializers.JSONField(required=False, write_only=True)
    chi_tiet_data = ChiTietDonMuaHangSerializer(source='chi_tiet_set', many=True, read_only=True)
    entity_model = serializers.UUIDField(read_only=True)

    # Nested data fields for reference fields
    ma_ncc_data = serializers.SerializerMethodField(read_only=True)
    ma_nvmh_data = serializers.SerializerMethod<PERSON>ield(read_only=True)
    han_thanh_toan_data = serializers.SerializerMethodField(read_only=True)
    so_ct_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)
    ma_kho_data = serializers.SerializerMethodField(read_only=True)
    phuong_thuc_thanh_toan_data = serializers.SerializerMethodField(read_only=True)
    noi_nhan_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = DonMuaHangModel
        fields = [
            'uuid',
            'entity_model',
            'ma_ncc',
            'ma_ncc_data',
            'mst',
            'ten_ncc',
            'nguoi_lien_he',
            'dia_chi',
            'e_mail',
            'ma_nvmh',
            'ma_nvmh_data',
            'han_thanh_toan',
            'han_thanh_toan_data',
            'dien_giai',
            'so_ct',
            'so_ct_data',
            'ngay_ct',
            'ngay_lct',
            'ngay_hl',
            'so_hd',
            'ma_nt',
            'ma_nt_data',
            'ty_gia',
            'status',
            'chi_tiet',
            'chi_tiet_data',
            'dien_thoai',
            'ma_kho',
            'ma_kho_data',
            'phuong_thuc_thanh_toan',
            'phuong_thuc_thanh_toan_data',
            'noi_nhan',
            'noi_nhan_data',
            'giao_dich',
            'ly_do_huy',
            'ghi_chu',
            't_so_luong',
            't_chi_phi',
            't_tien',
            't_thue',
            't_tt',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated', 'entity_model']

    def validate(self, data):
        """Custom validation for the entire object"""
        # Validate dates
        ngay_ct = data.get('ngay_ct')
        ngay_lct = data.get('ngay_lct')
        ngay_hl = data.get('ngay_hl')

        if ngay_lct and ngay_ct and ngay_lct > ngay_ct:
            raise serializers.ValidationError(
                _("Ngày lập chứng từ không thể sau ngày chứng từ")
            )

        if ngay_ct and ngay_hl and ngay_ct > ngay_hl:
            raise serializers.ValidationError(
                _("Ngày chứng từ không thể sau ngày hiệu lực")
            )

        # Validate ty_gia (exchange rate) is present
        if 'ty_gia' not in data:
            raise serializers.ValidationError(
                _("Tỷ giá là bắt buộc")
            )

        # Validate monetary amounts
        t_tien = data.get('t_tien')
        t_thue = data.get('t_thue')
        t_tt = data.get('t_tt')

        if all(v is not None for v in [t_tien, t_thue, t_tt]):
            t_tien = Decimal(str(t_tien))
            t_thue = Decimal(str(t_thue))
            t_tt = Decimal(str(t_tt))

            if t_tien + t_thue != t_tt:
                raise serializers.ValidationError(
                    _("Tổng thanh toán phải bằng tổng tiền cộng tổng thuế")
                )

        # Set default values
        if 'status' not in data:
            data['status'] = "1"

        return data

    def create(self, validated_data):
        """
        Create a new DonMuaHang instance
        The entity_model will be handled by the service layer
        """
        return super().create(validated_data)

    def update(self, instance, validated_data):
        """
        Update a DonMuaHang instance
        The entity_model will be handled by the service layer
        """
        return super().update(instance, validated_data)

    def get_ma_ncc_data(self, obj):
        """Get nested data for ma_ncc field"""
        if obj.ma_ncc:
            return {
                'uuid': obj.ma_ncc.uuid,
                'ma_kh': obj.ma_ncc.ma_kh,
                'ten_kh': obj.ma_ncc.ten_kh,
                'dia_chi': obj.ma_ncc.dia_chi,
                'dien_thoai': obj.ma_ncc.dien_thoai,
                'e_mail': obj.ma_ncc.e_mail,
                'mst': obj.ma_ncc.mst
            }
        return None

    def get_ma_nvmh_data(self, obj):
        """Get nested data for ma_nvmh field"""
        if obj.ma_nvmh:
            return {
                'uuid': obj.ma_nvmh.uuid,
                'ma_nv': obj.ma_nvmh.ma_nv,
                'ten_nv': obj.ma_nvmh.ten_nv,
                'bo_phan': obj.ma_nvmh.bo_phan_id
            }
        return None

    def get_han_thanh_toan_data(self, obj):
        """Get nested data for han_thanh_toan field"""
        if obj.han_thanh_toan:
            return {
                'uuid': obj.han_thanh_toan.uuid,
                'ma_htt': obj.han_thanh_toan.ma_htt,
                'ten_htt': obj.han_thanh_toan.ten_htt,
                'so_ngay': obj.han_thanh_toan.so_ngay
            }
        return None

    def get_so_ct_data(self, obj):
        """Get nested data for so_ct field"""
        if obj.so_ct:
            return {
                'uuid': obj.so_ct.uuid,
                'ma_quyen': obj.so_ct.ma_quyen,
                'ten_quyen': obj.so_ct.ten_quyen,
                'loai_ct': obj.so_ct.loai_ct
            }
        return None

    def get_ma_nt_data(self, obj):
        """Get nested data for ma_nt field"""
        if obj.ma_nt:
            return {
                'uuid': obj.ma_nt.uuid,
                'ma_nt': obj.ma_nt.ma_nt,
                'ten_nt': obj.ma_nt.ten_nt,
                'ky_hieu': obj.ma_nt.ky_hieu
            }
        return None

    def get_ma_kho_data(self, obj):
        """Get nested data for ma_kho field"""
        if obj.ma_kho:
            return {
                'uuid': obj.ma_kho.uuid,
                'ma_kho': obj.ma_kho.ma_kho,
                'ten_kho': obj.ma_kho.ten_kho,
                'dia_chi': obj.ma_kho.dia_chi
            }
        return None

    def get_phuong_thuc_thanh_toan_data(self, obj):
        """Get nested data for phuong_thuc_thanh_toan field"""
        if obj.phuong_thuc_thanh_toan:
            return {
                'uuid': obj.phuong_thuc_thanh_toan.uuid,
                'ma_pttt': obj.phuong_thuc_thanh_toan.ma_pttt,
                'ten_pttt': obj.phuong_thuc_thanh_toan.ten_pttt
            }
        return None

    def get_noi_nhan_data(self, obj):
        """Get nested data for noi_nhan field"""
        if obj.noi_nhan:
            return {
                'uuid': obj.noi_nhan.uuid,
                'ma_dcnh': obj.noi_nhan.ma_dcnh,
                'ten_dcnh': obj.noi_nhan.ten_dcnh,
                'dia_chi': obj.noi_nhan.dia_chi,
                'dien_thoai': obj.noi_nhan.dien_thoai
            }
        return None
