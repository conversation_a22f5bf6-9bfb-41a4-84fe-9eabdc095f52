"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietCapNhatDeNghiThanhToan model.
"""

from django.utils.translation import gettext_lazy as _
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.danh_muc import (
    ChiPhiKhongHopLeSerializer,
)
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import (
    KheUocModelSerializer,
)
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.danh_muc import PhiSerializer
from django_ledger.api.serializers.tien_do_thanh_toan import DotThanhToanModelSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.models import ChiTietCapNhatDeNghiThanhToanModel
from rest_framework import serializers


class ChiTietCapNhatDeNghiThanhToanSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietCapNhatDeNghiThanhToan model.
    """

    # Read-only fields for related objects
    cap_nhat_de_nghi_thanh_toan_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    tk_no_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietCapNhatDeNghiThanhToanModel
        fields = [
            "uuid",
            "cap_nhat_de_nghi_thanh_toan",
            "cap_nhat_de_nghi_thanh_toan_data",
            "line",
            "ma_ctns",
            "ns_kd",
            "ma_kh",
            "ma_kh_data",
            "id_hd",
            "tk_no",
            "tk_no_data",
            "ty_gia2",
            "tien_nt",
            "tien",
            "dien_giai",
            "ma_loai_hd",
            "ma_thue",
            "ten_thue",
            "thue_suat",
            "tk_thue",
            "tk_thue_data",
            "ten_tk_thue",
            "so_ct0",
            "so_ct2",
            "ngay_ct0",
            "ma_mau_ct",
            "ma_mau_bc",
            "ma_tc_thue",
            "ma_kh_thue",
            "ten_kh_thue",
            "dia_chi",
            "ma_so_thue",
            "ten_vt_thue",
            "thue_nt",
            "thue",
            "ma_kh9",
            "ten_kh9",
            "ghi_chu",
            "ma_bp",
            "ma_bp_data",
            "ma_vv",
            "ma_vv_data",
            "ma_hd",
            "ma_hd_data",
            "ma_dtt",
            "ma_dtt_data",
            "ma_ku",
            "ma_ku_data",
            "ma_phi",
            "ma_phi_data",
            "ma_sp",
            "ma_sp_data",
            "ma_lsx",
            "ma_cp0",
            "ma_cp0_data",
            "id_tt",
            "created_by",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "cap_nhat_de_nghi_thanh_toan",
            "cap_nhat_de_nghi_thanh_toan_data",
            "ma_kh_data",
            "tk_no_data",
            "tk_thue_data",
            "ma_bp_data",
            "created",
            "updated",
        ]

    def get_cap_nhat_de_nghi_thanh_toan_data(self, obj):
        """
        Get parent payment proposal update data.
        """
        if obj.cap_nhat_de_nghi_thanh_toan:
            return {
                "uuid": str(obj.cap_nhat_de_nghi_thanh_toan.uuid),
                "so_ct": obj.cap_nhat_de_nghi_thanh_toan.so_ct,
                "ngay_ct": obj.cap_nhat_de_nghi_thanh_toan.ngay_ct,
            }
        return None

    def get_ma_kh_data(self, obj):
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_tk_no_data(self, obj):
        """
        Get debit account data.
        """
        if obj.tk_no:
            return AccountModelSerializer(obj.tk_no).data
        return None

    def get_tk_thue_data(self, obj):
        """
        Get tax account data.
        """
        if obj.tk_thue:
            return AccountModelSerializer(obj.tk_thue).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):
        """
        Get case data.
        """
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_hd_data(self, obj):
        """
        Get contract data.
        """
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_dtt_data(self, obj):
        """
        Get payment period data.
        """
        if obj.ma_dtt:
            return DotThanhToanModelSerializer(obj.ma_dtt).data
        return None

    def get_ma_ku_data(self, obj):
        """
        Get agreement data.
        """
        if obj.ma_ku:
            return KheUocModelSerializer(obj.ma_ku).data
        return None

    def get_ma_phi_data(self, obj):
        """
        Get fee data.
        """
        if obj.ma_phi:
            return PhiSerializer(obj.ma_phi).data
        return None

    def get_ma_sp_data(self, obj):
        """
        Get product data.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_cp0_data(self, obj):
        """
        Get invalid cost data.
        """
        if obj.ma_cp0:
            return ChiPhiKhongHopLeSerializer(obj.ma_cp0).data
        return None

    def validate(self, attrs):
        """
        Validate the serializer data.
        """
        # Set default values if not provided
        if "tien_nt" not in attrs:
            attrs["tien_nt"] = 0.0

        if "tien" not in attrs:
            attrs["tien"] = 0.0

        if "thue_nt" not in attrs:
            attrs["thue_nt"] = 0.0

        if "thue" not in attrs:
            attrs["thue"] = 0.0

        if "ty_gia2" not in attrs:
            attrs["ty_gia2"] = 1.0

        if "thue_suat" not in attrs:
            attrs["thue_suat"] = 0.0

        return attrs
