"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Bang Ke Nhap <PERSON> (Inventory In/Out Report) API.
"""

from rest_framework import serializers
from decimal import Decimal
from datetime import date


class BangKeNhapXuatKhoRequestSerializer(serializers.Serializer):
    """
    Serializer for Bang Ke Nhap Xuat Kho request parameters.
    Validates all filter parameters from the cURL request.
    """
    
    # Date range filters (required)
    ngay_ct1 = serializers.DateField(
        required=True,
        help_text="Start date for transaction date range (YYYY-MM-DD)"
    )
    ngay_ct2 = serializers.DateField(
        required=True,
        help_text="End date for transaction date range (YYYY-MM-DD)"
    )
    
    # Document number range filters (optional)
    so_ct1 = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Start document number range"
    )
    so_ct2 = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="End document number range"
    )
    
    # Transaction type filter
    ct_nxd = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=10,
        help_text="Import/Export transaction type"
    )
    
    # Warehouse and location filters
    ma_kho = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Warehouse code"
    )
    ma_khon = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Sub-warehouse code"
    )
    ma_vi_tri = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Location code"
    )
    
    # Material filters
    ma_vt = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Material code"
    )
    ma_lo = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Batch/Lot code"
    )
    ma_lvt = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Material category code"
    )
    
    # Material group filters
    nh_vt1 = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=100,
        help_text="Material group level 1"
    )
    nh_vt2 = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=100,
        help_text="Material group level 2"
    )
    nh_vt3 = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=100,
        help_text="Material group level 3"
    )
    
    # Inventory tracking flag
    ton_kho_yn = serializers.BooleanField(
        required=False,
        default=True,
        help_text="Include only inventory-tracked materials"
    )
    
    # Description filter
    dien_giai = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=255,
        help_text="Description filter"
    )
    
    # Business dimension filters
    ma_kh = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Customer code"
    )
    ma_bp = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Department code"
    )
    ma_vv = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Business unit code"
    )
    ma_hd = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Contract code"
    )
    ma_dtt = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Payment term code"
    )
    ma_ku = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Region code"
    )
    ma_phi = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Fee code"
    )
    ma_sp = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Product code"
    )
    ma_lsx = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Production order code"
    )
    ma_cp0 = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Cost center code"
    )
    
    # Account filters
    tk_vt = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=20,
        help_text="Material account filter"
    )
    tk_ht = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=20,
        help_text="Current account filter"
    )
    tk_du = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=20,
        help_text="Balance account filter"
    )
    tk_gv = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=20,
        help_text="Cost of goods sold account filter"
    )
    
    # Transaction code filters
    ma_nx = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=10,
        help_text="Transaction code"
    )
    ma_gd = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=100,
        help_text="Transaction group codes (comma-separated)"
    )
    
    # Standard report parameters
    ma_unit = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Unit UUID filter"
    )
    mau_bc = serializers.IntegerField(
        required=False,
        default=20,
        help_text="Report template number"
    )
    data_analysis_struct = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=255,
        help_text="Data analysis structure"
    )

    def validate(self, data):
        """
        Validate the date range.
        """
        ngay_ct1 = data.get('ngay_ct1')
        ngay_ct2 = data.get('ngay_ct2')
        
        if ngay_ct1 and ngay_ct2 and ngay_ct1 > ngay_ct2:
            raise serializers.ValidationError(
                "Start date (ngay_ct1) must be less than or equal to end date (ngay_ct2)"
            )
        
        return data


class BangKeNhapXuatKhoResponseSerializer(serializers.Serializer):
    """
    Serializer for Bang Ke Nhap Xuat Kho response data.
    Defines all fields that should be returned in the report.
    """
    
    stt = serializers.IntegerField(help_text="Sequential number")
    stt_voucher = serializers.IntegerField(help_text="Voucher line number")
    nhom = serializers.CharField(max_length=10, help_text="Group (NHAP/XUAT)")
    tk_pn2 = serializers.CharField(max_length=50, help_text="Account code")
    id = serializers.UUIDField(help_text="Record UUID")
    unit_id = serializers.IntegerField(help_text="Unit ID")
    ma_ct = serializers.CharField(max_length=50, help_text="Document type code")
    ngay_ct = serializers.DateField(help_text="Transaction date")
    so_ct = serializers.CharField(max_length=50, help_text="Document number")
    ma_kh = serializers.CharField(max_length=50, help_text="Customer code")
    ma_vt = serializers.CharField(max_length=50, help_text="Material code")
    ma_kho = serializers.CharField(max_length=50, help_text="Warehouse code")
    ma_lo = serializers.CharField(max_length=50, help_text="Batch/Lot code")
    ma_vi_tri = serializers.CharField(max_length=50, help_text="Location code")
    sl_nhap = serializers.DecimalField(max_digits=15, decimal_places=3, help_text="Import quantity")
    sl_xuat = serializers.DecimalField(max_digits=15, decimal_places=3, help_text="Export quantity")
    gia = serializers.DecimalField(max_digits=15, decimal_places=2, help_text="Unit price")
    tien_nhap = serializers.DecimalField(max_digits=15, decimal_places=2, help_text="Import amount")
    tien_xuat = serializers.DecimalField(max_digits=15, decimal_places=2, help_text="Export amount")
    dien_giai = serializers.CharField(max_length=255, help_text="Description")
    tk_vt = serializers.CharField(max_length=20, help_text="Material account")
    tk_du = serializers.CharField(max_length=20, help_text="Balance account")
    so_ct0 = serializers.CharField(max_length=50, help_text="Original document number")
    ma_bp = serializers.CharField(max_length=50, help_text="Department code")
    ma_vv = serializers.CharField(max_length=50, help_text="Business unit code")
    ma_hd = serializers.CharField(max_length=50, help_text="Contract code")
    ma_ku = serializers.CharField(max_length=50, help_text="Region code")
    ma_phi = serializers.CharField(max_length=50, help_text="Fee code")
    ma_sp = serializers.CharField(max_length=50, help_text="Product code")
    ma_lsx = serializers.CharField(max_length=50, help_text="Production order code")
    ma_dtt = serializers.CharField(max_length=50, help_text="Payment term code")
    ma_cp0 = serializers.CharField(max_length=50, help_text="Cost center code")
    ma_unit = serializers.CharField(max_length=50, help_text="Unit code")
    ten_vt = serializers.CharField(max_length=255, help_text="Material name")
    dvt = serializers.CharField(max_length=50, help_text="Unit of measure")
    ma_ct_in = serializers.CharField(max_length=50, help_text="Internal document type code")
