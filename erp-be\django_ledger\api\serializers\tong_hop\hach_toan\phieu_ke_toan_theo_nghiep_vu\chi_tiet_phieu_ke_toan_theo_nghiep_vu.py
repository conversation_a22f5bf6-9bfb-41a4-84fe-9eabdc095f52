"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietPhieuKeToaNghiepVu model.
"""
from rest_framework import serializers

from django_ledger.models import ChiTietPhieuKeToaNghiepVuModel
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.chung_tu import ChungTuSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.tien_do_thanh_toan import DotThanhToanModelSerializer
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import KheUocModelSerializer
from django_ledger.api.serializers.danh_muc import PhiSerializer, ChiPhiKhongHopLeSerializer

class ChiTietPhieuKeToaNghiepVuSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietPhieuKeToaNghiepVu model.
    """
    # Read-only fields for related objects
    phieu_ke_toan_theo_nghiep_vu_data = serializers.SerializerMethodField(read_only=True)
    tk_no_data = serializers.SerializerMethodField(read_only=True)
    tk_co_data = serializers.SerializerMethodField(read_only=True)
    tk_cn_no_data = serializers.SerializerMethodField(read_only=True)
    tk_cn_co_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_co_data = serializers.SerializerMethodField(read_only=True)
    so_ct0_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_dtt_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_cp0_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietPhieuKeToaNghiepVuModel
        fields = [
            'uuid',
            'entity_model',
            'phieu_ke_toan_theo_nghiep_vu',
            'phieu_ke_toan_theo_nghiep_vu_data',
            'line',
            'ma_ngvkt',
            'tk_no',
            'tk_no_data',
            'tk_co',
            'tk_co_data',
            'tk_cn_no',
            'tk_cn_no_data',
            'tk_cn_co',
            'tk_cn_co_data',
            'ma_kh',
            'ma_kh_data',
            'ma_kh_co',
            'ma_kh_co_data',
            'ty_gia2',
            'ps_nt',
            'ps',
            'dien_giai',
            'so_ct0',
            'so_ct0_data',
            'ngay_ct0',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_sp_data',
            'ma_lsx',
            'ma_cp0',
            'ma_cp0_data',
            'id_tt',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'phieu_ke_toan_theo_nghiep_vu_data',
            'tk_no_data',
            'tk_co_data',
            'tk_cn_no_data',
            'tk_cn_co_data',
            'ma_kh_data',
            'ma_kh_co_data',
            'so_ct0_data',
            'ma_bp_data',
            'ma_vv_data',
            'ma_hd_data',
            'ma_dtt_data',
            'ma_ku_data',
            'ma_phi_data',
            'ma_sp_data',
            'ma_cp0_data',
            'created',
            'updated'
        ]

    def get_phieu_ke_toan_theo_nghiep_vu_data(self, obj):
        """
        Get parent voucher data.
        """
        if obj.phieu_ke_toan_theo_nghiep_vu:
            return {
                'uuid': str(obj.phieu_ke_toan_theo_nghiep_vu.uuid),
                'i_so_ct': obj.phieu_ke_toan_theo_nghiep_vu.i_so_ct,
                'dien_giai': obj.phieu_ke_toan_theo_nghiep_vu.dien_giai[:50] if obj.phieu_ke_toan_theo_nghiep_vu.dien_giai else None
            }
        return None

    def get_tk_no_data(self, obj):
        """
        Get debit account data.
        """
        if obj.tk_no:
            return AccountModelSerializer(obj.tk_no).data
        return None

    def get_tk_co_data(self, obj):
        """
        Get credit account data.
        """
        if obj.tk_co:
            return AccountModelSerializer(obj.tk_co).data
        return None

    def get_tk_cn_no_data(self, obj):
        """
        Get subsidiary debit account data.
        """
        if obj.tk_cn_no:
            return AccountModelSerializer(obj.tk_cn_no).data
        return None

    def get_tk_cn_co_data(self, obj):
        """
        Get subsidiary credit account data.
        """
        if obj.tk_cn_co:
            return AccountModelSerializer(obj.tk_cn_co).data
        return None

    def get_ma_kh_data(self, obj):
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_ma_kh_co_data(self, obj):
        """
        Get credit customer data.
        """
        if obj.ma_kh_co:
            return CustomerModelSerializer(obj.ma_kh_co).data
        return None

    def get_so_ct0_data(self, obj):
        """
        Get original document data.
        """
        if obj.so_ct0:
            return ChungTuSerializer(obj.so_ct0).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):
        """
        Get case/matter data.
        """
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_hd_data(self, obj):
        """
        Get contract data.
        """
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_dtt_data(self, obj):
        """
        Get payment period data.
        """
        if obj.ma_dtt:
            return DotThanhToanModelSerializer(obj.ma_dtt).data
        return None

    def get_ma_ku_data(self, obj):
        """
        Get agreement data.
        """
        if obj.ma_ku:
            return KheUocModelSerializer(obj.ma_ku).data
        return None

    def get_ma_phi_data(self, obj):
        """
        Get fee data.
        """
        if obj.ma_phi:
            return PhiSerializer(obj.ma_phi).data
        return None

    def get_ma_sp_data(self, obj):
        """
        Get product data.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_cp0_data(self, obj):
        """
        Get invalid expense data.
        """
        if obj.ma_cp0:
            return ChiPhiKhongHopLeSerializer(obj.ma_cp0).data
        return None
