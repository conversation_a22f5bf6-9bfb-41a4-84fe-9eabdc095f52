from django_ledger.api.serializers.unit import EntityUnitModelSimpleSerializer
from django_ledger.models.quyen_chung_tu import (
    QuyenChungTu,
    QuyenChungTuChiTiet,
    QuyenChungTuNgay,
)
from rest_framework import serializers


class QuyenChungTuChiTietSerializer(serializers.ModelSerializer):
    """
    Serializer for the QuyenChungTuChiTiet model.
    """

    quyen_chung_tu_uuid = serializers.UUIDField(
        source="quyen_chung_tu.uuid", read_only=True
    )
    ma_ct_code = serializers.Char<PERSON>ield(source="ma_ct.ma_ct", read_only=True)
    ma_ct_name = serializers.CharField(source="ma_ct.ten_ct", read_only=True)
    user_id_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = QuyenChungTuChiTiet
        fields = [
            "uuid",
            "quyen_chung_tu_uuid",
            "ma_nk",
            "line",
            "ma_ct",
            "ma_ct_code",
            "ma_ct_name",
            "user_id",
            "user_id_data",
            "username",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "quyen_chung_tu_uuid",
            "ma_nk",
            "ma_ct_code",
            "ma_ct_name",
            "user_id_data",
            "created",
            "updated",
        ]
        swagger_schema_fields = {
            "title": "Chi tiết quyền chứng từ",
            "description": "API để quản lý chi tiết quyền chứng từ",
        }

    def get_user_id_data(self, obj):
        """Method field for user_id_data"""
        if obj.user_id:
            from django_ledger.api.serializers.profile import UserProfileSerializer

            return UserProfileSerializer(obj.user_id).data
        return None


class QuyenChungTuNgaySerializer(serializers.ModelSerializer):
    """
    Serializer for the QuyenChungTuNgay model.
    """

    quyen_chung_tu_uuid = serializers.UUIDField(
        source="quyen_chung_tu.uuid", read_only=True
    )

    class Meta:
        model = QuyenChungTuNgay
        fields = [
            "uuid",
            "quyen_chung_tu_uuid",
            "ma_nk",
            "ngay_nk",
            "i_so_ct_ht",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "quyen_chung_tu_uuid",
            "ma_nk",
            "created",
            "updated",
        ]
        swagger_schema_fields = {
            "title": "Quyền chứng từ theo ngày",
            "description": "API để quản lý quyền chứng từ theo ngày",
        }


class QuyenChungTuListSerializer(serializers.ModelSerializer):
    """
    Serializer for listing QuyenChungTu models.
    """

    entity_slug = serializers.SlugField(source="entity_model.slug", read_only=True)
    chi_tiet = QuyenChungTuChiTietSerializer(many=True, read_only=True)
    ngay_count = serializers.SerializerMethodField()
    unit_id_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = QuyenChungTu
        fields = [
            "uuid",
            "entity_slug",
            "ma_nk",
            "ten_nk",
            "ten_nk2",
            "so_ct_mau",
            "number_text",
            "unit_id",
            "unit_id_data",
            "kieu_trung_so",
            "i_so_ct_ht",
            "ngay_hl1",
            "ngay_hl2",
            "so_ct2",
            "status",
            "chi_tiet",
            "ngay_count",
            "created",
            "updated",
        ]
        read_only_fields = ["uuid", "entity_slug", "created", "updated", "unit_id_data"]
        swagger_schema_fields = {
            "title": "Danh sách quyền chứng từ",
            "description": "API để lấy danh sách quyền chứng từ",
        }

    def get_ngay_count(self, obj):
        return obj.ngay.count()

    def get_unit_id_data(self, obj):
        """Method field for unit_id_data"""
        if obj.unit_id:
            return EntityUnitModelSimpleSerializer(obj.unit_id).data
        return None


class QuyenChungTuDetailSerializer(serializers.ModelSerializer):
    """
    Serializer for detailed view of QuyenChungTu model.
    """

    entity_slug = serializers.SlugField(source="entity_model.slug", read_only=True)
    chi_tiet = QuyenChungTuChiTietSerializer(many=True, read_only=True)
    ngay = QuyenChungTuNgaySerializer(many=True, read_only=True)
    unit_id_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = QuyenChungTu
        fields = [
            "uuid",
            "entity_slug",
            "ma_nk",
            "ten_nk",
            "ten_nk2",
            "so_ct_mau",
            "number_text",
            "unit_id",
            "unit_id_data",
            "kieu_trung_so",
            "i_so_ct_ht",
            "ngay_hl1",
            "ngay_hl2",
            "so_ct2",
            "status",
            "chi_tiet",
            "ngay",
            "created",
            "updated",
        ]
        read_only_fields = ["uuid", "entity_slug", "created", "updated", "unit_id_data"]
        swagger_schema_fields = {
            "title": "Chi tiết quyền chứng từ",
            "description": "API để xem chi tiết quyền chứng từ",
        }

    def get_unit_id_data(self, obj):
        """Method field for unit_id_data"""
        if obj.unit_id:
            return EntityUnitModelSimpleSerializer(obj.unit_id).data
        return None


class QuyenChungTuCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating QuyenChungTu models.
    """

    # Thêm trường danh_sach_chung_tu để nhận danh sách mã chứng từ
    danh_sach_chung_tu = serializers.ListField(
        child=serializers.CharField(max_length=50),
        required=False,
        write_only=True,
        help_text="Danh sách mã chứng từ để thêm vào quyển",
    )

    class Meta:
        model = QuyenChungTu
        fields = [
            "ma_nk",
            "ten_nk",
            "ten_nk2",
            "so_ct_mau",
            "number_text",
            "unit_id",
            "kieu_trung_so",
            "i_so_ct_ht",
            "ngay_hl1",
            "ngay_hl2",
            "so_ct2",
            "status",
            "danh_sach_chung_tu",  # Thêm trường này vào danh sách fields
        ]
        swagger_schema_fields = {
            "title": "Tạo/Cập nhật quyền chứng từ",
            "description": "API để tạo hoặc cập nhật quyền chứng từ",
        }

    def validate_danh_sach_chung_tu(self, value):
        """
        Validate that all document types in danh_sach_chung_tu exist.
        """
        if value:
            from django_ledger.models.chung_tu import ChungTu

            for ma_ct in value:
                try:
                    ChungTu.objects.get(ma_ct=ma_ct)
                except ChungTu.DoesNotExist:
                    raise serializers.ValidationError(
                        f"Document type with code '{ma_ct}' not found"
                    )

        return value
