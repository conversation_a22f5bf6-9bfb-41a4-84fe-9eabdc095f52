"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietButToanHuy model.
"""

from rest_framework import serializers
from django_ledger.models import ChiTietButToanHuyModel
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.accounts import AccountModelSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.group import GroupModelSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.tien_do_thanh_toan import DotThanhToanModelSerializer
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import KheUocModelSerializer
from django_ledger.api.serializers.danh_muc import PhiSerializer, ChiPhiKhongHopLeSerializer

class ChiTietButToanHuySerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietButToanHuy model.
    """
    # Read-only fields for related objects
    but_toan_huy_data = serializers.SerializerMethodField(read_only=True)
    tk_data = serializers.SerializerMethodField(read_only=True)
    tk_cn_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    nh_dk_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_dtt_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_cp0_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietButToanHuyModel
        fields = [
            'uuid',
            'entity_model',
            'but_toan_huy',
            'but_toan_huy_data',
            'line',
            'tk',
            'tk_data',
            'tk_cn',
            'tk_cn_data',
            'ma_kh',
            'ma_kh_data',
            'ps_no_nt',
            'ps_co_nt',
            'nh_dk',
            'nh_dk_data',
            'dien_giai',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_sp_data',
            'ma_lsx',
            'ma_cp0',
            'ma_cp0_data',
            'ps_no',
            'ps_co',
            'id_tt',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'but_toan_huy_data',
            'tk_data',
            'tk_cn_data',
            'ma_kh_data',
            'nh_dk_data',
            'ma_bp_data',
            'ma_vv_data',
            'ma_hd_data',
            'ma_dtt_data',
            'ma_ku_data',
            'ma_phi_data',
            'ma_sp_data',
            'ma_cp0_data',
            'created',
            'updated'
        ]

    def get_but_toan_huy_data(self, obj):
        """
        Get parent ButToanHuy data.
        """
        if obj.but_toan_huy:
            return {
                'uuid': str(obj.but_toan_huy.uuid),
                'ma_ngv': obj.but_toan_huy.ma_ngv,
                'dien_giai': obj.but_toan_huy.dien_giai
            }
        return None

    def get_tk_data(self, obj):
        """
        Get main account data.
        """
        if obj.tk:
            return AccountModelSerializer(obj.tk).data
        return None

    def get_tk_cn_data(self, obj):
        """
        Get subsidiary account data.
        """
        if obj.tk_cn:
            return AccountModelSerializer(obj.tk_cn).data
        return None

    def get_ma_kh_data(self, obj):
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_nh_dk_data(self, obj):
        """
        Get group data.
        """
        if obj.nh_dk:
            return GroupModelSerializer(obj.nh_dk).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):
        """
        Get task/case data.
        """
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_hd_data(self, obj):
        """
        Get contract data.
        """
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_dtt_data(self, obj):
        """
        Get payment period data.
        """
        if obj.ma_dtt:
            return DotThanhToanModelSerializer(obj.ma_dtt).data
        return None

    def get_ma_ku_data(self, obj):
        """
        Get agreement data.
        """
        if obj.ma_ku:
            return KheUocModelSerializer(obj.ma_ku).data
        return None

    def get_ma_phi_data(self, obj):
        """
        Get fee data.
        """
        if obj.ma_phi:
            return PhiSerializer(obj.ma_phi).data
        return None

    def get_ma_sp_data(self, obj):
        """
        Get product/material data.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_cp0_data(self, obj):
        """
        Get invalid cost data.
        """
        if obj.ma_cp0:
            return ChiPhiKhongHopLeSerializer(obj.ma_cp0).data
        return None
