"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Customer ERP-related models
"""

from rest_framework import serializers

from django_ledger.models import (
    LoaiGiaBan, NhomKhachHang
)


class LoaiGiaBanSerializer(serializers.ModelSerializer):
    """
    Serializer for the LoaiGiaBan (Sales Price Type) model.

    This serializer handles the conversion between LoaiGiaBan model instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - ma_loai_gb: Primary key and unique code for the sales price type
    - ten_loai_gb: Primary name of the sales price type
    - ten_loai_gb2: Secondary/alternative name (optional)
    - status: Status indicator (0=inactive, >0=active)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    - created_by: Username of creator
    - updated_by: Username of last updater
    """

    class Meta:
        model = LoaiGiaBan
        fields = ['ma_loai_gb', 'ten_loai_gb', 'ten_loai_gb2', 'status',
                  'created', 'updated']
        read_only_fields = ['created', 'updated']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "ma_loai_gb": "BL",
                "ten_loai_gb": "Bán lẻ",
                "ten_loai_gb2": "Retail",
                "status": 1,
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z",
                "created_by": "admin",
                "updated_by": ""
            }
        }


class NhomKhachHangSerializer(serializers.ModelSerializer):
    """
    Serializer for the NhomKhachHang (Customer Group) model.

    This serializer handles the conversion between NhomKhachHang model instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - ma_loai_nh: Code for the customer group type
    - ma_nh: Code for the customer group
    - ten_nh: Primary name of the customer group
    - ten_nh2: Secondary/alternative name (optional)
    - status: Status indicator ('0'=inactive, '1'=active)
    - ngay_tao: Timestamp of creation (read-only)
    - ngay_cap_nhat: Timestamp of last update (read-only)
    """

    class Meta:
        model = NhomKhachHang
        fields = ['ma_loai_nh', 'ma_nh', 'ten_nh', 'ten_nh2', 'status',
                  'ngay_tao', 'ngay_cap_nhat']
        read_only_fields = ['ngay_tao', 'ngay_cap_nhat']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "ma_loai_nh": "KH1",
                "ma_nh": "VIP",
                "ten_nh": "Khách hàng VIP",
                "ten_nh2": "VIP Customer",
                "status": "1",
                "ngay_tao": "2023-04-21T10:30:00Z",
                "ngay_cap_nhat": "2023-04-21T10:30:00Z"
            }
        }
