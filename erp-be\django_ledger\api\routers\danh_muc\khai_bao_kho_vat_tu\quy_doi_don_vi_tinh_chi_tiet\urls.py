"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Quy Doi Don Vi Tinh Chi Tiet (Unit Conversion Detail) module.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.danh_muc.khai_bao_kho_vat_tu.quy_doi_don_vi_tinh_chi_tiet import QuyDoiDonViTinhChiTietViewSet

# Main router for QuyDoiDonViTinhChiTiet
router = DefaultRouter()
router.register('', QuyDoiDonViTinhChiTietViewSet, basename='quy-doi-don-vi-tinh-chi-tiet')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
