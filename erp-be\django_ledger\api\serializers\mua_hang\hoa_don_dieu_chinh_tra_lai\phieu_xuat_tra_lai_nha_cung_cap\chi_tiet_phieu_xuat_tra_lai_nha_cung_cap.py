"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for ChiTietPhieuXuatTraLaiNhaCungCap (Supplier Return Note Detail) model.
"""

from rest_framework import serializers

# Import các serializer cần thiết cho các trường _data
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import KheUocModelSerializer
from django_ledger.api.serializers.lo import LoModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.danh_muc import PhiSerializer
from django_ledger.api.serializers.tien_do_thanh_toan import DotThanhToanModelSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.vi_tri import ViTriModelSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.api.serializers.warehouse import KhoHangModelSerializer

# Import model
from django_ledger.models.mua_hang.hoa_don_dieu_chinh_tra_lai.phieu_xuat_tra_lai_nha_cung_cap import ChiTietPhieuXuatTraLaiNhaCungCapModel


class ChiTietPhieuXuatTraLaiNhaCungCapSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietPhieuXuatTraLaiNhaCungCap model
    """
    phieu_xuat_tra_lai_uuid = serializers.UUIDField(source='phieu_xuat_tra_lai.uuid', read_only=True)

    # Reference data fields
    ma_vt_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)
    ma_kho_data = serializers.SerializerMethodField(read_only=True)
    ma_lo_data = serializers.SerializerMethodField(read_only=True)
    ma_vi_tri_data = serializers.SerializerMethodField(read_only=True)
    tk_vt_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_dtt_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)

    def get_ma_vt_data(self, obj):
        """Return product data if available"""
        if obj.ma_vt:
            return VatTuSerializer(obj.ma_vt).data
        return None

    def get_dvt_data(self, obj):
        """Return unit data if available"""
        if obj.dvt:
            return DonViTinhSerializer(obj.dvt).data
        return None

    def get_ma_kho_data(self, obj):
        """Return warehouse data if available"""
        if obj.ma_kho:
            return KhoHangModelSerializer(obj.ma_kho).data
        return None

    def get_ma_lo_data(self, obj):
        """Return lot data if available"""
        if obj.ma_lo:
            return LoModelSerializer(obj.ma_lo).data
        return None

    def get_ma_vi_tri_data(self, obj):
        """Return position data if available"""
        if obj.ma_vi_tri:
            return ViTriModelSerializer(obj.ma_vi_tri).data
        return None

    def get_tk_vt_data(self, obj):
        """Return account data if available"""
        if obj.tk_vt:
            return AccountModelSerializer(obj.tk_vt).data
        return None

    def get_ma_bp_data(self, obj):
        """Return department data if available"""
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):
        """Return case data if available"""
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_hd_data(self, obj):
        """Return contract data if available"""
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_dtt_data(self, obj):
        """Return payment progress data if available"""
        if obj.ma_dtt:
            return DotThanhToanModelSerializer(obj.ma_dtt).data
        return None

    def get_ma_ku_data(self, obj):
        """Return loan data if available"""
        if obj.ma_ku:
            return KheUocModelSerializer(obj.ma_ku).data
        return None

    def get_ma_phi_data(self, obj):
        """Return fee data if available"""
        if obj.ma_phi:
            return PhiSerializer(obj.ma_phi).data
        return None

    class Meta:
        model = ChiTietPhieuXuatTraLaiNhaCungCapModel
        fields = [
            'uuid',
            'phieu_xuat_tra_lai_uuid',
            'line',
            'ma_vt',
            'ma_vt_data',
            'dvt',
            'dvt_data',
            'ten_dvt',
            'ma_kho',
            'ma_kho_data',
            'ten_kho',
            'ma_lo',
            'ma_lo_data',
            'ten_lo',
            'lo_yn',
            'ma_vi_tri',
            'ma_vi_tri_data',
            'ten_vi_tri',
            'vi_tri_yn',
            'he_so',
            'qc_yn',
            'so_luong',
            'gia_nt',
            'tien_nt',
            'thue_nt',
            'tk_vt',
            'tk_vt_data',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_lsx',
            'ma_cp0',
            'gia',
            'tien',
            'thue',
            'created',
            'updated',
        ]
        read_only_fields = [
            'uuid',
            'phieu_xuat_tra_lai_uuid',
            'created',
            'updated',
        ]


class ChiTietPhieuXuatTraLaiNhaCungCapCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating ChiTietPhieuXuatTraLaiNhaCungCap model
    """
    class Meta:
        model = ChiTietPhieuXuatTraLaiNhaCungCapModel
        fields = [
            'line',
            'ma_vt',
            'dvt',
            'ten_dvt',
            'ma_kho',
            'ten_kho',
            'ma_lo',
            'ten_lo',
            'lo_yn',
            'ma_vi_tri',
            'ten_vi_tri',
            'vi_tri_yn',
            'he_so',
            'qc_yn',
            'so_luong',
            'gia_nt',
            'tien_nt',
            'thue_nt',
            'tk_vt',
            'ma_bp',
            'ma_vv',
            'ma_hd',
            'ma_dtt',
            'ma_ku',
            'ma_phi',
            'ma_sp',
            'ma_lsx',
            'ma_cp0',
            'gia',
            'tien',
            'thue',
        ]


class ChiTietPhieuXuatTraLaiNhaCungCapNestedSerializer(serializers.ModelSerializer):
    """
    Serializer for nested ChiTietPhieuXuatTraLaiNhaCungCap model in PhieuXuatTraLaiNhaCungCap
    """
    class Meta:
        model = ChiTietPhieuXuatTraLaiNhaCungCapModel
        fields = [
            'line',
            'ma_vt',
            'dvt',
            'ten_dvt',
            'ma_kho',
            'ten_kho',
            'ma_lo',
            'ten_lo',
            'lo_yn',
            'ma_vi_tri',
            'ten_vi_tri',
            'vi_tri_yn',
            'he_so',
            'qc_yn',
            'so_luong',
            'gia_nt',
            'tien_nt',
            'thue_nt',
            'tk_vt',
            'ma_bp',
            'ma_vv',
            'ma_hd',
            'ma_dtt',
            'ma_ku',
            'ma_phi',
            'ma_sp',
            'ma_lsx',
            'ma_cp0',
            'gia',
            'tien',
            'thue',
        ]
