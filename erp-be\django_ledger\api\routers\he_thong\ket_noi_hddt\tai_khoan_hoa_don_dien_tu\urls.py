"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL Configuration for Tai Khoan Hoa Don Dien Tu (Electronic Invoice Account) API.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.he_thong.ket_noi_hddt.tai_khoan_hoa_don_dien_tu import TaiKhoanHoaDonDienTuViewSet

router = DefaultRouter()
router.register('', TaiKhoanHoaDonDienTuViewSet, basename='tai-khoan-hoa-don-dien-tu')

urlpatterns = [
    path('', include(router.urls))
]
