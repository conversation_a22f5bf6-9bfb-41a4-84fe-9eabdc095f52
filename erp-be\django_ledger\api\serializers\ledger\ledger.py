from django_ledger.models.ledger.ledger import LedgerModel
from rest_framework import serializers


class LedgerModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = LedgerModel
        fields = [
            "uuid",
            "ledger_xid",
            "name",
            "entity",
            "posted",
            "locked",
            "hidden",
            "additional_info",
            "created",
            "updated",
        ]
        read_only_fields = ["uuid", "created", "updated"]


class LedgerModelCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = LedgerModel
        fields = ["ledger_xid", "name", "entity", "additional_info"]


class LedgerModelUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = LedgerModel
        fields = ["name", "additional_info"]
