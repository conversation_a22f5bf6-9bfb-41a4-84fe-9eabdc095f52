"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Khai Bao He So Phan Bo TSCD (Fixed Asset Depreciation Coefficient Declaration) module.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.tai_san.tinh_khau_hao_tscd.khai_bao_he_so_phan_bo_tscd import KhaiBaoHeSoPhanBoTSCDViewSet

# Main router for KhaiBaoHeSoPhanBoTSCD
router = DefaultRouter()
router.register('', KhaiBaoHeSoPhanBoTSCDViewSet, basename='khai-bao-he-so-phan-bo-tscd')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
