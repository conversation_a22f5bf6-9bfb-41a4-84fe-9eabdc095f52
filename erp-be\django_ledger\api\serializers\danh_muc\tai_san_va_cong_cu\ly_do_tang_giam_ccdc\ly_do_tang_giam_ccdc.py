"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for LyDoTangGiamCCDC model.
"""
from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models.danh_muc import LyDoTangGiamCCDCModel



class LyDoTangGiamCCDCSerializer(serializers.ModelSerializer):
    """
    Serializer for LyDoTangGiamCCDC model.
    """
    # Read-only fields for related objects


    class Meta:
        model = LyDoTangGiamCCDCModel
        fields = [
            'uuid',
            'entity_model',
            'loai_tg_cc',
            'ma_tg_cc',
            'ten_tg_cc',
            'ten_tg_cc2',
            'status',
            'created_by',
            'updated_by',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'created',
            'updated'
        ]

   


