"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Khai Bao Thoi Khau Hao TSCD (Fixed Asset Depreciation Suspension Declaration) module.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.tai_san.dung_khau_hao_tscd.khai_bao_thoi_khau_hao_tscd import KhaiBaoThoiKhauHaoTSCDViewSet

# Main router for KhaiBaoThoiKhauHaoTSCD
router = DefaultRouter()
router.register('', KhaiBaoThoiKhauHaoTSCDViewSet, basename='khai-bao-thoi-khau-hao-tscd')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
