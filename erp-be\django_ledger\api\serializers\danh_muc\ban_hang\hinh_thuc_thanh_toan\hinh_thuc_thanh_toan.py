"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Payment Method (Hình thức thanh toán) API
"""

from rest_framework import serializers

from django_ledger.models.danh_muc.ban_hang.hinh_thuc_thanh_toan import HinhThucThanhToanModel
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.unit import EntityUnitModelSimpleSerializer
from django_ledger.api.serializers.chung_tu import ChungTuSerializer
from django_ledger.models.chung_tu import ChungTu
from django_ledger.models.quyen_chung_tu import QuyenChungTu
from django_ledger.api.serializers.quyen_chung_tu import QuyenChungTuListSerializer


class HinhThucThanhToanModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the HinhThucThanhToanModel
    """
    entity_model = EntityModelSerializer(read_only=True)

    # Add data fields for foreign key relationships
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    ma_ct_data = serializers.SerializerMethodField(read_only=True)
    ma_nk_data = serializers.SerializerMethodField(read_only=True)

    def get_unit_id_data(self, obj):
        """Method field for unit_id_data"""
        if obj.unit_id:
            return EntityUnitModelSimpleSerializer(obj.unit_id).data
        return None

    def get_ma_ct_data(self, obj):
        """Method field for ma_ct_data"""
        if obj.ma_ct:
            return ChungTuSerializer(obj.ma_ct).data
        return None

    def get_ma_nk_data(self, obj):
        """Method field for ma_nk_data"""
        if obj.ma_nk:
            return QuyenChungTuListSerializer(obj.ma_nk).data
        return None

    class Meta:
        model = HinhThucThanhToanModel
        fields = [
            'uuid',
            'entity_model',
            'unit_id',
            'unit_id_data',
            'ma_ct',
            'ma_ct_data',
            'ma_nk',
            'ma_nk_data',
            'ma_httt',
            'ten_httt',
            'ten_httt2',
            'tknh',
            'tk',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'unit_id_data',
            'ma_ct_data',
            'ma_nk_data',
            'created',
            'updated'
        ]
