"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Tax Information serializer implementation.
"""

from django_ledger.models.journal_entry import TaxInformationModel
from rest_framework import serializers


class TaxInformationSerializer(serializers.ModelSerializer):
    """
    Serializer for TaxInformationModel.
    """

    class Meta:
        model = TaxInformationModel
        fields = [
            "uuid",
            "phieu_ke_toan",
            "line",
            "so_ct0",
            "so_ct2",
            "ngay_ct0",
            "ma_thue",
            "thue_suat",
            "ma_mau_ct",
            "ma_mau_bc",
            "ma_tc_thue",
            "ma_kh",
            "ten_kh_thue",
            "dia_chi",
            "ma_so_thue",
            "ten_vt_thue",
            "t_tien_nt",
            "t_tien",
            "tk_thue_no",
            "ten_tk_thue_no",
            "tk_du",
            "ten_tk_du",
            "t_thue_nt",
            "t_thue",
            "ma_kh9",
            "ten_kh9",
            "ma_tt",
            "ten_tt",
            "ghi_chu",
            "id_tt",
            "ma_bp",
            "ma_vv",
            "ma_hd",
            "ma_dtt",
            "ma_ku",
            "ma_phi",
            "ma_sp",
            "ma_lsx",
            "ma_cp0",
        ]
