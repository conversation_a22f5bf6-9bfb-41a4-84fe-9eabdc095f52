"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for Bang Ke Hoa Don Chung Tu Hang Hoa Dich Vu Ban Ra (Tax Invoice Report) API endpoints.
"""

from django.urls import path
from django.http import JsonResponse
from django_ledger.api.views.thue.bang_ke_thue.bang_ke_hoa_don_chung_tu_hang_hoa_dich_vu_ban_ra import BangKeHoaDonChungTuHangHoaDichVuBanRaViewSet

def test_endpoint(request, entity_slug):
    """Simple test endpoint to verify URL routing works."""
    return JsonResponse({
        "message": "Bang Ke Hoa Don Chung Tu Hang Hoa Dich Vu Ban Ra API endpoint is working!",
        "method": request.method,
        "path": request.path,
        "entity_slug": entity_slug
    })

# URL patterns - Single endpoint for tax invoice report with filters as POST body data
urlpatterns = [
    # Test endpoint to verify routing
    path("test/", test_endpoint, name="bang-ke-hoa-don-test"),

    # Tax Invoice Report endpoint - returns report directly with filter POST body data
    path("", BangKeHoaDonChungTuHangHoaDichVuBanRaViewSet.as_view({"post": "get_report"}), name="bang-ke-hoa-don-chung-tu-hang-hoa-dich-vu-ban-ra-report"),
]
