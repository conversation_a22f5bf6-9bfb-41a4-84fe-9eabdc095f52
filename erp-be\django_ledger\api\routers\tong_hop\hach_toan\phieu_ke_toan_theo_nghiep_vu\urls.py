"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for PhieuKeToaNghiepVu API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.tong_hop.hach_toan.phieu_ke_toan_theo_nghiep_vu import (
    PhieuKeToaNghiepVuViewSet,
    ChiTietPhieuKeToaNghiepVuViewSet,
    ThuePhieuKeToaNghiepVuViewSet
)

# Main router for PhieuKeToaNghiepVu
router = DefaultRouter()
router.register('', PhieuKeToaNghiepVuViewSet, basename='phieu-ke-toan-theo-nghiep-vu')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),

    # Nested routes for phieu-ke-toan-theo-nghiep-vu
    path('<uuid:phieu_ke_toan_theo_nghiep_vu_uuid>/', include([
        # Chi tiet phieu ke toan theo nghiep vu routes
        path('chi-tiet-phieu-ke-toan-theo-nghiep-vu/', ChiTietPhieuKeToaNghiepVuViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-tiet-phieu-ke-toan-theo-nghiep-vu-list'),

        path('chi-tiet-phieu-ke-toan-theo-nghiep-vu/<uuid:uuid>/', ChiTietPhieuKeToaNghiepVuViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-tiet-phieu-ke-toan-theo-nghiep-vu-detail'),

        # Thue phieu ke toan theo nghiep vu routes
        path('thue-phieu-ke-toan-theo-nghiep-vu/', ThuePhieuKeToaNghiepVuViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='thue-phieu-ke-toan-theo-nghiep-vu-list'),

        path('thue-phieu-ke-toan-theo-nghiep-vu/<uuid:uuid>/', ThuePhieuKeToaNghiepVuViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='thue-phieu-ke-toan-theo-nghiep-vu-detail'),
    ])),
]
