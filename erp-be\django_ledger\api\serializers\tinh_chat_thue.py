"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for TinhChatThueModel (Tax Nature)
"""

from rest_framework import serializers

from django_ledger.api.serializers.base import GlobalModelSerializer
from django_ledger.models import TinhChatThueModel


class TinhChatThueModelSerializer(GlobalModelSerializer):
    """
    Serializer for the TinhChatThueModel (Tax Nature) model.

    This serializer handles the conversion between TinhChatThueModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - ma_tc_thue: Unique code for the tax nature
    - ten_tc_thue: Name of the tax nature
    - ten_tc_thue2: Alternative name for the tax nature (optional)
    - status: Status indicator ('0'=inactive, '1'=active)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    """

    class Meta:
        model = TinhChatThueModel
        fields = ['uuid', 'entity_model', 'ma_tc_thue', 'ten_tc_thue', 'ten_tc_thue2', 'status',
                  'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_tc_thue": "KHAU_TRU",
                "ten_tc_thue": "Khấu trừ",
                "ten_tc_thue2": "Deductible",
                "status": "1",
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z"
            }
        }
