"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for KhaiBaoHeSoPhanBoTSCD model.
"""
from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models import KhaiBaoHeSoPhanBoTSCDModel
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.danh_muc import BoPhanSuDungTSSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.danh_muc import PhiSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.tai_san.khai_bao_tang_giam_tscd.khai_bao_thong_tin_tai_san_co_dinh import KhaiBaoThongTinTaiSanCoDinhSerializer


class KhaiBaoHeSoPhanBoTSCDSerializer(serializers.ModelSerializer):
    """
    Serializer for the KhaiBaoHeSoPhanBoTSCDModel (Fixed Asset Depreciation Coefficient Declaration) model.

    This serializer handles the conversion between KhaiBaoHeSoPhanBoTSCDModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields (tk_kh, tk_cp, ma_bp, etc.)
    - Adds additional fields with "_data" suffix (tk_kh_data, tk_cp_data, ma_bp_data, etc.)
      that contain the complete nested object data with all available fields
    - When deserializing, accepts UUIDs for reference fields

    Fields:
    - uuid: Unique identifier (read-only)
    - entity_model: Reference to the entity (ForeignKey to EntityModel)
    - ky: Depreciation period
    - nam: Depreciation year
    - ma_ts: Reference to the fixed asset (ForeignKey to KhaiBaoThongTinTaiSanCoDinhModel)
    - ma_ts_data: Full asset data (nested KhaiBaoThongTinTaiSanCoDinhSerializer)
    - he_so: Depreciation coefficient
    - tk_kh: Reference to the depreciation account (ForeignKey to AccountModel)
    - tk_kh_data: Full account data (nested AccountModelSerializer)
    - tk_cp: Reference to the expense account (ForeignKey to AccountModel)
    - tk_cp_data: Full account data (nested AccountModelSerializer)
    - ma_bp_ts: Reference to the asset management department (ForeignKey to BoPhanSuDungTSModel)
    - ma_bp_ts_data: Full department data (nested BoPhanSuDungTSModelSerializer)
    - ma_bp: Reference to the usage department (ForeignKey to BoPhanModel)
    - ma_bp_data: Full department data (nested BoPhanModelSerializer)
    - ma_vv: Reference to the task/job (ForeignKey to VuViecModel)
    - ma_vv_data: Full task data (nested VuViecModelSerializer)
    - ma_sp: Reference to the product (ForeignKey to VatTuModel)
    - ma_sp_data: Full product data (nested VatTuSerializer)
    - ma_phi: Reference to the fee (ForeignKey to PhiModel)
    - ma_phi_data: Full fee data (nested PhiSerializer)
    - ma_hd: Reference to the contract (ForeignKey to ContractModel)
    - ma_hd_data: Full contract data (nested ContractModelSerializer)
    - created: Creation timestamp (read-only)
    - updated: Update timestamp (read-only)
    """

    # Related data fields using SerializerMethodField
    ma_ts_data = serializers.SerializerMethodField(read_only=True)
    tk_kh_data = serializers.SerializerMethodField(read_only=True)
    tk_cp_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_ts_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = KhaiBaoHeSoPhanBoTSCDModel
        fields = [
            'uuid',
            'entity_model',
            'ky',
            'nam',
            'ma_ts',
            'ma_ts_data',
            'he_so',
            'tk_kh',
            'tk_kh_data',
            'tk_cp',
            'tk_cp_data',
            'ma_bp_ts',
            'ma_bp_ts_data',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_sp',
            'ma_sp_data',
            'ma_phi',
            'ma_phi_data',
            'ma_hd',
            'ma_hd_data',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'entity_model', 'created', 'updated']

    def get_ma_ts_data(self, instance):
        """
        Get fixed asset information data
        """
        if instance.ma_ts:
            return KhaiBaoThongTinTaiSanCoDinhSerializer(instance.ma_ts).data
        return None

    def get_tk_kh_data(self, instance):
        """
        Get depreciation account data
        """
        if instance.tk_kh:
            return AccountModelSerializer(instance.tk_kh).data
        return None

    def get_tk_cp_data(self, instance):
        """
        Get expense account data
        """
        if instance.tk_cp:
            return AccountModelSerializer(instance.tk_cp).data
        return None

    def get_ma_bp_ts_data(self, instance):
        """
        Get asset management department data
        """
        if instance.ma_bp_ts:
            return BoPhanSuDungTSSerializer(instance.ma_bp_ts).data
        return None

    def get_ma_bp_data(self, instance):
        """
        Get usage department data
        """
        if instance.ma_bp:
            return BoPhanModelSerializer(instance.ma_bp).data
        return None

    def get_ma_vv_data(self, instance):
        """
        Get task/job data
        """
        if instance.ma_vv:
            return VuViecModelSerializer(instance.ma_vv).data
        return None

    def get_ma_sp_data(self, instance):
        """
        Get product data
        """
        if instance.ma_sp:
            return VatTuSerializer(instance.ma_sp).data
        return None

    def get_ma_phi_data(self, instance):
        """
        Get fee data
        """
        if instance.ma_phi:
            return PhiSerializer(instance.ma_phi).data
        return None

    def get_ma_hd_data(self, instance):
        """
        Get contract data
        """
        if instance.ma_hd:
            return ContractModelSerializer(instance.ma_hd).data
        return None

    def validate_ky(self, value):
        """
        Validate period field
        """
        if value < 1 or value > 12:
            raise serializers.ValidationError(_("Period must be between 1 and 12"))
        return value

    def validate_nam(self, value):
        """
        Validate year field
        """
        if value < 1900 or value > 9999:
            raise serializers.ValidationError(_("Year must be between 1900 and 9999"))
        return value

    def validate_he_so(self, value):
        """
        Validate depreciation coefficient
        """
        if value <= 0:
            raise serializers.ValidationError(_("Depreciation coefficient must be greater than 0"))
        return value

    def validate(self, attrs):
        """
        Validate the entire object
        """
        # Check required fields for non-nullable foreign keys
        if 'ma_ts' not in attrs or attrs['ma_ts'] is None:
            raise serializers.ValidationError({'ma_ts': _("Fixed asset is required")})

        if 'tk_kh' not in attrs or attrs['tk_kh'] is None:
            raise serializers.ValidationError({'tk_kh': _("Depreciation account is required")})

        if 'tk_cp' not in attrs or attrs['tk_cp'] is None:
            raise serializers.ValidationError({'tk_cp': _("Expense account is required")})

        return attrs
