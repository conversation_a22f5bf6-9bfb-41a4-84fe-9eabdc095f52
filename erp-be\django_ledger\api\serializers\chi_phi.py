"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for ChiPhi model.
"""

from rest_framework import serializers

from django_ledger.models import ChiPhiModel
from django_ledger.models.chung_tu import ChungTu
from django_ledger.api.serializers.chung_tu import ChungTuSerializer


class ChiPhiModelSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiPhiModel.
    """
    truoc_hq_yn = serializers.BooleanField(required=False, default=False)
    ma_ct_data = serializers.SerializerMethodField(read_only=True)

    def get_ma_ct_data(self, obj):
        """
        Method field for ma_ct_data that returns the full ChungTu object data
        """
        if obj.ma_ct:
            return ChungTuSerializer(obj.ma_ct).data
        return None

    class Meta:
        model = ChiPhiModel
        fields = [
            'uuid',
            'entity_model',
            'ma_cp',
            'truoc_hq_yn',
            'ten_cp',
            'ten_cp2',
            'loai_cp',
            'loai_pb',
            'ma_ct',
            'ma_ct_data',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated', 'entity_model']
        swagger_schema_fields = {
            'title': 'ChiPhi',
            'description': 'Chi phí model serializer'
        }

    def validate_ma_cp(self, value):
        """
        Validate ma_cp field

        Parameters
        ----------
        value : str
            The ma_cp value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Mã chi phí không được để trống')
        return value.strip()

    def validate_ten_cp(self, value):
        """
        Validate ten_cp field

        Parameters
        ----------
        value : str
            The ten_cp value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Tên chi phí không được để trống')
        return value.strip()

    def validate_loai_cp(self, value):
        """
        Validate loai_cp field

        Parameters
        ----------
        value : str
            The loai_cp value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Loại chi phí không được để trống')
        return value.strip()

    def validate_loai_pb(self, value):
        """
        Validate loai_pb field

        Parameters
        ----------
        value : str
            The loai_pb value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Loại phân bổ không được để trống')
        return value.strip()

    def validate_ma_ct(self, value):
        """
        Validate ma_ct field

        Parameters
        ----------
        value : ChungTu
            The ma_ct value to validate (ChungTu instance)

        Returns
        -------
        ChungTu
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        # Since ma_ct is now a ForeignKey that can be null, we don't need to validate it's not empty
        return value
