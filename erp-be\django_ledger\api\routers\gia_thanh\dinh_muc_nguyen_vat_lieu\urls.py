"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for DinhMucNguyenVatLieu API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.gia_thanh.dinh_muc_nguyen_vat_lieu import (
    DinhMucNguyenVatLieuViewSet,
    ChiTietDinhMucNguyenVatLieuViewSet
)

# Main router for DinhMucNguyenVatLieu
router = DefaultRouter()
router.register('', DinhMucNguyenVatLieuViewSet, basename='dinh-muc-nguyen-vat-lieu')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),

    # Nested routes for dinh-muc-nguyen-vat-lieu
    path('<uuid:dinh_muc_uuid>/', include([
        # Chi tiet dinh muc nguyen vat lieu routes
        path('chi-tiet/', ChiTietDinhMucNguyenVatLieuViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-tiet-dinh-muc-nguyen-vat-lieu-list'),

        path('chi-tiet/<uuid:uuid>/', ChiTietDinhMucNguyenVatLieuViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-tiet-dinh-muc-nguyen-vat-lieu-detail'),
    ])),
]
