"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiTietPhieuYeuCauXuatKho (Warehouse Export Request Detail) serializer implementation.
"""

from rest_framework import serializers

from django_ledger.models.ton_kho.xuat_kho_noi_bo.phieu_yeu_cau_xuat_kho import ChiTietPhieuYeuCauXuatKhoModel

# Import serializers for foreign key fields
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.warehouse import KhoHangModelSerializer
from django_ledger.api.serializers.lo import LoModelSerializer
from django_ledger.api.serializers.vi_tri import ViTriModelSerializer
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.nhap_xuat import NhapXuatModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.tien_do_thanh_toan import DotThanhToanModelSerializer
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import KheUocModelSerializer
from django_ledger.api.serializers.danh_muc import PhiSerializer, ChiPhiKhongHopLeSerializer


class ChiTietPhieuYeuCauXuatKhoModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the ChiTietPhieuYeuCauXuatKhoModel.

    This serializer handles the conversion between ChiTietPhieuYeuCauXuatKhoModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields
    - Adds additional fields with "_data" suffix that contain the complete nested object data
    - When deserializing, accepts UUIDs for reference fields
    """
    # Add nested serializers for foreign key fields
    phieu_yeu_cau_xuat_kho_data = serializers.SerializerMethodField(read_only=True)
    ma_vt_data = VatTuSerializer(source='ma_vt', read_only=True)
    dvt_data = DonViTinhSerializer(source='dvt', read_only=True)
    ma_kho_data = KhoHangModelSerializer(source='ma_kho', read_only=True)
    ma_lo_data = LoModelSerializer(source='ma_lo', read_only=True)
    ma_vi_tri_data = ViTriModelSerializer(source='ma_vi_tri', read_only=True)
    tk_vt_data = AccountModelSerializer(source='tk_vt', read_only=True)
    ma_nx_data = NhapXuatModelSerializer(source='ma_nx', read_only=True)
    tk_du_data = AccountModelSerializer(source='tk_du', read_only=True)
    ma_bp_data = BoPhanModelSerializer(source='ma_bp', read_only=True)
    ma_vv_data = VuViecModelSerializer(source='ma_vv', read_only=True)
    ma_hd_data = ContractModelSerializer(source='ma_hd', read_only=True)
    ma_dtt_data = DotThanhToanModelSerializer(source='ma_dtt', read_only=True)
    ma_ku_data = KheUocModelSerializer(source='ma_ku', read_only=True)
    ma_phi_data = PhiSerializer(source='ma_phi', read_only=True)
    ma_sp_data = VatTuSerializer(source='ma_sp', read_only=True)
    ma_cp0_data = ChiPhiKhongHopLeSerializer(source='ma_cp0', read_only=True)

    class Meta:
        model = ChiTietPhieuYeuCauXuatKhoModel
        fields = [
            'uuid',
            'phieu_yeu_cau_xuat_kho',
            'phieu_yeu_cau_xuat_kho_data',
            'line',
            'ma_vt',
            'ma_vt_data',
            'dvt',
            'dvt_data',
            'ma_kho',
            'ma_kho_data',
            'ma_lo',
            'ma_lo_data',
            'lo_yn',
            'ma_vi_tri',
            'ma_vi_tri_data',
            'vi_tri_yn',
            'he_so',
            'qc_yn',
            'so_luong',
            'px_dd',
            'gia_nt',
            'tien_nt',
            'gia',
            'tien',
            'tk_vt',
            'tk_vt_data',
            'ma_nx',
            'ma_nx_data',
            'tk_du',
            'tk_du_data',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_sp_data',
            'ma_lsx',
            'ma_cp0',
            'ma_cp0_data',
            'sl_px',
            'id_px',
            'line_px',
            'id_yc',
            'line_yc',
            'id_nhap',
            'line_nhap',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'phieu_yeu_cau_xuat_kho_data',
            'ma_vt_data',
            'dvt_data',
            'ma_kho_data',
            'ma_lo_data',
            'ma_vi_tri_data',
            'tk_vt_data',
            'ma_nx_data',
            'tk_du_data',
            'ma_bp_data',
            'ma_vv_data',
            'ma_hd_data',
            'ma_dtt_data',
            'ma_ku_data',
            'ma_phi_data',
            'ma_sp_data',
            'ma_cp0_data',
            'created',
            'updated'
        ]

    def get_phieu_yeu_cau_xuat_kho_data(self, obj):
        """
        Get the phieu_yeu_cau_xuat_kho data for the ChiTietPhieuYeuCauXuatKho.
        This is a method field that returns a basic representation of the PhieuYeuCauXuatKho.

        Parameters
        ----------
        obj : ChiTietPhieuYeuCauXuatKhoModel
            The ChiTietPhieuYeuCauXuatKhoModel instance

        Returns
        -------
        dict
            Basic representation of the PhieuYeuCauXuatKho
        """
        if not obj.phieu_yeu_cau_xuat_kho:
            return None

        # Trả về chỉ 3 trường cơ bản để tránh circular import
        phieu = obj.phieu_yeu_cau_xuat_kho
        return {
            'uuid': str(phieu.uuid),
            'so_ct': phieu.so_ct.so_ct if phieu.so_ct else None,
            'dien_giai': phieu.dien_giai
        }

