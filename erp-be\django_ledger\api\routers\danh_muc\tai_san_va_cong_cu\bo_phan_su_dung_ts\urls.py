"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for BoPhanSuDungTS API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.danh_muc.tai_san_va_cong_cu.bo_phan_su_dung_ts import BoPhanSuDungTSViewSet

# Create router and register viewsets
router = DefaultRouter()
router.register('', BoPhanSuDungTSViewSet, basename='bo-phan-su-dung-ts')

urlpatterns = [
    path('', include(router.urls)),
]
