"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Tai san va cong cu (Assets and Tools) API serializers package initialization.
"""

from django_ledger.api.serializers.danh_muc.tai_san_va_cong_cu.loai_tai_san_cong_cu import LoaiTaiSanCongCuModelSerializer
from django_ledger.api.serializers.danh_muc.tai_san_va_cong_cu.bo_phan_su_dung_ccdc import BoPhanSuDungCCDCSerializer
from django_ledger.api.serializers.danh_muc.tai_san_va_cong_cu.ly_do_tang_giam_tai_san_co_dinh import LyDoTangGiamTaiSanCoDinhSerializer
from django_ledger.api.serializers.danh_muc.tai_san_va_cong_cu.bo_phan_su_dung_ts import BoPhanSuDungTSSerializer
from django_ledger.api.serializers.danh_muc.tai_san_va_cong_cu.ly_do_tang_giam_ccdc import LyDoTangGiamCCDCSerializer

__all__ = [
    'LoaiTaiSanCongCuModelSerializer',
    'BoPhanSuDungCCDCSerializer',
    'LyDoTangGiamTaiSanCoDinhSerializer'
    'BoPhanSuDungTSSerializer',
    'LyDoTangGiamCCDCSerializer'
]

