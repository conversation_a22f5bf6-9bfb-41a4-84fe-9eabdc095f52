"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Admin registration for SoDuTucThoiTheoTaiKhoan (Real-time Account Balance) model.
"""

from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from django_ledger.models.ban_hang.so_du_tuc_thoi_theo_tai_khoan import SoDuTucThoiTheoTaiKhoanModel


class SoDuTucThoiTheoTaiKhoanModelAdmin(admin.ModelAdmin):
    """
    Admin class for the SoDuTucThoiTheoTaiKhoanModel (Real-time Account Balance) model.
    """
    list_display = ['nam', 'ma_kh', 'tk', 'created_by', 'updated_by']
    list_filter = ['nam', 'created', 'updated']
    search_fields = ['nam', 'ma_kh__customer_name', 'tk__code']
    readonly_fields = ['uuid', 'created', 'updated']
    fieldsets = [
        (
            _('Basic Information'), {
                'fields': [
                    'entity_model',
                    'nam',
                    'ma_kh',
                    'tk',
                ]
            }
        ),
        (
            _('Metadata'), {
                'fields': [
                    'created_by',
                    'updated_by',
                    'uuid',
                    'created',
                    'updated',
                ],
                'classes': ['collapse']
            }
        ),
    ]
