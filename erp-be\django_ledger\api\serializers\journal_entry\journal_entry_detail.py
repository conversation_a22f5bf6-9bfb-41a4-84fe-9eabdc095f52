"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Journal Entry Detail serializer implementation.
"""

from django_ledger.models.journal_entry import JournalEntryDetailModel
from rest_framework import serializers


class JournalEntryDetailSerializer(serializers.ModelSerializer):
    """
    Serializer for JournalEntryDetailModel.
    """

    class Meta:
        model = JournalEntryDetailModel
        fields = [
            "uuid",
            "phieu_ke_toan",
            "line",
            "tk",
            "tk_cn",
            "ma_kh",
            "ty_gia2",
            "ps_no_nt",
            "ps_co_nt",
            "dien_giai",
            "so_ct0",
            "ngay_ct0",
            "ps_no",
            "ps_co",
            "ma_bp",
            "ma_vv",
            "ma_hd",
            "ma_dtt",
            "ma_ku",
            "ma_phi",
            "ma_sp",
            "ma_lsx",
            "ma_cp0",
            "id_tt",
        ]
