"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for DichVu model.
"""

from rest_framework import serializers

from django_ledger.models import DichVuModel
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.accounts import AccountModelSerializer
from django_ledger.api.serializers.tax import TaxModelSerializer
from django_ledger.api.serializers.chi_phi import ChiPhiModelSerializer
from django_ledger.api.serializers.danh_muc import PhiSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import KheUocModelSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer


class DichVuModelSerializer(serializers.ModelSerializer):
    """
    Serializer for DichVuModel.
    """
    # Add nested serializers for foreign key fields
    dvt_data = DonViTinhSerializer(source='dvt', read_only=True)
    tk_dt_data = AccountModelSerializer(source='tk_dt', read_only=True)
    tk_cp_data = AccountModelSerializer(source='tk_cp', read_only=True)
    ma_thue_data = TaxModelSerializer(source='ma_thue', read_only=True)
    ma_cp_data = ChiPhiModelSerializer(source='ma_cp', read_only=True)
    ma_phi_data = PhiSerializer(source='ma_phi', read_only=True)
    ma_bp_data = BoPhanModelSerializer(source='ma_bp', read_only=True)
    ma_hd_data = ContractModelSerializer(source='ma_hd', read_only=True)
    ma_ku_data = KheUocModelSerializer(source='ma_ku', read_only=True)
    ma_vv_data = VuViecModelSerializer(source='ma_vv', read_only=True)

    class Meta:
        model = DichVuModel
        fields = [
            'uuid',
            'entity_model',
            'action',
            'param',
            'ma_dv',
            'ten_dv',
            'ten_dv2',
            'dvt',
            'dvt_data',
            'tk_dt',
            'tk_dt_data',
            'tk_cp',
            'tk_cp_data',
            'ma_thue',
            'ma_thue_data',
            'gia_nt2',
            'gia_nt',
            'ma_cp',
            'ma_cp_data',
            'ma_phi',
            'ma_phi_data',
            'ma_bp',
            'ma_bp_data',
            'ma_hd',
            'ma_hd_data',
            'ma_ku',
            'ma_ku_data',
            'ma_vv',
            'ma_vv_data',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid', 'created', 'updated', 'entity_model',
            'dvt_data', 'tk_dt_data', 'tk_cp_data', 'ma_thue_data',
            'ma_cp_data', 'ma_phi_data', 'ma_bp_data', 'ma_hd_data',
            'ma_ku_data', 'ma_vv_data'
        ]
        swagger_schema_fields = {
            'title': 'DichVu',
            'description': 'Dịch vụ model serializer'
        }

    def validate_ma_dv(self, value):
        """
        Validate ma_dv field

        Parameters
        ----------
        value : str
            The ma_dv value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Mã dịch vụ không được để trống')
        return value.strip()

    def validate_ten_dv(self, value):
        """
        Validate ten_dv field

        Parameters
        ----------
        value : str
            The ten_dv value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Tên dịch vụ không được để trống')
        return value.strip()

    def validate_action(self, value):
        """
        Validate action field

        Parameters
        ----------
        value : str
            The action value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Hành động không được để trống')
        return value.strip()

    def create(self, validated_data):
        """
        Override create method to handle entity_model

        Parameters
        ----------
        validated_data : dict
            The validated data

        Returns
        -------
        DichVuModel
            The created DichVuModel instance
        """
        # Remove entity_model from validated_data to prevent unexpected keyword argument error
        if 'entity_model' in validated_data:
            validated_data.pop('entity_model')
        return super().create(validated_data)
