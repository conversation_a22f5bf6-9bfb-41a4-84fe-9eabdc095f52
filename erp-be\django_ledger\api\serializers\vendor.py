"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.
"""

from rest_framework import serializers
from django_ledger.models import VendorModel

class VendorModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the VendorModel
    """
    class Meta:
        model = VendorModel
        fields = [
            'uuid',
            'vendor_name',
            'address_1',
            'address_2',
            'city',
            'state',
            'zip_code',
            'country',
            'phone',
            'email',
            'website',
            'active',
            'hidden',
            'description',
            # Additional ERP fields
            'ma_nha_cung_cap',
            'khach_hang',
            'nha_cung_cap',
            'loai_khach_hang',
            'ten_nha_cung_cap',
            'ten_khac',
            'dia_chi',
            'ma_so_thue',
            'nguoi_lien_he',
            'nhan_vien_ban_hang',
            'tai_khoan_ngam_dinh',
            'ma_th_toan_cong_no',
            'ph_th_th_toan',
            'gioi_han_tien_no',
            'khu_vuc',
            'dien_thoai',
            'ngay_sinh',
            'so_tai_khoan',
            'ten_ngan_hang',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated']
