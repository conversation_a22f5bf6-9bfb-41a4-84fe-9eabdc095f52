from django.contrib import admin

from django_ledger.models import (
    EntityModel,
    EntityManagementModel,
    LedgerModel,
    ChartOfAccountModel,
    AccountModel,
    TransactionModel,
    JournalEntryModel,
    BillModel,
    InvoiceModel,
    EstimateModel,
    BankAccountModel,
    EntityUnitModel,
    CustomerModel,
    PurchaseOrderModel,
    VendorModel,
    ImportJobModel,
    StagedTransactionModel,
    ClosingEntryModel,
    UnitOfMeasureModel,
    ItemModel,
    ItemTransactionModel,
    UserProfileModel,
    NhanVienModel,
    # New import
    LoaiGiaBan,  # Sales Price Type model
    NhomKhachHang,  # Customer Group model
    QuocGiaModel,  # Country model
    TinhThanhModel,  # Province/City model
    QuanHuyenModel,  # District model
    XaPhuongModel,  # Ward model
    KhuVucModel,  # Region model
    BoPhanModel,  # Department model
    BoPhanSuDungTSModel,  # Department for Asset Usage model
    # BoPhanSuDungCCDCModel moved to new structure
    VuViecModel,  # VuViec model
    GroupModel,
    CoQuanThueModel,  # Tax Authority model
    LyDoTangGiamTaiSanCoDinhModel,  # Fixed Asset Change Reason model
    TyGiaModel,  # Exchange Rate model
    NganHangModel,  # Bank model
    ViTriKhoHangModel,  # Warehouse Location model
    LyDoTangGiamCCDCModel,  # Tool and Equipment Change Reason model
    DieuKienThanhToanModel,  # Payment Condition model
    ChiTietDieuKienThanhToanModel,  # Payment Condition Detail model
    ChiPhiKhongHopLeModel,  # Invalid Expense model
    ContractModel,  # Contract model
    SoDuTucThoiTheoTaiKhoanModel,  # Real-time Account Balance model
    DanhMucCongDoanModel,  # Production Stage Catalog model
)

# KheUoc models are now imported in their own admin module
# from django_ledger.models.danh_muc.hop_dong_khe_uoc.khe_uoc import (
#     KheUocModel,  # Loan/Contract model
#     ChiTietKheUocLaiSuatModel,  # Loan Interest Rate model
#     ChiTietKheUocThanhToanModel,  # Loan Payment Detail model
# )

from django_ledger.admin.entity import EntityModelAdmin, EntityManagementModelAdmin
from django_ledger.admin.ledger import LedgerModelAdmin
from django_ledger.admin.chart_of_accounts import ChartOfAccountsModelAdmin
from django_ledger.admin.erp_admin import VendorModelAdmin, CustomerModelAdmin, QuocGiaModelAdmin, TinhThanhModelAdmin, QuanHuyenModelAdmin, XaPhuongModelAdmin, KhuVucModelAdmin, BoPhanModelAdmin, BoPhanSuDungTSModelAdmin
# BoPhanSuDungCCDCModelAdmin moved to new structure
from django_ledger.admin.co_quan_thue import CoQuanThueModelAdmin
from django_ledger.admin.items import UnitOfMeasureModelAdmin, ItemModelAdmin  # Import Customer/Vendor management admin
from django_ledger.admin.loai_gia_ban_admin import LoaiGiaBanAdmin  # Import Sales Price Type admin
from django_ledger.admin.nhom_khach_hang_admin import NhomKhachHangAdmin  # Import Customer Group admin
from django_ledger.admin.vu_viec import VuViecModelAdmin  # Import VuViec admin
from django_ledger.admin.group import GroupModelAdmin

from django_ledger.admin.ty_gia import TyGiaModelAdmin  # Import Exchange Rate admin
from django_ledger.admin.ngan_hang import NganHangModelAdmin  # Import Bank admin
from django_ledger.admin.account import AccountModelAdmin  # Import Account admin
from django_ledger.admin.vi_tri_kho_hang import ViTriKhoHangModelAdmin  # Import Warehouse Location admin
from django_ledger.admin.ly_do_tang_giam_ccdc import LyDoTangGiamCCDCModelAdmin  # Import Tool and Equipment Change Reason admin
from django_ledger.admin.dieu_kien_thanh_toan import DieuKienThanhToanModelAdmin, ChiTietDieuKienThanhToanModelAdmin  # Import Payment Condition admin
from django_ledger.admin.chi_phi_khong_hop_le import ChiPhiKhongHopLeModelAdmin  # Import Invalid Expense admin
from django_ledger.admin.contract import ContractModelAdmin  # Import Contract admin
from django_ledger.admin.ban_hang.so_du_tuc_thoi_theo_tai_khoan import SoDuTucThoiTheoTaiKhoanModelAdmin  # Import Real-time Account Balance admin

admin.site.register(EntityModel, EntityModelAdmin)
admin.site.register(EntityManagementModel, EntityManagementModelAdmin)  # Register EntityManagementModel
admin.site.register(LedgerModel, LedgerModelAdmin)
admin.site.register(ChartOfAccountModel, ChartOfAccountsModelAdmin)
admin.site.register(AccountModel, AccountModelAdmin)
admin.site.register(TransactionModel)
admin.site.register(JournalEntryModel)
admin.site.register(BillModel)
admin.site.register(InvoiceModel)
admin.site.register(EstimateModel)
admin.site.register(BankAccountModel)
admin.site.register(EntityUnitModel)
admin.site.register(CustomerModel, CustomerModelAdmin)
admin.site.register(PurchaseOrderModel)
admin.site.register(VendorModel, VendorModelAdmin)
admin.site.register(ImportJobModel)
admin.site.register(StagedTransactionModel)
admin.site.register(ClosingEntryModel)
admin.site.register(UnitOfMeasureModel, UnitOfMeasureModelAdmin)
admin.site.register(ItemModel, ItemModelAdmin)
admin.site.register(ItemTransactionModel)
admin.site.register(UserProfileModel)
admin.site.register(NhanVienModel)
admin.site.register(VuViecModel, VuViecModelAdmin)
admin.site.register(GroupModel, GroupModelAdmin)

# New registration
admin.site.register(LoaiGiaBan, LoaiGiaBanAdmin)  # Register Sales Price Type model
admin.site.register(NhomKhachHang, NhomKhachHangAdmin)  # Register Customer Group model
admin.site.register(QuocGiaModel, QuocGiaModelAdmin)  # Register Country model
admin.site.register(TinhThanhModel, TinhThanhModelAdmin)  # Register Province/City model
admin.site.register(QuanHuyenModel, QuanHuyenModelAdmin)  # Register District model
admin.site.register(XaPhuongModel, XaPhuongModelAdmin)  # Register Ward model
admin.site.register(KhuVucModel, KhuVucModelAdmin)  # Register Region model
admin.site.register(BoPhanModel, BoPhanModelAdmin)  # Register Department model
admin.site.register(BoPhanSuDungTSModel, BoPhanSuDungTSModelAdmin)  # Register Department for Asset Usage model
# BoPhanSuDungCCDCModel registration moved to new structure
admin.site.register(CoQuanThueModel, CoQuanThueModelAdmin)  # Register Tax Authority model
admin.site.register(TyGiaModel, TyGiaModelAdmin)  # Register Exchange Rate model
admin.site.register(NganHangModel, NganHangModelAdmin)  # Register Bank model
admin.site.register(ViTriKhoHangModel, ViTriKhoHangModelAdmin)  # Register Warehouse Location model
admin.site.register(LyDoTangGiamCCDCModel, LyDoTangGiamCCDCModelAdmin)  # Register Tool and Equipment Change Reason model
# KheUoc models are now registered in their own admin module
# admin.site.register(KheUocModel)  # Register Loan/Contract model
# admin.site.register(ChiTietKheUocLaiSuatModel)  # Register Loan Interest Rate model
# admin.site.register(ChiTietKheUocThanhToanModel)  # Register Loan Payment Detail model
admin.site.register(DieuKienThanhToanModel, DieuKienThanhToanModelAdmin)  # Register Payment Condition model
admin.site.register(ChiTietDieuKienThanhToanModel, ChiTietDieuKienThanhToanModelAdmin)  # Register Payment Condition Detail model
admin.site.register(ChiPhiKhongHopLeModel, ChiPhiKhongHopLeModelAdmin)  # Register Invalid Expense model
admin.site.register(ContractModel, ContractModelAdmin)  # Register Contract model
admin.site.register(SoDuTucThoiTheoTaiKhoanModel, SoDuTucThoiTheoTaiKhoanModelAdmin)  # Register Real-time Account Balance model
