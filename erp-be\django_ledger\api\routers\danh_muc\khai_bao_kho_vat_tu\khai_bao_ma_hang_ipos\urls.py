"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Khai Bao Ma Hang Ipos (Ipos Product Code Declaration) module.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.danh_muc.khai_bao_kho_vat_tu.khai_bao_ma_hang_ipos import KhaiBaoMaHangIposViewSet

# Main router for KhaiBaoMaHangIpos
router = DefaultRouter()
router.register('', KhaiBaoMaHangIposViewSet, basename='khai-bao-ma-hang-ipos')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
