"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for PhieuXuatTraLaiNhaCungCap (Supplier Return Note) model.
"""

from rest_framework import serializers

# Import các serializer cần thiết
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.danh_muc.ban_hang.hinh_thuc_thanh_toan import HinhThucThanhToanModelSerializer
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer 
from django_ledger.api.serializers.nhan_vien import NhanVienModelSerializer
from django_ledger.api.serializers.nhap_xuat import NhapXuatModelSerializer
from django_ledger.api.serializers.mua_hang.hoa_don_dieu_chinh_tra_lai.phieu_xuat_tra_lai_nha_cung_cap.chi_tiet_phieu_xuat_tra_lai_nha_cung_cap import ChiTietPhieuXuatTraLaiNhaCungCapNestedSerializer
from django_ledger.api.serializers.tax import TaxModelSerializer

# Import model
from django_ledger.models.mua_hang.hoa_don_dieu_chinh_tra_lai.phieu_xuat_tra_lai_nha_cung_cap import PhieuXuatTraLaiNhaCungCapModel


class PhieuXuatTraLaiNhaCungCapSerializer(serializers.ModelSerializer):
    """
    Serializer for PhieuXuatTraLaiNhaCungCap model
    """
    entity_model = EntityModelSerializer(read_only=True)

    # Reference data fields
    ma_ngv_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    ma_nx_data = serializers.SerializerMethodField(read_only=True)
    ma_tt_data = serializers.SerializerMethodField(read_only=True)
    tk_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)
    ma_thue_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_data = serializers.SerializerMethodField(read_only=True)
    ma_kh9_data = serializers.SerializerMethodField(read_only=True)

    def get_ma_ngv_data(self, obj):
        """Return employee data if available"""
        if obj.ma_ngv:
            return NhanVienModelSerializer(obj.ma_ngv).data
        return None

    def get_ma_kh_data(self, obj):
        """Return customer data if available"""
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_ma_nx_data(self, obj):
        """Return import/export data if available"""
        if obj.ma_nx:
            return NhapXuatModelSerializer(obj.ma_nx).data
        return None

    def get_ma_tt_data(self, obj):
        """Return payment method data if available"""
        if obj.ma_tt:
            return HinhThucThanhToanModelSerializer(obj.ma_tt).data
        return None

    def get_tk_data(self, obj):
        """Return account data if available"""
        if obj.tk:
            return AccountModelSerializer(obj.tk).data
        return None

    def get_ma_nt_data(self, obj):
        """Return currency data if available"""
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_ma_thue_data(self, obj):
        """Return tax data if available"""
        if obj.ma_thue:
            return TaxModelSerializer(obj.ma_thue).data
        return None

    def get_tk_thue_data(self, obj):
        """Return tax account data if available"""
        if obj.tk_thue:
            return AccountModelSerializer(obj.tk_thue).data
        return None

    def get_ma_kh9_data(self, obj):
        """Return customer 9 data if available"""
        if obj.ma_kh9:
            return CustomerModelSerializer(obj.ma_kh9).data
        return None

    class Meta:
        model = PhieuXuatTraLaiNhaCungCapModel
        fields = [
            'uuid',
            'entity_model',
            'ma_ngv',
            'ma_ngv_data',
            'ma_kh',
            'ma_kh_data',
            'ma_so_thue',
            'ten_kh_thue',
            'ong_ba',
            'dia_chi',
            'e_mail',
            'ma_nx',
            'ma_nx_data',
            'ma_tt',
            'ma_tt_data',
            'tk',
            'tk_data',
            'dien_giai',
            'unit_id',
            'i_so_ct',
            'ma_nk',
            'so_ct',
            'ngay_ct',
            'ngay_lct',
            'so_ct2',
            'ngay_ct0',
            'ma_nt',
            'ma_nt_data',
            'ty_gia',
            'status',
            'transfer_yn',
            'ma_mau_bc',
            'ma_tc_thue',
            'ma_thue',
            'ma_thue_data',
            'tk_thue',
            'tk_thue_data',
            'ma_kh9',
            'ma_kh9_data',
            'ten_vt_thue',
            'ghi_chu',
            'outvat_yn',
            'thue_suat',
            'ma_tthddt',
            'ma_pttt',
            'ma_gd',
            't_so_luong',
            't_tien_nt',
            't_tien',
            't_thue_nt',
            't_thue',
            't_tt_nt',
            't_tt',
            'created',
            'updated',
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            't_so_luong',
            't_tien_nt',
            't_tien',
            't_thue_nt',
            't_thue',
            't_tt_nt',
            't_tt',
            'created',
            'updated',
        ]


class PhieuXuatTraLaiNhaCungCapCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating PhieuXuatTraLaiNhaCungCap model
    """
    chi_tiet = ChiTietPhieuXuatTraLaiNhaCungCapNestedSerializer(many=True, required=False)

    class Meta:
        model = PhieuXuatTraLaiNhaCungCapModel
        fields = [
            'ma_ngv',
            'ma_kh',
            'ma_so_thue',
            'ten_kh_thue',
            'ong_ba',
            'dia_chi',
            'e_mail',
            'ma_nx',
            'ma_tt',
            'tk',
            'dien_giai',
            'unit_id',
            'i_so_ct',
            'ma_nk',
            'so_ct',
            'ngay_ct',
            'ngay_lct',
            'so_ct2',
            'ngay_ct0',
            'ma_nt',
            'ty_gia',
            'status',
            'transfer_yn',
            'ma_mau_bc',
            'ma_tc_thue',
            'ma_thue',
            'tk_thue',
            'ma_kh9',
            'ten_vt_thue',
            'ghi_chu',
            'outvat_yn',
            'thue_suat',
            'ma_tthddt',
            'ma_pttt',
            'ma_gd',
            'chi_tiet',
        ]
