"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Common utilities for API serializers
"""

from rest_framework import serializers


class StatusModelSerializer(serializers.Serializer):
    """
    Serializer for status field
    """
    status_display = serializers.CharField(read_only=True, source='get_status_display')
