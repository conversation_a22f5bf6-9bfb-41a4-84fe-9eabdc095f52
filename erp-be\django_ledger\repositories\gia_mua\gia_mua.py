"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Repository for GiaMua (Purchase Price) model database operations
"""

from django.db.models import QuerySet
from typing import Dict, Any, Optional, Tuple, Union
from django.core.paginator import <PERSON><PERSON><PERSON>, EmptyPage, PageNotAnInteger
from django.shortcuts import get_object_or_404

from django_ledger.models import GiaMuaModel, EntityModel
from django_ledger.repositories.base import BaseRepository


class GiaMuaRepository(BaseRepository):
    """
    Repository for handling GiaMua (Purchase Price) model database operations
    """

    def __init__(self):
        self.model = GiaMuaModel

    def _convert_uuids_to_model_instances(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Helper method to convert UUID strings to model instances for foreign key fields
        """
        from django_ledger.models import (
            VatTuModel,
            DonViTinhModel,
            CustomerModel,
            NgoaiTeModel,
        )

        # Default page size constant to avoid circular import
        DEFAULT_PAGE_SIZE = 20

        # Create a copy of the data to avoid modifying the original
        data_copy = data.copy()

        # Handle entity_model field if it's a string (slug)
        if "entity_slug" in data_copy and isinstance(data_copy["entity_slug"], str):
            try:
                # Convert entity_slug to entity_model
                entity_slug = data_copy.pop("entity_slug")
                data_copy["entity_model"] = get_object_or_404(
                    EntityModel, slug=entity_slug
                )
            except EntityModel.DoesNotExist:
                raise ValueError(f"EntityModel with slug {entity_slug} does not exist")

        # Handle ma_vat_tu (product) field
        if "ma_vat_tu" in data_copy and isinstance(data_copy["ma_vat_tu"], str):
            try:
                data_copy["ma_vat_tu"] = VatTuModel.objects.get(
                    uuid=data_copy["ma_vat_tu"]
                )
            except VatTuModel.DoesNotExist:
                raise ValueError(
                    f"VatTuModel with UUID {data_copy['ma_vat_tu']} does not exist"
                )

        # Handle don_vi_tinh (unit) field
        if "don_vi_tinh" in data_copy and isinstance(data_copy["don_vi_tinh"], str):
            try:
                data_copy["don_vi_tinh"] = DonViTinhModel.objects.get(
                    uuid=data_copy["don_vi_tinh"]
                )
            except DonViTinhModel.DoesNotExist:
                raise ValueError(
                    f"DonViTinhModel with UUID {data_copy['don_vi_tinh']} does not exist"
                )

        # Handle nha_cung_cap (vendor) field - now optional
        if "nha_cung_cap" in data_copy:
            if isinstance(data_copy["nha_cung_cap"], str) and data_copy["nha_cung_cap"].strip():
                try:
                    data_copy["nha_cung_cap"] = CustomerModel.objects.get(
                        uuid=data_copy["nha_cung_cap"]
                    )
                except CustomerModel.DoesNotExist:
                    raise ValueError(
                        f"CustomerModel with UUID {data_copy['nha_cung_cap']} does not exist"
                    )
            else:
                # If empty string or None, set to None (optional supplier)
                data_copy["nha_cung_cap"] = None

        # Handle ngoai_te (currency) field
        if "ngoai_te" in data_copy and isinstance(data_copy["ngoai_te"], str):
            try:
                data_copy["ngoai_te"] = NgoaiTeModel.objects.get(
                    uuid=data_copy["ngoai_te"]
                )
            except NgoaiTeModel.DoesNotExist:
                raise ValueError(
                    f"NgoaiTeModel with UUID {data_copy['ngoai_te']} does not exist"
                )

        return data_copy

    def get_gia_mua_by_uuid(self, uuid: str) -> Optional[GiaMuaModel]:
        """
        Get a purchase price by UUID
        """
        try:
            return self.model.objects.get(uuid=uuid)
        except self.model.DoesNotExist:
            return None

    def get_gia_mua(self, **kwargs) -> QuerySet:
        """
        Get all purchase prices with optional filters
        """
        queryset = self.model.objects.all()
        if kwargs:
            queryset = queryset.filter(**kwargs)
        return queryset

    def create_gia_mua(self, data: Dict[str, Any], entity_model=None) -> GiaMuaModel:
        """
        Create a new purchase price

        Parameters
        ----------
        data : Dict[str, Any]
            The data for the new purchase price
        entity_model : EntityModel, optional
            The entity model to associate with the purchase price. If provided, it will override
            any entity_model in the data.

        Returns
        -------
        GiaMuaModel
            The created purchase price model
        """
        # Convert UUID strings to model instances for foreign key fields
        processed_data = self._convert_uuids_to_model_instances(data)

        # If entity_model is provided, use it
        if entity_model:
            processed_data["entity_model"] = entity_model

        return self.model.objects.create(**processed_data)

    def update_gia_mua(
        self, uuid: str, data: Dict[str, Any], entity_model=None
    ) -> Optional[GiaMuaModel]:
        """
        Update an existing purchase price

        Parameters
        ----------
        uuid : str
            The UUID of the purchase price to update
        data : Dict[str, Any]
            The data to update the purchase price with
        entity_model : EntityModel, optional
            The entity model to associate with the purchase price. If provided, it will override
            any entity_model in the data.

        Returns
        -------
        Optional[GiaMuaModel]
            The updated purchase price model, or None if not found
        """
        try:
            # Convert UUID strings to model instances for foreign key fields
            processed_data = self._convert_uuids_to_model_instances(data)

            # If entity_model is provided, use it
            if entity_model:
                processed_data["entity_model"] = entity_model

            gia_mua = self.get_gia_mua_by_uuid(uuid)
            if gia_mua:
                for key, value in processed_data.items():
                    setattr(gia_mua, key, value)
                gia_mua.save()
            return gia_mua
        except self.model.DoesNotExist:
            return None

    def delete_gia_mua(self, uuid: str) -> bool:
        """
        Delete a purchase price by UUID
        """
        try:
            gia_mua = self.get_gia_mua_by_uuid(uuid)
            if gia_mua:
                gia_mua.delete()
                return True
            return False
        except self.model.DoesNotExist:
            return False

    def get_active_gia_mua(self) -> QuerySet:
        """
        Get all active purchase prices
        """
        return self.get_gia_mua(trang_thai__gt=0)

    def get_gia_mua_for_entity(
        self, entity_model: EntityModel, page: int = 1, page_size: int = None
    ) -> Union[QuerySet, Tuple[QuerySet, Dict]]:
        """
        Get paginated purchase prices for a specific entity

        Parameters
        ----------
        entity_model : EntityModel
            The entity model to filter by
        page : int, optional
            The page number, by default 1
        page_size : int, optional
            The number of items per page, by default DEFAULT_PAGE_SIZE

        Returns
        -------
        Union[QuerySet, Tuple[QuerySet, Dict]]
            If page is specified: Tuple[QuerySet, pagination_info]
            If page is None: QuerySet
        """
        queryset = self.get_gia_mua().filter(entity_model=entity_model)

        if page is not None:
            try:
                paginator = Paginator(queryset, per_page=page_size or DEFAULT_PAGE_SIZE)
                current_page = paginator.page(page)

                # Calculate remaining items
                total_items = paginator.count
                items_before = (page - 1) * current_page.paginator.per_page
                items_after = total_items - (page * current_page.paginator.per_page)

                pagination_info = {
                    "count": total_items,
                    "next": page + 1 if current_page.has_next() else None,
                    "previous": page - 1 if current_page.has_previous() else None,
                }

                return current_page.object_list, pagination_info
            except (EmptyPage, PageNotAnInteger):
                # Return empty first page if error
                return queryset.none(), {"count": 0, "next": None, "previous": None}

        return queryset

    def get_gia_mua_for_product(
        self,
        entity_model: EntityModel,
        ma_vt: str,
        page: int = 1,
        page_size: int = None,
    ) -> Union[QuerySet, Tuple[QuerySet, Dict]]:
        """
        Get paginated purchase prices for a specific product

        Parameters
        ----------
        entity_model : EntityModel
            The entity model to filter by
        ma_vt : str
            The product code to filter by
        page : int, optional
            The page number, by default 1
        page_size : int, optional
            The number of items per page, by default DEFAULT_PAGE_SIZE

        Returns
        -------
        Union[QuerySet, Tuple[QuerySet, Dict]]
            If page is specified: Tuple[QuerySet, pagination_info]
            If page is None: QuerySet
        """
        queryset = self.get_gia_mua().filter(
            entity_model=entity_model, ma_vat_tu__ma_vt=ma_vt
        )

        if page is not None:
            try:
                paginator = Paginator(queryset, per_page=page_size or DEFAULT_PAGE_SIZE)
                current_page = paginator.page(page)

                pagination_info = {
                    "count": paginator.count,
                    "next": page + 1 if current_page.has_next() else None,
                    "previous": page - 1 if current_page.has_previous() else None,
                }

                return current_page.object_list, pagination_info
            except (EmptyPage, PageNotAnInteger):
                # Return empty first page if error
                return queryset.none(), {"count": 0, "next": None, "previous": None}

        return queryset

    def get_gia_mua_for_vendor(
        self,
        entity_model: EntityModel,
        ma_kh: str,
        page: int = 1,
        page_size: int = None,
    ) -> Union[QuerySet, Tuple[QuerySet, Dict]]:
        """
        Get paginated purchase prices for a specific vendor

        Parameters
        ----------
        entity_model : EntityModel
            The entity model to filter by
        ma_kh : str
            The vendor code to filter by
        page : int, optional
            The page number, by default 1
        page_size : int, optional
            The number of items per page, by default DEFAULT_PAGE_SIZE

        Returns
        -------
        Union[QuerySet, Tuple[QuerySet, Dict]]
            If page is specified: Tuple[QuerySet, pagination_info]
            If page is None: QuerySet
        """
        queryset = self.get_gia_mua().filter(entity_model=entity_model)

        # Filter by vendor code if provided
        if ma_kh:
            # We need to join to the vendor model and filter by its code
            # This assumes the nha_cung_cap field is a ForeignKey to a model that has a ma_kh field
            queryset = queryset.filter(nha_cung_cap__ma_kh=ma_kh)

        if page is not None:
            try:
                paginator = Paginator(queryset, per_page=page_size or DEFAULT_PAGE_SIZE)
                current_page = paginator.page(page)

                pagination_info = {
                    "count": paginator.count,
                    "next": page + 1 if current_page.has_next() else None,
                    "previous": page - 1 if current_page.has_previous() else None,
                }

                return current_page.object_list, pagination_info
            except (EmptyPage, PageNotAnInteger):
                # Return empty first page if error
                return queryset.none(), {"count": 0, "next": None, "previous": None}

        return queryset
