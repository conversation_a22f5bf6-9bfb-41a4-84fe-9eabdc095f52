"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

DieuChinhGiaTriCCDC (Tool Value Adjustment) serializer implementation.
"""

from rest_framework import serializers

from django_ledger.models.cong_cu.khai_bao_tang_giam_ccdc.dieu_chinh_gia_tri_ccdc import DieuChinhGiaTriCCDCModel
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc import KhaiBaoThongTinCCDCModelSerializer
from django_ledger.api.serializers.danh_muc import LyDoTangGiamCCDCSerializer
from django_ledger.api.serializers.ty_gia import TyGiaModelSerializer


class DieuChinhGiaTriCCDCModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the DieuChinhGiaTriCCDCModel.

    This serializer handles the conversion between DieuChinhGiaTriCCDCModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields
    - Adds additional fields with "_data" suffix that contain the complete nested object data
    - When deserializing, accepts UUIDs for reference fields
    """
    # Add nested serializers for foreign key fields
    entity_model = EntityModelSerializer(read_only=True)
    ma_cc_data = KhaiBaoThongTinCCDCModelSerializer(source='ma_cc', read_only=True)
    ma_tg_cc_data = LyDoTangGiamCCDCSerializer(source='ma_tg_cc', read_only=True)
    ty_gia_data = TyGiaModelSerializer(source='ty_gia', read_only=True)

    class Meta:
        model = DieuChinhGiaTriCCDCModel
        fields = [
            'uuid',
            'entity_model',
            'ma_cc',
            'ma_cc_data',
            'ma_tg_cc',
            'ma_tg_cc_data',
            'loai_tg_cc',
            'ky',
            'nam',
            'ngay_ct',
            'ma_nt',
            'ty_gia',
            'ty_gia_data',
            'nguyen_gia_nt',
            'nguyen_gia',
            'gt_da_kh_nt',
            'gt_da_kh',
            'gt_cl_nt',
            'gt_cl',
            'so_ky_kh',
            'gt_kh_ky_nt',
            'gt_kh_ky',
            'gt_kh_ht_nt',
            'gt_kh_ht',
            'gt_kh_sdc_nt',
            'gt_kh_sdc',
            'dien_giai',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'ma_cc_data',
            'ma_tg_cc_data',
            'ty_gia_data',
            'created',
            'updated'
        ]
