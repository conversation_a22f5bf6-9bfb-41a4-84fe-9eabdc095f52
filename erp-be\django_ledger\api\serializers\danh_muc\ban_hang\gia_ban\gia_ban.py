"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for GiaBan model.
"""
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from django_ledger.models.danh_muc.ban_hang.gia_ban.gia_ban import GiaBanModel
from django_ledger.api.serializers.base import GlobalModelSerializer
from django_ledger.api.serializers.danh_muc.ban_hang.gia_ban.gia_ban_chi_tiet import GiaBanChiTietSerializer
# Import VatTuSerializer lazily to avoid circular imports
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.danh_muc.ke_toan import NgoaiTeSerializer 
from django_ledger.api.serializers.customer import CustomerModelSerializer
# Removed LoaiGiaBan import as it's no longer needed
from django_ledger.api.serializers.group import GroupModelSimpleSerializer


class GiaBanSerializer(GlobalModelSerializer):
    """
    Serializer class for GiaBanModel that handles data serialization/deserialization.
    Inherits from GlobalModelSerializer for common fields and behaviors.

    This serializer can be used in two modes:
    1. Full mode (default): Includes all fields and related data
    2. Nested mode: Simplified representation for use in nested contexts

    To use in nested mode, pass context={'nested': True} when initializing.
    """
    # Read-only fields for related objects - only used in full mode
    vat_tu_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    nh_kh1_data = serializers.SerializerMethodField(read_only=True)
    nh_kh2_data = serializers.SerializerMethodField(read_only=True)
    nh_kh3_data = serializers.SerializerMethodField(read_only=True)

    # Price details - read-only for GET responses, write-only for POST/PUT requests
    chi_tiet_data = GiaBanChiTietSerializer(source='chi_tiet', many=True, read_only=True)
    chi_tiet = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = GiaBanModel
        fields = [
            'uuid',
            'entity_model',
            'ma_vt',
            'vat_tu_data',
            'dvt',
            'dvt_data',
            'ngay_hl',
            'ma_nt',
            'ma_nt_data',
            'gia_nt2',
            'thue_yn',
            'status',
            'ma_kh',
            'ma_kh_data',
            'nh_kh1',
            'nh_kh1_data',
            'nh_kh2',
            'nh_kh2_data',
            'nh_kh3',
            'nh_kh3_data',
            'chi_tiet_data',
            'chi_tiet',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'vat_tu_data',
            'dvt_data',
            'ma_nt_data',
            'ma_kh_data',
            'nh_kh1_data',
            'nh_kh2_data',
            'nh_kh3_data',
            'chi_tiet_data',
            'created',
            'updated'
        ]

    def __init__(self, *args, **kwargs):
        """
        Initialize the serializer with options for nested or full representation.

        Parameters
        ----------
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments

        Notes
        -----
        If context contains {'nested': True}, the serializer will use a simplified
        representation suitable for nested contexts.
        """
        super().__init__(*args, **kwargs)

        # Check if we're using the nested mode
        if self.context.get('nested', False):
            # Remove fields not needed in nested representation
            for field_name in ['vat_tu_data', 'dvt_data', 'ma_nt_data', 'ma_kh_data',
                              'nh_kh1_data', 'nh_kh2_data', 'nh_kh3_data',
                              'chi_tiet_data', 'chi_tiet', 'entity_model']:
                if field_name in self.fields:
                    self.fields.pop(field_name)

    def get_vat_tu_data(self, obj):
        """
        Get basic information about the material using VatTuSerializer
        """
        if obj.ma_vt:
            # Import VatTuSerializer lazily to avoid circular imports
            from django_ledger.api.serializers.vat_tu import VatTuSerializer
            return VatTuSerializer(obj.ma_vt).data
        return None

    def get_dvt_data(self, obj):
        """
        Get basic information about the unit of measure using DonViTinhSerializer
        """
        if obj.dvt:
            return DonViTinhSerializer(obj.dvt).data
        return None

    def get_ma_nt_data(self, obj):
        """
        Get basic information about the currency using NgoaiTeModelSerializer
        """
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_ma_kh_data(self, obj):
        """
        Get basic information about the customer using CustomerModelSerializer
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None



    def get_nh_kh1_data(self, obj):
        """
        Get basic information about customer group 1 using GroupModelSimpleSerializer
        """
        if obj.nh_kh1:
            return GroupModelSimpleSerializer(obj.nh_kh1).data
        return None

    def get_nh_kh2_data(self, obj):
        """
        Get basic information about customer group 2 using GroupModelSimpleSerializer
        """
        if obj.nh_kh2:
            return GroupModelSimpleSerializer(obj.nh_kh2).data
        return None

    def get_nh_kh3_data(self, obj):
        """
        Get basic information about customer group 3 using GroupModelSimpleSerializer
        """
        if obj.nh_kh3:
            return GroupModelSimpleSerializer(obj.nh_kh3).data
        return None

    def validate(self, attrs):
        """
        Custom validation for GiaBan data.

        Parameters
        ----------
        attrs: dict
            Dictionary of field values to validate

        Returns
        -------
        dict
            Validated data

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        attrs = super().validate(attrs)

        # Required fields validation
        # If in nested mode, require more fields
        if self.context.get('nested', False):
            required_fields = ['dvt', 'ngay_hl', 'ma_nt', 'gia_nt2']
        else:
            required_fields = ['ngay_hl', 'gia_nt2']

        for field in required_fields:
            if field not in attrs or not attrs.get(field):
                raise serializers.ValidationError({
                    field: _('This field is required.')
                })

        # Validate price is positive
        if 'gia_nt2' in attrs:
            try:
                # Convert to float for comparison if it's a string
                gia_nt2_value = float(attrs['gia_nt2']) if isinstance(attrs['gia_nt2'], str) else attrs['gia_nt2']
                if gia_nt2_value < 0:
                    raise serializers.ValidationError({
                        'gia_nt2': _('Price cannot be negative.')
                    })
            except (ValueError, TypeError):
                raise serializers.ValidationError({
                    'gia_nt2': _('Invalid price format.')
                })

        # Validate status
        if 'status' in attrs:
            # Convert status to string if it's an integer
            if isinstance(attrs['status'], int):
                attrs['status'] = str(attrs['status'])

            # Validate status value
            if attrs['status'] not in ['0', '1']:
                raise serializers.ValidationError({
                    'status': _('Invalid status value. Must be 0 or 1.')
                })

        # Validate chi_tiet data if present
        if 'chi_tiet' in attrs:
            chi_tiet_data = attrs['chi_tiet']
            if not isinstance(chi_tiet_data, list):
                raise serializers.ValidationError({
                    'chi_tiet': _('Must be a list of price detail objects.')
                })

            # Validate each chi_tiet item
            for i, item in enumerate(chi_tiet_data):
                if not isinstance(item, dict):
                    raise serializers.ValidationError({
                        f'chi_tiet[{i}]': _('Must be an object.')
                    })

                # Ensure required fields are present
                if 'gia_nt2' not in item:
                    raise serializers.ValidationError({
                        f'chi_tiet[{i}].gia_nt2': _('This field is required.')
                    })

                # Validate price is positive
                try:
                    gia_nt2 = item.get('gia_nt2')
                    if gia_nt2 is not None:
                        # Convert to float for comparison if it's a string
                        gia_nt2_value = float(gia_nt2) if isinstance(gia_nt2, str) else gia_nt2
                        if gia_nt2_value < 0:
                            raise serializers.ValidationError({
                                f'chi_tiet[{i}].gia_nt2': _('Price cannot be negative.')
                            })
                except (ValueError, TypeError):
                    raise serializers.ValidationError({
                        f'chi_tiet[{i}].gia_nt2': _('Invalid price format.')
                    })

                # Ensure UUID is valid if provided
                if 'uuid' in item and item['uuid']:
                    try:
                        # Try to parse as UUID to validate
                        from uuid import UUID
                        UUID(item['uuid'])
                    except ValueError:
                        raise serializers.ValidationError({
                            f'chi_tiet[{i}].uuid': _('Invalid UUID format.')
                        })

        return attrs

