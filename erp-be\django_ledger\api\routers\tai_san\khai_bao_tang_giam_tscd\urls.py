"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Khai Bao Tang Giam TSCD (Fixed Asset Increase/Decrease Declaration) module.
"""

from django.urls import path, include

# URL patterns for the module
urlpatterns = [
    # Include dieu_chinh_gia_tri_tscd URLs
    path('dieu-chinh-gia-tri-tscd/', include('django_ledger.api.routers.tai_san.khai_bao_tang_giam_tscd.dieu_chinh_gia_tri_tscd.urls')),
    
    # Include khai_bao_thong_tin_tai_san_co_dinh URLs
    path('khai-bao-thong-tin-tai-san-co-dinh/', include('django_ledger.api.routers.tai_san.khai_bao_tang_giam_tscd.khai_bao_thong_tin_tai_san_co_dinh.urls')),

    # Add other khai_bao_tang_giam_tscd-related URLs here
]
