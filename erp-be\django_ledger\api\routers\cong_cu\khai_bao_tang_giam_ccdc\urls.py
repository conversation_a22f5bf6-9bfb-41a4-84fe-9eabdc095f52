"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Khai <PERSON><PERSON> Tang Giam CCDC (Tool Increase/Decrease Declaration) router implementation.
"""

from django.urls import path, include

urlpatterns = [
    path('khai-bao-thong-tin-ccdc/', include('django_ledger.api.routers.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc.urls')),
    path('dieu-chinh-gia-tri-ccdc/', include('django_ledger.api.routers.cong_cu.khai_bao_tang_giam_ccdc.dieu_chinh_gia_tri_ccdc.urls')),
]
