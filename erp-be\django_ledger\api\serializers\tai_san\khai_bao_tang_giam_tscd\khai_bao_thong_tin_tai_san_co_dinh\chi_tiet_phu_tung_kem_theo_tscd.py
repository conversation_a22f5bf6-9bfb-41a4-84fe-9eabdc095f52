"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietPhuTungKemTheoTSCD (Fixed Asset Accessory Detail) model
"""

from rest_framework import serializers

from django_ledger.models import ChiTietPhuTungKemTheoTSCDModel
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer


class ChiTietPhuTungKemTheoTSCDSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietPhuTungKemTheoTSCDModel
    """
    dvt_data = serializers.SerializerMethodField()

    class Meta:
        model = ChiTietPhuTungKemTheoTSCDModel
        fields = [
            'uuid',
            'khai_bao_thong_tin_tai_san_co_dinh',
            'line',
            'ten_ptts',
            'dvt',
            'dvt_data',
            'so_luong',
            'gia_tri_nt',
            'gia_tri',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated']

    def get_dvt_data(self, instance):
        """
        Get unit of measure data
        """
        if instance.dvt:
            return DonViTinhSerializer(instance.dvt).data
        return None
