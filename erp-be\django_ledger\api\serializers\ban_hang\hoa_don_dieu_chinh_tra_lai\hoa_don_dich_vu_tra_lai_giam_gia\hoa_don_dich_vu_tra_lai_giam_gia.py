"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for HoaDonDichVuTraLaiGiamGia (Service Invoice Return/Discount) model.
"""

from rest_framework import serializers

from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dich_vu_tra_lai_giam_gia import HoaDonDichVuTraLaiGiamGiaModel
from django_ledger.services.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dich_vu_tra_lai_giam_gia import HoaDonDichVuTraLaiGiamGiaService



class HoaDonDichVuTraLaiGiamGiaModelSerializer(serializers.ModelSerializer):
    """
    Serializer for HoaDonDichVuTraLaiGiamGia model.
    """
    

    ma_ngv_data = serializers.SerializerMethodField()
    ma_kh_data = serializers.SerializerMethod<PERSON>ield()
    tk_data = serializers.SerializerMethodField()
    ma_nk_data = serializers.SerializerMethodField()
    so_ct_data = serializers.SerializerMethodField()
    ma_nt_data = serializers.SerializerMethodField()
    chi_tiet_count = serializers.SerializerMethodField()
    
    class Meta:
        model = HoaDonDichVuTraLaiGiamGiaModel
        fields = '__all__'
        read_only_fields = ['uuid', 'created', 'updated', 'entity_model']
    
    
    def get_ma_ngv_data(self, obj):
        """
        Get ma_ngv data.
        """
        if obj.ma_ngv:
            return {
                'uuid': obj.ma_ngv.uuid,
                'ma_nv': obj.ma_ngv.ma_nv,
                'ten_nv': obj.ma_ngv.ten_nv
            }
        return None
    
    def get_ma_kh_data(self, obj):
        """
        Get ma_kh data.
        """
        if obj.ma_kh:
            return {
                'uuid': obj.ma_kh.uuid,
                'customer_name': obj.ma_kh.customer_name,
                'customer_number': obj.ma_kh.customer_number
            }
        return None
    
    def get_tk_data(self, obj):
        """
        Get tk data.
        """
        if obj.tk:
            return {
                'uuid': obj.tk.uuid,
                'code': obj.tk.code,
                'name': obj.tk.name
            }
        return None
    
    def get_ma_nk_data(self, obj):
        """
        Get ma_nk data.
        """
        if obj.ma_nk:
            return {
                'uuid': obj.ma_nk.uuid,
                'ma_quyen': obj.ma_nk.ma_quyen,
                'ten_quyen': obj.ma_nk.ten_quyen
            }
        return None
    
    def get_so_ct_data(self, obj):
        """
        Get so_ct data.
        """
        if obj.so_ct:
            return {
                'uuid': obj.so_ct.uuid,
                'so_ct': obj.so_ct.so_ct,
                'ngay_ct': obj.so_ct.ngay_ct
            }
        return None
    
    def get_ma_nt_data(self, obj):
        """
        Get ma_nt data.
        """
        if obj.ma_nt:
            return {
                'uuid': obj.ma_nt.uuid,
                'ma_nt': obj.ma_nt.ma_nt,
                'ten_nt': obj.ma_nt.ten_nt
            }
        return None
    
    def get_chi_tiet_count(self, obj):
        """
        Get chi_tiet count.
        """
        return obj.chi_tiet.count()


class HoaDonDichVuTraLaiGiamGiaModelCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating HoaDonDichVuTraLaiGiamGia model.
    """
    
    chi_tiet = serializers.ListField(required=False, write_only=True)
    
    class Meta:
        model = HoaDonDichVuTraLaiGiamGiaModel
        fields = '__all__'
        read_only_fields = ['uuid', 'created', 'updated']
    
    def create(self, validated_data):
        """
        Create a new HoaDonDichVuTraLaiGiamGia instance.
        """
        chi_tiet_data = validated_data.pop('chi_tiet', None)
        
        service = HoaDonDichVuTraLaiGiamGiaService()
        instance = service.create(validated_data, chi_tiet_data)
        
        return instance
    
    def update(self, instance, validated_data):
        """
        Update an existing HoaDonDichVuTraLaiGiamGia instance.
        """
        chi_tiet_data = validated_data.pop('chi_tiet', None)
        
        service = HoaDonDichVuTraLaiGiamGiaService()
        instance = service.update(instance.uuid, validated_data)
        
        # If chi_tiet_data is provided, handle it separately
        if chi_tiet_data:
            from django_ledger.services.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dich_vu_tra_lai_giam_gia import ChiTietHoaDonDichVuTraLaiGiamGiaService
            chi_tiet_service = ChiTietHoaDonDichVuTraLaiGiamGiaService()
            
            # Add the hoa_don reference to each chi_tiet
            for chi_tiet in chi_tiet_data:
                chi_tiet['hoa_don'] = instance.uuid
                
                # If uuid is provided, update the existing chi_tiet
                if 'uuid' in chi_tiet:
                    uuid = chi_tiet.pop('uuid')
                    chi_tiet_service.update(uuid, chi_tiet)
                else:
                    # Otherwise, create a new chi_tiet
                    chi_tiet_service.create(chi_tiet)
        
        return instance
