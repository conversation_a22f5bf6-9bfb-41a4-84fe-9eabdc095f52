"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for NganHang model.
"""

from rest_framework import serializers

from django_ledger.models import NganHangModel


class NganHangModelSerializer(serializers.ModelSerializer):
    """
    Serializer for NganHangModel.
    """

    class Meta:
        model = NganHangModel
        fields = [
            'uuid',
            'entity_model',
            'action',
            'param',
            'ma_ngan_hang',
            'ten_ngan_hang',
            'ten_ngan_hang2',
            'ma_so',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated', 'entity_model']
        swagger_schema_fields = {
            'title': 'NganHang',
            'description': 'Ngân hàng model serializer'
        }

    def validate_ma_ngan_hang(self, value):
        """
        Validate ma_ngan_hang field
        
        Parameters
        ----------
        value : str
            The ma_ngan_hang value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Mã ngân hàng không được để trống')
        return value.strip()

    def validate_ten_ngan_hang(self, value):
        """
        Validate ten_ngan_hang field
        
        Parameters
        ----------
        value : str
            The ten_ngan_hang value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Tên ngân hàng không được để trống')
        return value.strip()

    def validate_action(self, value):
        """
        Validate action field
        
        Parameters
        ----------
        value : str
            The action value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Hành động không được để trống')
        return value.strip()

    def create(self, validated_data):
        """
        Override create method to handle entity_model
        
        Parameters
        ----------
        validated_data : dict
            The validated data
            
        Returns
        -------
        NganHangModel
            The created NganHangModel instance
        """
        # Remove entity_model from validated_data to prevent unexpected keyword argument error
        if 'entity_model' in validated_data:
            validated_data.pop('entity_model')
        return super().create(validated_data)
