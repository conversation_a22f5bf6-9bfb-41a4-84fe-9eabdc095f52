"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ThueButToanHuy model.
"""

from rest_framework import serializers
from django_ledger.models import ThueButToanHuyModel
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.accounts import AccountModelSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.chung_tu import ChungTuSerializer
from django_ledger.api.serializers.han_thanh_toan import HanThanhToanModelSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.tien_do_thanh_toan import DotThanhToanModelSerializer
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import KheUocModelSerializer
from django_ledger.api.serializers.danh_muc import PhiSerializer, ChiPhiKhongHopLeSerializer

class ThueButToanHuySerializer(serializers.ModelSerializer):
    """
    Serializer for ThueButToanHuy model.
    """
    # Read-only fields for related objects
    but_toan_huy_data = serializers.SerializerMethodField(read_only=True)
    so_ct0_data = serializers.SerializerMethodField(read_only=True)
    so_ct2_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_no_data = serializers.SerializerMethodField(read_only=True)
    tk_du_data = serializers.SerializerMethodField(read_only=True)
    ma_kh9_data = serializers.SerializerMethodField(read_only=True)
    ma_tt_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_dtt_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_cp0_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ThueButToanHuyModel
        fields = [
            'uuid',
            'entity_model',
            'but_toan_huy',
            'but_toan_huy_data',
            'line',
            'so_ct0',
            'so_ct0_data',
            'so_ct2',
            'so_ct2_data',
            'ngay_ct0',
            'ma_thue',
            'thue_suat',
            'ma_mau_ct',
            'ma_mau_bc',
            'ma_tc_thue',
            'ma_kh',
            'ma_kh_data',
            'ten_kh_thue',
            'dia_chi',
            'ma_so_thue',
            'ten_vt_thue',
            't_tien_nt',
            't_tien',
            'tk_thue_no',
            'tk_thue_no_data',
            'ten_tk_thue_no',
            'tk_du',
            'tk_du_data',
            'ten_tk_du',
            't_thue_nt',
            't_thue',
            'ma_kh9',
            'ma_kh9_data',
            'ten_kh9',
            'ma_tt',
            'ma_tt_data',
            'ten_tt',
            'ghi_chu',
            'id_tt',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_sp_data',
            'ma_lsx',
            'ma_cp0',
            'ma_cp0_data',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'but_toan_huy_data',
            'so_ct0_data',
            'so_ct2_data',
            'ma_kh_data',
            'tk_thue_no_data',
            'tk_du_data',
            'ma_kh9_data',
            'ma_tt_data',
            'ma_bp_data',
            'ma_vv_data',
            'ma_hd_data',
            'ma_dtt_data',
            'ma_ku_data',
            'ma_phi_data',
            'ma_sp_data',
            'ma_cp0_data',
            'created',
            'updated'
        ]

    def get_but_toan_huy_data(self, obj):
        """
        Get parent ButToanHuy data.
        """
        if obj.but_toan_huy:
            return {
                'uuid': str(obj.but_toan_huy.uuid),
                'ma_ngv': obj.but_toan_huy.ma_ngv,
                'dien_giai': obj.but_toan_huy.dien_giai
            }
        return None

    def get_so_ct0_data(self, obj):
        """
        Get original document data.
        """
        if obj.so_ct0:
            return ChungTuSerializer(obj.so_ct0).data
        return None

    def get_so_ct2_data(self, obj):
        """
        Get document 2 data.
        """
        if obj.so_ct2:
            return ChungTuSerializer(obj.so_ct2).data
        return None

    def get_ma_kh_data(self, obj):
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_tk_thue_no_data(self, obj):
        """
        Get tax debit account data.
        """
        if obj.tk_thue_no:
            return AccountModelSerializer(obj.tk_thue_no).data
        return None

    def get_tk_du_data(self, obj):
        """
        Get reserve account data.
        """
        if obj.tk_du:
            return AccountModelSerializer(obj.tk_du).data
        return None

    def get_ma_kh9_data(self, obj):
        """
        Get customer 9 data.
        """
        if obj.ma_kh9:
            return CustomerModelSerializer(obj.ma_kh9).data
        return None

    def get_ma_tt_data(self, obj):
        """
        Get payment term data.
        """
        if obj.ma_tt:
            return HanThanhToanModelSerializer(obj.ma_tt).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):
        """
        Get task/case data.
        """
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_hd_data(self, obj):
        """
        Get contract data.
        """
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_dtt_data(self, obj):
        """
        Get payment period data.
        """
        if obj.ma_dtt:
            return DotThanhToanModelSerializer(obj.ma_dtt).data
        return None

    def get_ma_ku_data(self, obj):
        """
        Get agreement data.
        """
        if obj.ma_ku:
            return KheUocModelSerializer(obj.ma_ku).data
        return None

    def get_ma_phi_data(self, obj):
        """
        Get fee data.
        """
        if obj.ma_phi:
            return PhiSerializer(obj.ma_phi).data
        return None

    def get_ma_sp_data(self, obj):
        """
        Get product/material data.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_cp0_data(self, obj):
        """
        Get invalid cost data.
        """
        if obj.ma_cp0:
            return ChiPhiKhongHopLeSerializer(obj.ma_cp0).data
        return None
