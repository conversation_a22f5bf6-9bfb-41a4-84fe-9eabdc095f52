"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ButToanHuy model.
"""

from rest_framework import serializers
from django_ledger.models import ButToanHuyModel
from django_ledger.api.serializers.unit import EntityUnitModelSimpleSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer
from django_ledger.api.serializers.tong_hop.but_toan_cuoi_ky.but_toan_huy.chi_tiet_but_toan_huy import ChiTietButToanHuySerializer
from django_ledger.api.serializers.tong_hop.but_toan_cuoi_ky.but_toan_huy.thue_but_toan_huy import ThueButToanHuySerializer
from django_ledger.api.serializers.quyen_chung_tu import QuyenChungTuListSerializer
from django_ledger.api.serializers.chung_tu import ChungTuSerializer


class ButToanHuySerializer(serializers.ModelSerializer):
    """
    Serializer for ButToanHuy model.
    """
    # Read-only fields for related objects
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)
    ma_nk_data = serializers.SerializerMethodField(read_only=True)
    so_ct_data = serializers.SerializerMethodField(read_only=True)

    # Child data
    chi_tiet_data = serializers.SerializerMethodField(read_only=True)
    thue_data = serializers.SerializerMethodField(read_only=True)

    # Write-only fields for child data
    chi_tiet = serializers.ListField(required=False, write_only=True)
    thue = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = ButToanHuyModel
        fields = [
            'uuid',
            'entity_model',
            'ma_ngv',
            'dien_giai',
            'id_goc',
            'unit_id',
            'unit_id_data',
            'id_progress',
            'xprogress',
            'i_so_ct',
            'ma_nk',
            'ma_nk_data',
            'so_ct',
            'so_ct_data',
            'ngay_ct',
            'ngay_lct',
            'xdatetime2',
            'ma_nt',
            'ma_nt_data',
            'ty_gia',
            'status',
            'transfer_yn',
            't_ps_no_nt',
            't_ps_no',
            't_ps_co_nt',
            't_ps_co',
            'xfile',
            'chi_tiet_data',
            'thue_data',
            'chi_tiet',
            'thue',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'unit_id_data',
            'ma_nt_data',
            'ma_nk_data',
            'so_ct_data',
            'chi_tiet_data',
            'thue_data',
            'created',
            'updated'
        ]

    def get_unit_id_data(self, obj):
        """
        Get entity unit data.
        """
        if obj.unit_id:
            return EntityUnitModelSimpleSerializer(obj.unit_id).data
        return None

    def get_ma_nt_data(self, obj):
        """
        Get currency data.
        """
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_ma_nk_data(self, obj):
        """
        Get journal code data.
        """
        if obj.ma_nk:
            return QuyenChungTuListSerializer(obj.ma_nk).data
        return None

    def get_so_ct_data(self, obj):
        """
        Get document number data.
        """
        if obj.so_ct:
            return ChungTuSerializer(obj.so_ct).data
        return None

    def get_chi_tiet_data(self, obj):
        """
        Get child details data.
        """
        if hasattr(obj, 'chi_tiet_list'):
            return ChiTietButToanHuySerializer(obj.chi_tiet_list, many=True).data
        return ChiTietButToanHuySerializer(obj.chi_tiet.all(), many=True).data

    def get_thue_data(self, obj):
        """
        Get tax details data.
        """
        if hasattr(obj, 'thue_list'):
            return ThueButToanHuySerializer(obj.thue_list, many=True).data
        return ThueButToanHuySerializer(obj.thong_tin_thue.all(), many=True).data
