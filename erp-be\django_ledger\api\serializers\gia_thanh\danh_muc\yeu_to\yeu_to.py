"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for YeuTo model.
"""

from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models import YeuToModel
from django_ledger.api.serializers.unit import EntityUnitModelSerializer
from django_ledger.api.serializers.group import GroupModelSerializer
from django_ledger.api.serializers.loai_yeu_to import LoaiYeuToModelSerializer
from django_ledger.api.serializers.accounts import AccountModelSerializer

# Removed import to break circular dependency


class YeuToModelSerializer(serializers.ModelSerializer):
    """
    Serializer for YeuTo model.
    """
    # Read-only fields for related objects
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    loai_yt_data = serializers.SerializerMethodField(read_only=True)
    nh_yt_data = serializers.SerializerMethodField(read_only=True)
    tk_no_data = serializers.SerializerMethodField(read_only=True)
    tk_co_data = serializers.SerializerMethodField(read_only=True)
    tk_dd_data = serializers.SerializerMethodField(read_only=True)
    hs_yt_data = serializers.SerializerMethodField(read_only=True)

    # Child data
    chi_tiet_data = serializers.SerializerMethodField(read_only=True)
    chi_tiet = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = YeuToModel
        fields = [
            'uuid',
            'entity_model',
            'unit_id',
            'unit_id_data',
            'ma_yt',
            'ten_yt',
            'ten_yt2',
            'loai_yt',
            'loai_yt_data',
            'nh_yt',
            'nh_yt_data',
            'kieu_yt',
            'cong_tru',
            'tk_no',
            'tk_no_data',
            'tk_co',
            'tk_co_data',
            'tk_dd',
            'tk_dd_data',
            'dd_ck',
            'th_bp0',
            'th_sp',
            'th_lsx',
            'th_bp',
            'th_nvl',
            'hs_yt',
            'hs_yt_data',
            'gt_ps',
            'pb_yn',
            'status',
            'chi_tiet_data',
            'chi_tiet',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'chi_tiet_data',
            'created',
            'updated'
        ]

    def get_unit_id_data(self, obj):
        """
        Get unit data.
        """
        if obj.unit_id:
            return EntityUnitModelSerializer(obj.unit_id).data
        return None

    def get_loai_yt_data(self, obj):
        """
        Get factor type data.
        """
        if obj.loai_yt:
            return LoaiYeuToModelSerializer(obj.loai_yt).data
        return None

    def to_representation(self, instance):
        """
        Override the default representation to handle UUID serialization.
        """
        representation = super().to_representation(instance)

        # Convert JSON fields to lists of strings
        json_fields = ['tk_no', 'tk_co', 'hs_yt']
        for field in json_fields:
            if field in representation and representation[field] is not None:
                if not isinstance(representation[field], list):
                    representation[field] = [str(representation[field])] if representation[field] else []
                else:
                    representation[field] = [str(item) if item is not None else '' for item in representation[field]]

        # Convert other UUID fields to strings
        uuid_fields = ['unit_id', 'loai_yt', 'nh_yt', 'tk_dd']
        for field in uuid_fields:
            if field in representation and representation[field] is not None:
                representation[field] = str(representation[field])

        return representation

    def get_nh_yt_data(self, obj):
        """
        Get factor group data.
        """
        if obj.nh_yt:
            return GroupModelSerializer(obj.nh_yt).data
        return None

    def get_tk_no_data(self, obj):
        """
        Get debit account data.
        """
        # Use standard naming pattern from utils
        if hasattr(obj, 'tk_no_data'):
            return [
                {
                    'uuid': str(account.uuid),
                    'code': account.code,
                    'name': account.name
                } if account else "notfound"
                for account in obj.tk_no_data
            ]
        return []

    def get_tk_co_data(self, obj):
        """
        Get credit account data.
        """
        # Use standard naming pattern from utils
        if hasattr(obj, 'tk_co_data'):
            return [
                {
                    'uuid': str(account.uuid),
                    'code': account.code,
                    'name': account.name
                } if account else "notfound"
                for account in obj.tk_co_data
            ]
        return []

    def get_tk_dd_data(self, obj):
        """
        Get offset account data.
        """
        if obj.tk_dd:
            return AccountModelSerializer(obj.tk_dd).data
        return None

    def get_hs_yt_data(self, obj):
        """
        Get factor coefficient data.
        """
        # Use standard naming pattern from utils
        if hasattr(obj, 'hs_yt_data'):
            result = []
            for factor in obj.hs_yt_data:
                if factor is None:
                    result.append("notfound")
                else:
                    try:
                        result.append({
                            'uuid': str(factor.uuid),
                            'ma_yt': factor.ma_yt if hasattr(factor, 'ma_yt') else None
                        })
                    except AttributeError:
                        # Handle case where factor doesn't have expected attributes
                        result.append("notfound")
            return result
        return []

    def get_chi_tiet_data(self, obj):
        """
        Get child detail data.
        """
        # Serializer không nên truy cập trực tiếp database
        # Dữ liệu này nên được chuẩn bị trước trong context hoặc từ service
        # Import here to avoid circular imports
        from django_ledger.api.serializers.gia_thanh.danh_muc.yeu_to.chi_tiet_yeu_to import YeuToChiTietModelSerializer

        if hasattr(obj, 'chi_tiet_prepared'):
            return YeuToChiTietModelSerializer(obj.chi_tiet_prepared, many=True).data
        # Fallback nếu không có dữ liệu được chuẩn bị trước
        return YeuToChiTietModelSerializer(obj.chi_tiet_yeu_to.all(), many=True).data


