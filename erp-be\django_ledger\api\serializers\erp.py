"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for ERP-related models

NOTE: This file is kept for backward compatibility.
All Serializers have been moved to separate files based on their functionality:
- common.py: Common utilities like StatusModelSerializer
- geography.py: Geography-related Serializers (QuocGia, TinhThanh, QuanHuyen, XaPhuong, KhuVuc)
- organization.py: Organization-related Serializers (BoPhan, NhanVien)
- warehouse.py: Warehouse-related Serializers (KhoHang, LoaiVatTu, VatTuSanPhamDonViTinh, VatTuSanPhamHangHoa, GiaMua)
- finance.py: Finance-related Serializers (NgoaiTe, HanThanhToan, PhuongThucThanhToan, ThueSuatThueGTGT, KheUoc, etc.)
- customer_erp.py: Customer-related Serializers (LoaiGiaBan, NhomKhachHang)
- misc.py: Miscellaneous Serializers (RangBuocNhap)

Please import from these files directly instead of from this file.
"""

from django_ledger.models import (
    BoPhanModel,
    CustomerModel,
    GiaMuaModel,
    KhoHangModel,
    KhuVucModel,
    KichCoModel,
    LoaiGiaBan,
    LoaiVatTuModel,
    NgoaiTeModel,
    NhanVienModel,
    NhomKhachHang,
    NhomPhiModel,
    PhiModel,
    PhuongThucThanhToanModel,
    QuanHuyenModel,
    QuocGiaModel,
    RangBuocNhapModel,
    ThueSuatThueGTGTModel,
    TinhThanhModel,
    VatTuSanPhamDonViTinhModel,
    VatTuSanPhamHangHoaModel,
    XaPhuongModel,
)

# These imports are kept for backward compatibility
from rest_framework import serializers


class KhoHangModelSerializer(serializers.ModelSerializer):
    unit_data = serializers.SerializerMethodField(read_only=True)

    def get_unit_data(self, obj):
        """
        Get the entity unit data corresponding to the unit_id field
        """
        from django_ledger.api.serializers.unit import EntityUnitModelSerializer

        if obj.unit_id:
            return EntityUnitModelSerializer(obj.unit_id).data
        return None

    class Meta:
        model = KhoHangModel
        fields = [
            "uuid",
            "don_vi",
            "unit_data",
            "ma_kho",
            "ten_kho",
            "ten_khac",
            "vi_tri",
            "dai_ly",
            "dia_chi",
            "dien_thoai",
            "trang_thai",
            "created",
            "updated",
        ]
        read_only_fields = ["uuid", "created", "updated", "unit_data"]


class NhanVienModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = NhanVienModel
        fields = [
            "uuid",
            "ma_nhan_vien",
            "nhan_vien_ban_hang",
            "nhan_vien_mua_hang",
            "chi_nhanh",
            "ho_ten_nhan_vien",
            "chuc_vu",
            "ma_bo_phan",
            "gioi_tinh",
            "ngay_sinh",
            "noi_sinh",
            "dia_chi",
            "dien_thoai",
            "email",
            "so_cmnd",
            "ngay_hieu_luc_cmnd",
            "noi_cap_cmnd",
            "so_the_ngan_hang",
            "ngan_hang",
            "id_nguoi_dung",
            "ten_dang_nhap",
            "trang_thai",
            "ngay_nghi",
            "ma_so_thue_thu_nhap_ca_nhan",
            "quyet_toan_thue_thu_nhap",
            "tinh_thue_thu_nhap",
            "luy_tien_thue_thu_nhap",
            "hop_dong_lao_dong",
            "thue_quyet_toan",
            "doi_tuong_thue_thu_nhap",
            "loai_giay_to",
            "so_giay_to",
            "created",
            "updated",
        ]
        read_only_fields = ["uuid", "created", "updated"]


class NgoaiTeModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = NgoaiTeModel
        fields = [
            "uuid",
            "ngoai_te",
            "ten_ngoai_te",
            "ten_khac",
            "tk_phat_sinh_cl_no",
            "tk_phat_sinh_cl_co",
            "stt",
            "doc_le",
            "cach_doc",
            "trang_thai",
            "created",
            "updated",
        ]
        read_only_fields = ["uuid", "created", "updated"]


class PhuongThucThanhToanModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the PhuongThucThanhToanModel (Payment Method) model.

    This serializer handles the conversion between PhuongThucThanhToanModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - ma_pttt: Unique code for the payment method
    - ten_pttt: Primary name of the payment method
    - ten_pttt2: Secondary/alternative name (optional)
    - ma_ph_thuc_hddt: Electronic invoice code
    - status: Status indicator ('0'=inactive, '1'=active)
    - created_at: Timestamp of creation (read-only)
    - updated_at: Timestamp of last update (read-only)
    """

    class Meta:
        model = PhuongThucThanhToanModel
        fields = [
            "uuid",
            "ma_pttt",
            "ten_pttt",
            "ten_pttt2",
            "ma_ph_thuc_hddt",
            "status",
            "created",
            "updated",
        ]
        read_only_fields = ["uuid", "created", "updated"]

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_pttt": "TM",
                "ten_pttt": "Tiền mặt",
                "ten_pttt2": "Cash",
                "ma_ph_thuc_hddt": "TM",
                "status": "1",
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z",
            }
        }


class LoaiVatTuModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = LoaiVatTuModel
        fields = [
            "uuid",
            "ma_loai_vat_tu",
            "ten_loai_vat_tu",
            "ten_khac",
            "trang_thai",
            "tk_kho",
            "tk_doanh_thu",
            "tk_gia_von",
            "created",
            "updated",
        ]
        read_only_fields = ["uuid", "created", "updated"]


class ThueSuatThueGTGTModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = ThueSuatThueGTGTModel
        fields = [
            "uuid",
            "ma_thue",
            "ten_thue",
            "ten_khac",
            "thue_suat",
            "thue_suat_hddt",
            "nhom_thue",
            "tk_thue_dau_ra",
            "tk_thue_dau_vao",
            "trang_thai",
            "created",
            "updated",
        ]
        read_only_fields = ["uuid", "created", "updated"]


class VatTuSanPhamDonViTinhModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = VatTuSanPhamDonViTinhModel
        fields = [
            "uuid",
            "vat_tu_san_pham_id",
            "don_vi_tinh_id",
            "ten_dvt",
            "he_so",
            "created",
            "updated",
        ]
        read_only_fields = ["uuid", "created", "updated"]


class VatTuSanPhamHangHoaModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = VatTuSanPhamHangHoaModel
        fields = ["uuid", "vat_tu_san_pham_id", "ma_hang_hoa", "created", "updated"]
        read_only_fields = ["uuid", "created", "updated"]


class GiaMuaModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = GiaMuaModel
        fields = [
            "uuid",
            "ma_vat_tu",
            "don_vi_tinh",
            "ngay_hieu_luc",
            "nha_cung_cap",
            "ngoai_te",
            "so_luong_tu",
            "gia_mua",
            "trang_thai",
            "created",
            "updated",
        ]
        read_only_fields = ["uuid", "created", "updated"]


class RangBuocNhapModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = RangBuocNhapModel
        fields = [
            "uuid",
            "tai_khoan_id",
            "ten_doi_tuong",
            "bat_buoc_nhap",
            "created",
            "updated",
        ]
        read_only_fields = ["uuid", "created", "updated"]


class NhomPhiModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = NhomPhiModel
        fields = [
            "uuid",
            "ma_nhom",
            "ten_phan_nhom",
            "ten_2",
            "trang_thai",
            "loai_nhom",
            "created",
            "updated",
        ]
        read_only_fields = ["uuid", "created", "updated"]


class PhiModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = PhiModel
        fields = [
            "uuid",
            "ma_phi",
            "ten_phi",
            "ten_khac",
            "nhom_phi_1",
            "nhom_phi_2",
            "nhom_phi_3",
            "bo_phan",
            "trang_thai",
            "created",
            "updated",
        ]
        read_only_fields = ["uuid", "created", "updated"]


class LoaiGiaBanSerializer(serializers.ModelSerializer):
    """
    Serializer for the LoaiGiaBan (Sales Price Type) model.

    This serializer handles the conversion between LoaiGiaBan model instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - ma_loai_gb: Primary key and unique code for the sales price type
    - ten_loai_gb: Primary name of the sales price type
    - ten_loai_gb2: Secondary/alternative name (optional)
    - status: Status indicator (0=inactive, >0=active)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    - created_by: Username of creator
    - updated_by: Username of last updater
    """

    class Meta:
        model = LoaiGiaBan
        fields = [
            "ma_loai_gb",
            "ten_loai_gb",
            "ten_loai_gb2",
            "status",
            "entity_model",
            "created",
            "updated",
        ]
        read_only_fields = ["created", "updated"]

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "ma_loai_gb": "BL",
                "ten_loai_gb": "Bán lẻ",
                "ten_loai_gb2": "Retail",
                "status": 1,
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z",
            }
        }


class NhomKhachHangSerializer(serializers.ModelSerializer):
    """
    Serializer for the NhomKhachHang (Customer Group) model.

    This serializer handles the conversion between NhomKhachHang model instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - ma_loai_nh: Code for the customer group type
    - ma_nh: Code for the customer group
    - ten_nh: Primary name of the customer group
    - ten_nh2: Secondary/alternative name (optional)
    - status: Status indicator ('0'=inactive, '1'=active)
    - ngay_tao: Timestamp of creation (read-only)
    - ngay_cap_nhat: Timestamp of last update (read-only)
    """

    class Meta:
        model = NhomKhachHang
        fields = [
            "ma_loai_nh",
            "ma_nh",
            "ten_nh",
            "ten_nh2",
            "status",
            "ngay_tao",
            "ngay_cap_nhat",
        ]
        read_only_fields = ["ngay_tao", "ngay_cap_nhat"]

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "ma_loai_nh": "KH1",
                "ma_nh": "VIP",
                "ten_nh": "Khách hàng VIP",
                "ten_nh2": "VIP Customer",
                "status": "1",
                "ngay_tao": "2023-04-21T10:30:00Z",
                "ngay_cap_nhat": "2023-04-21T10:30:00Z",
            }
        }


class QuocGiaModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the QuocGiaModel (Country) model.

    This serializer handles the conversion between QuocGiaModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - ma_qg: Country code (e.g., US, VN, JP)
    - ten_qg: Primary name of the country
    - ten_qg2: Secondary/alternative name (optional)
    - status: Status indicator ('0'=inactive, '1'=active)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    """

    class Meta:
        model = QuocGiaModel
        fields = ["uuid", "ma_qg", "ten_qg", "ten_qg2", "status", "created", "updated"]
        read_only_fields = ["uuid", "created", "updated"]

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_qg": "VN",
                "ten_qg": "Việt Nam",
                "ten_qg2": "Vietnam",
                "status": "1",
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z",
            }
        }


class TinhThanhModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the TinhThanhModel (Province/City) model.

    This serializer handles the conversion between TinhThanhModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - ma_tinh: Province/City code
    - ten_tinh: Primary name of the province/city
    - ten_tinh2: Secondary/alternative name (optional)
    - status: Status indicator ('0'=inactive, '1'=active)
    - created_at: Timestamp of creation (read-only)
    - updated_at: Timestamp of last update (read-only)
    """

    class Meta:
        model = TinhThanhModel
        fields = [
            "uuid",
            "ma_tinh",
            "ten_tinh",
            "ten_tinh2",
            "status",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["uuid", "created_at", "updated_at"]

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_tinh": "HN",
                "ten_tinh": "Hà Nội",
                "ten_tinh2": "Hanoi",
                "status": "1",
                "created_at": "2023-04-21T10:30:00Z",
                "updated_at": "2023-04-21T10:30:00Z",
            }
        }


class CustomerModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the CustomerModel

    This serializer includes additional fields with "_data" suffix for all reference fields,
    providing the complete nested object data for each reference.
    """

    # Reference data fields
    sales_rep_data = serializers.SerializerMethodField()
    account_data = serializers.SerializerMethodField()
    payment_term_data = serializers.SerializerMethodField()
    payment_method_data = serializers.SerializerMethodField()
    customer_group1_data = serializers.SerializerMethodField()
    customer_group2_data = serializers.SerializerMethodField()
    customer_group3_data = serializers.SerializerMethodField()
    region_data = serializers.SerializerMethodField()

    class Meta:
        model = CustomerModel
        fields = [
            "uuid",
            "customer_code",
            "customer_type",
            "is_customer",
            "is_vendor",
            "customer_name",
            "alternative_name",
            "address",
            "tax_code",
            "contact_person",
            "enterprise_name",
            "phone",
            "fax",
            "email",
            "website",
            "legal_representative",
            "representative_position",
            "representative",
            "account",
            "account_data",
            "payment_term",
            "payment_term_data",
            "payment_method",
            "payment_method_data",
            "credit_limit",
            "bank_account",
            "bank_name",
            "bank_branch",
            "customer_group1",
            "customer_group1_data",
            "customer_group2",
            "customer_group2_data",
            "customer_group3",
            "customer_group3_data",
            "region",
            "region_data",
            "sales_rep",
            "sales_rep_data",
            "search_keywords",
            "province",
            "notes",
            "status",
            "birth_date",
            "id_number",
            "description",
            "delivery_address",
            "business_field",
            "use_einvoice",
            "einvoice_email",
            "customer_number",
            "entity_model",
            "active",
            "hidden",
            "additional_info",
            "description",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "customer_number",
            "created",
            "updated",
            "sales_rep_data",
            "account_data",
            "payment_term_data",
            "payment_method_data",
            "customer_group1_data",
            "customer_group2_data",
            "customer_group3_data",
            "region_data",
        ]

    def get_sales_rep_data(self, obj):
        """
        Returns the sales representative data for the sales_rep field.
        """
        if obj.sales_rep:
            from django_ledger.api.serializers.nhan_vien import NhanVienModelSerializer

            return NhanVienModelSerializer(obj.sales_rep).data
        return None

    def get_account_data(self, obj):
        """
        Returns the account data for the account field.
        """
        if obj.account:
            from django_ledger.api.serializers.account import AccountModelSerializer

            return AccountModelSerializer(obj.account).data
        return None

    def get_payment_term_data(self, obj):
        """
        Returns the payment term data for the payment_term field.
        """
        if obj.payment_term:
            from django_ledger.api.serializers.han_thanh_toan import (
                HanThanhToanModelSerializer,
            )

            return HanThanhToanModelSerializer(obj.payment_term).data
        return None

    def get_payment_method_data(self, obj):
        """
        Returns the payment method data for the payment_method field.
        """
        if obj.payment_method:
            from django_ledger.api.serializers.finance import (
                PhuongThucThanhToanModelSerializer,
            )

            return PhuongThucThanhToanModelSerializer(obj.payment_method).data
        return None

    def get_customer_group1_data(self, obj):
        """
        Returns the customer group 1 data for the customer_group1 field.
        """
        if obj.customer_group1:
            from django_ledger.api.serializers.group import GroupModelSerializer

            return GroupModelSerializer(obj.customer_group1).data
        return None

    def get_customer_group2_data(self, obj):
        """
        Returns the customer group 2 data for the customer_group2 field.
        """
        if obj.customer_group2:
            from django_ledger.api.serializers.group import GroupModelSerializer

            return GroupModelSerializer(obj.customer_group2).data
        return None

    def get_customer_group3_data(self, obj):
        """
        Returns the customer group 3 data for the customer_group3 field.
        """
        if obj.customer_group3:
            from django_ledger.api.serializers.group import GroupModelSerializer

            return GroupModelSerializer(obj.customer_group3).data
        return None

    def get_region_data(self, obj):
        """
        Returns the region data for the region field.
        """
        if obj.region:
            from django_ledger.api.serializers.khu_vuc import KhuVucModelSerializer

            return KhuVucModelSerializer(obj.region).data
        return None


class VendorModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the VendorModel (which uses CustomerModel)

    This serializer includes additional fields with "_data" suffix for all reference fields,
    providing the complete nested object data for each reference.
    """

    # Reference data fields
    sales_rep_data = serializers.SerializerMethodField()
    account_data = serializers.SerializerMethodField()
    payment_term_data = serializers.SerializerMethodField()
    payment_method_data = serializers.SerializerMethodField()
    customer_group1_data = serializers.SerializerMethodField()
    customer_group2_data = serializers.SerializerMethodField()
    customer_group3_data = serializers.SerializerMethodField()
    region_data = serializers.SerializerMethodField()

    class Meta:
        model = CustomerModel
        fields = [
            "uuid",
            "customer_code",
            "customer_type",
            "is_customer",
            "is_vendor",
            "customer_name",
            "alternative_name",
            "address",
            "tax_code",
            "contact_person",
            "enterprise_name",
            "phone",
            "fax",
            "email",
            "website",
            "legal_representative",
            "representative_position",
            "representative",
            "account",
            "account_data",
            "payment_term",
            "payment_term_data",
            "payment_method",
            "payment_method_data",
            "credit_limit",
            "bank_account",
            "bank_name",
            "bank_branch",
            "customer_group1",
            "customer_group1_data",
            "customer_group2",
            "customer_group2_data",
            "customer_group3",
            "customer_group3_data",
            "region",
            "region_data",
            "sales_rep",
            "sales_rep_data",
            "search_keywords",
            "province",
            "notes",
            "status",
            "birth_date",
            "id_number",
            "description",
            "delivery_address",
            "business_field",
            "use_einvoice",
            "einvoice_email",
            "customer_number",
            "entity_model",
            "active",
            "hidden",
            "additional_info",
            "description",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "customer_number",
            "created",
            "updated",
            "sales_rep_data",
            "account_data",
            "payment_term_data",
            "payment_method_data",
            "customer_group1_data",
            "customer_group2_data",
            "customer_group3_data",
            "region_data",
        ]

    def get_sales_rep_data(self, obj):
        """
        Returns the sales representative data for the sales_rep field.
        """
        if obj.sales_rep:
            from django_ledger.api.serializers.nhan_vien import NhanVienModelSerializer

            return NhanVienModelSerializer(obj.sales_rep).data
        return None

    def get_account_data(self, obj):
        """
        Returns the account data for the account field.
        """
        if obj.account:
            from django_ledger.api.serializers.account import AccountModelSerializer

            return AccountModelSerializer(obj.account).data
        return None

    def get_payment_term_data(self, obj):
        """
        Returns the payment term data for the payment_term field.
        """
        if obj.payment_term:
            from django_ledger.api.serializers.han_thanh_toan import (
                HanThanhToanModelSerializer,
            )

            return HanThanhToanModelSerializer(obj.payment_term).data
        return None

    def get_payment_method_data(self, obj):
        """
        Returns the payment method data for the payment_method field.
        """
        if obj.payment_method:
            from django_ledger.api.serializers.finance import (
                PhuongThucThanhToanModelSerializer,
            )

            return PhuongThucThanhToanModelSerializer(obj.payment_method).data
        return None

    def get_customer_group1_data(self, obj):
        """
        Returns the customer group 1 data for the customer_group1 field.
        """
        if obj.customer_group1:
            from django_ledger.api.serializers.group import GroupModelSerializer

            return GroupModelSerializer(obj.customer_group1).data
        return None

    def get_customer_group2_data(self, obj):
        """
        Returns the customer group 2 data for the customer_group2 field.
        """
        if obj.customer_group2:
            from django_ledger.api.serializers.group import GroupModelSerializer

            return GroupModelSerializer(obj.customer_group2).data
        return None

    def get_customer_group3_data(self, obj):
        """
        Returns the customer group 3 data for the customer_group3 field.
        """
        if obj.customer_group3:
            from django_ledger.api.serializers.group import GroupModelSerializer

            return GroupModelSerializer(obj.customer_group3).data
        return None

    def get_region_data(self, obj):
        """
        Returns the region data for the region field.
        """
        if obj.region:
            from django_ledger.api.serializers.khu_vuc import KhuVucModelSerializer

            return KhuVucModelSerializer(obj.region).data
        return None


class QuanHuyenModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the QuanHuyenModel (District) model.

    This serializer handles the conversion between QuanHuyenModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - ma_quan: District code
    - ten_quan: Primary name of the district
    - ten_quan2: Secondary/alternative name (optional)
    - ma_tinh: Reference to the province/city (ForeignKey to TinhThanhModel)
    - tinh_thanh_name: Name of the province/city (read-only)
    - status: Status indicator ('0'=inactive, '1'=active)
    - created_at: Timestamp of creation (read-only)
    - updated_at: Timestamp of last update (read-only)
    """

    tinh_thanh_name = serializers.CharField(source="ma_tinh.ten_tinh", read_only=True)

    class Meta:
        model = QuanHuyenModel
        fields = [
            "uuid",
            "ma_quan",
            "ten_quan",
            "ten_quan2",
            "ma_tinh",
            "tinh_thanh_name",
            "status",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["uuid", "created_at", "updated_at"]

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_quan": "Q1",
                "ten_quan": "Quận 1",
                "ten_quan2": "District 1",
                "ma_tinh": "123e4567-e89b-12d3-a456-************",
                "tinh_thanh_name": "Hồ Chí Minh",
                "status": "1",
                "created_at": "2023-04-21T10:30:00Z",
                "updated_at": "2023-04-21T10:30:00Z",
            }
        }


class XaPhuongModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the XaPhuongModel (Ward) model.

    This serializer handles the conversion between XaPhuongModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - ma_xp: Ward code
    - ten_xp: Primary name of the ward
    - ten_xp2: Secondary/alternative name (optional)
    - ma_tinh: Reference to the province/city (ForeignKey to TinhThanhModel)
    - ma_quan: Reference to the district (ForeignKey to QuanHuyenModel)
    - tinh_thanh_name: Name of the province/city (read-only)
    - quan_huyen_name: Name of the district (read-only)
    - status: Status indicator ('0'=inactive, '1'=active)
    - created_at: Timestamp of creation (read-only)
    - updated_at: Timestamp of last update (read-only)
    """

    tinh_thanh_name = serializers.CharField(source="ma_tinh.ten_tinh", read_only=True)
    quan_huyen_name = serializers.CharField(source="ma_quan.ten_quan", read_only=True)

    class Meta:
        model = XaPhuongModel
        fields = [
            "uuid",
            "ma_xp",
            "ten_xp",
            "ten_xp2",
            "ma_tinh",
            "ma_quan",
            "tinh_thanh_name",
            "quan_huyen_name",
            "status",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["uuid", "created_at", "updated_at"]

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_xp": "P01",
                "ten_xp": "Phường Bến Nghé",
                "ten_xp2": "Ben Nghe Ward",
                "ma_tinh": "123e4567-e89b-12d3-a456-************",
                "ma_quan": "123e4567-e89b-12d3-a456-************",
                "tinh_thanh_name": "Hồ Chí Minh",
                "quan_huyen_name": "Quận 1",
                "status": "1",
                "created_at": "2023-04-21T10:30:00Z",
                "updated_at": "2023-04-21T10:30:00Z",
            }
        }


class KhuVucModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the KhuVucModel (Region) model.

    This serializer handles the conversion between KhuVucModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - rg_code: Region code
    - rgname: Primary name of the region
    - rgname2: Secondary/alternative name (optional)
    - parent_rg: Reference to the parent region (self-reference)
    - parent_name: Name of the parent region (read-only)
    - stt: Order number
    - status: Status indicator ('0'=inactive, '1'=active)
    - created_at: Timestamp of creation (read-only)
    - updated_at: Timestamp of last update (read-only)
    """

    parent_name = serializers.CharField(source="parent_rg.rgname", read_only=True)

    class Meta:
        model = KhuVucModel
        fields = [
            "uuid",
            "rg_code",
            "rgname",
            "rgname2",
            "parent_rg",
            "parent_name",
            "stt",
            "status",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["uuid", "created_at", "updated_at"]

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "rg_code": "MN",
                "rgname": "Miền Nam",
                "rgname2": "Southern Region",
                "parent_rg": None,
                "parent_name": None,
                "stt": 1.0,
                "status": "1",
                "created_at": "2023-04-21T10:30:00Z",
                "updated_at": "2023-04-21T10:30:00Z",
            }
        }


class BoPhanModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the BoPhanModel (Department) model.

    This serializer handles the conversion between BoPhanModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - ma_bp: Department code
    - ten_bp: Primary name of the department
    - ten_bp2: Secondary/alternative name (optional)
    - ghi_chu: Notes or description for the department (optional)
    - status: Status indicator ('0'=inactive, '1'=active)
    - created_at: Timestamp of creation (read-only)
    - updated_at: Timestamp of last update (read-only)
    - created_by: Username of creator
    - updated_by: Username of last updater
    """

    class Meta:
        model = BoPhanModel
        fields = [
            "uuid",
            "ma_bp",
            "ten_bp",
            "ten_bp2",
            "ghi_chu",
            "status",
            "entity_model" "created",
            "updated",
            "created_by",
            "updated_by",
        ]
        read_only_fields = ["uuid", "created", "updated"]

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_bp": "KT",
                "ten_bp": "Kế toán",
                "ten_bp2": "Accounting",
                "ghi_chu": "Bộ phận kế toán của công ty",
                "status": "1",
                "created_at": "2023-04-21T10:30:00Z",
                "updated_at": "2023-04-21T10:30:00Z",
                "created_by": "admin",
                "updated_by": "admin",
            }
        }


class KichCoModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = KichCoModel
        fields = [
            "uuid",
            "ma_kich_co",
            "ten_kich_co",
            "ten_kich_co2",
            "status",
            "entity_model",
            "created",
            "updated",
        ]
        read_only_fields = ["uuid", "created", "updated"]
