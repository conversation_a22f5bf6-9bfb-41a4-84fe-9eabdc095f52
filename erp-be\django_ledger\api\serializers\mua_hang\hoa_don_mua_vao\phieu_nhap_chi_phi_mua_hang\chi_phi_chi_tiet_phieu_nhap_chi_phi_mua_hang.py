"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiPhiChiTietPhieuNhapChiPhiMuahangSerializer, which handles serialization
for the ChiPhiChiTietPhieuNhapChiPhiMuahangModel.
"""

from rest_framework import serializers

from django_ledger.models.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import ChiPhiChiTietPhieuNhapChiPhiMuahangModel


class ChiPhiChiTietPhieuNhapChiPhiMuahangSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiPhiChiTietPhieuNhapChiPhiMuahangModel.
    """

    # Reference data fields
    ma_cp_data = serializers.SerializerMethodField()
    ma_vt_data = serializers.SerializerMethodField()

    class Meta:
        model = ChiPhiChiTietPhieuNhapChiPhiMuahangModel
        fields = [
            'uuid', 'phieu_nhap', 'line', 'ma_cp', 'ma_vt', 'tien_cp_nt', 'tien_cp', 'line_vt',
            'ma_cp_data', 'ma_vt_data'
        ]
        read_only_fields = ['uuid', 'phieu_nhap']

    def get_ma_cp_data(self, obj):
        """
        Returns the expense data for the ma_cp field.
        """
        if obj.ma_cp:
            return {
                'uuid': obj.ma_cp.uuid,
                'ma_cp': obj.ma_cp.ma_cp,
                'ten_cp': obj.ma_cp.ten_cp
            }
        return None

    def get_ma_vt_data(self, obj):
        """
        Returns the material data for the ma_vt field.
        """
        if obj.ma_vt:
            return {
                'uuid': obj.ma_vt.uuid,
                'ma_vt': obj.ma_vt.ma_vt,
                'ten_vt': obj.ma_vt.ten_vt
            }
        return None


