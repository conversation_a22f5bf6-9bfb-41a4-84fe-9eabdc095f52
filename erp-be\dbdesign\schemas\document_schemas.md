# S<PERSON> Đồ Cơ Sở Dữ Liệu Chứng Từ - Django Ledger

Tài liệu này mô tả chi tiết cấu trúc cơ sở dữ liệu cho các thực thể liên quan đến chứng từ kinh doanh trong hệ thống Django Ledger.

## <PERSON><PERSON><PERSON>

- [<PERSON><PERSON> Đồ Cơ Sở Dữ Liệu Chứng Từ - Django Ledger](#sơ-đồ-cơ-sở-dữ-liệu-chứng-từ---django-ledger)
  - [<PERSON><PERSON><PERSON>](#mục-lục)
  - [CustomerModel](#customermodel)
  - [VendorModel](#vendormodel)
  - [BillModel](#billmodel)
  - [InvoiceModel](#invoicemodel)
  - [PurchaseOrderModel](#purchaseordermodel)
  - [EstimateModel](#estimatemodel)
  - [BankAccountModel](#bankaccountmodel)
  - [MuaHangModel](#muahangmodel)

## CustomerModel

Đại diện cho một khách hàng, tứ<PERSON> là người mua hàng hoặc dịch vụ từ đơn vị.

| Tr<PERSON>ờng | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| customer_number | CharField | Mã khách hàng duy nhất |
| customer_name | CharField | Tên khách hàng |
| entity_model | ForeignKey(EntityModel) | Đơn vị liên quan |
| address_1 | CharField | Địa chỉ dòng 1 |
| address_2 | CharField | Địa chỉ dòng 2 (tùy chọn) |
| city | CharField | Thành phố |
| state | CharField | Tỉnh/Bang |
| zip_code | CharField | Mã bưu chính |
| country | CharField | Quốc gia |
| phone | CharField | Số điện thoại |
| email | EmailField | Địa chỉ email |
| website | URLField | Website (tùy chọn) |
| active | BooleanField | Khách hàng có đang hoạt động |
| hidden | BooleanField | Khách hàng có bị ẩn |
| description | TextField | Mô tả (tùy chọn) |
| additional_info | JSONField | Thông tin bổ sung |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: CustomerModel lưu trữ thông tin về khách hàng. Khách hàng liên kết với hóa đơn và báo giá. Thông tin liên hệ và thông tin bổ sung được lưu trữ để tạo điều kiện cho các tương tác với khách hàng.

## VendorModel

Đại diện cho một nhà cung cấp, tức là người bán hàng hoặc dịch vụ cho đơn vị.

| Trường | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| vendor_number | CharField | Mã nhà cung cấp duy nhất |
| vendor_name | CharField | Tên nhà cung cấp |
| entity_model | ForeignKey(EntityModel) | Đơn vị liên quan |
| address_1 | CharField | Địa chỉ dòng 1 |
| address_2 | CharField | Địa chỉ dòng 2 (tùy chọn) |
| city | CharField | Thành phố |
| state | CharField | Tỉnh/Bang |
| zip_code | CharField | Mã bưu chính |
| country | CharField | Quốc gia |
| phone | CharField | Số điện thoại |
| email | EmailField | Địa chỉ email |
| website | URLField | Website (tùy chọn) |
| active | BooleanField | Nhà cung cấp có đang hoạt động |
| hidden | BooleanField | Nhà cung cấp có bị ẩn |
| description | TextField | Mô tả (tùy chọn) |
| additional_info | JSONField | Thông tin bổ sung |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: VendorModel lưu trữ thông tin về nhà cung cấp. Nhà cung cấp liên kết với hóa đơn mua hàng và đơn đặt hàng. Thông tin liên hệ và thông tin bổ sung được lưu trữ để tạo điều kiện cho các tương tác với nhà cung cấp.

## BillModel

Đại diện cho một hóa đơn mua hàng của nhà cung cấp.

| Trường | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| vendor | ForeignKey(VendorModel) | Nhà cung cấp phát hành hóa đơn |
| terms | CharField | Điều khoản thanh toán |
| xref | CharField | Tham chiếu bên ngoài (số hóa đơn của nhà cung cấp) |
| bill_number | SlugField | Số hóa đơn mua hàng duy nhất |
| bill_status | CharField | Trạng thái (nháp, đang xem xét, đã duyệt, v.v.) |
| ledger | OneToOneField(LedgerModel) | Sổ cái liên quan |
| cash_account | ForeignKey(AccountModel) | Tài khoản tiền mặt |
| prepaid_account | ForeignKey(AccountModel) | Tài khoản trả trước |
| unearned_account | ForeignKey(AccountModel) | Tài khoản phải trả |
| date_draft | DateField | Ngày nháp |
| date_in_review | DateField | Ngày xem xét |
| date_approved | DateField | Ngày duyệt |
| date_paid | DateField | Ngày thanh toán |
| date_void | DateField | Ngày hủy |
| date_canceled | DateField | Ngày hủy bỏ |
| amount_due | DecimalField | Số tiền phải trả |
| amount_paid | DecimalField | Số tiền đã trả |
| markdown_notes | TextField | Ghi chú định dạng Markdown |
| additional_info | JSONField | Thông tin bổ sung |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: BillModel đại diện cho các hóa đơn mua hàng từ nhà cung cấp. Mỗi hóa đơn mua hàng liên kết với sổ cái có chứa các bút toán.

**Điểm đặc biệt**:
- BillModel theo dõi chu kỳ sống đầy đủ của hóa đơn mua hàng từ nháp đến thanh toán.
- Các tài khoản khác nhau được liên kết để xử lý kế toán tự động (tiền mặt, trả trước, phải trả).

## InvoiceModel

Đại diện cho một hóa đơn phát hành cho khách hàng.

| Trường | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| customer | ForeignKey(CustomerModel) | Khách hàng nhận hóa đơn |
| terms | CharField | Điều khoản thanh toán |
| invoice_number | SlugField | Số hóa đơn duy nhất |
| invoice_status | CharField | Trạng thái (nháp, đang xem xét, đã duyệt, v.v.) |
| ledger | OneToOneField(LedgerModel) | Sổ cái liên quan |
| cash_account | ForeignKey(AccountModel) | Tài khoản tiền mặt |
| prepaid_account | ForeignKey(AccountModel) | Tài khoản phải thu |
| unearned_account | ForeignKey(AccountModel) | Tài khoản doanh thu chưa thực hiện |
| date_draft | DateField | Ngày nháp |
| date_in_review | DateField | Ngày xem xét |
| date_approved | DateField | Ngày duyệt |
| date_paid | DateField | Ngày thanh toán |
| date_void | DateField | Ngày hủy |
| date_canceled | DateField | Ngày hủy bỏ |
| amount_due | DecimalField | Số tiền phải thu |
| amount_received | DecimalField | Số tiền đã nhận |
| markdown_notes | TextField | Ghi chú định dạng Markdown |
| additional_info | JSONField | Thông tin bổ sung |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: InvoiceModel đại diện cho các hóa đơn phát hành cho khách hàng. Mỗi hóa đơn liên kết với sổ cái có chứa các bút toán.

**Điểm đặc biệt**:
- InvoiceModel theo dõi chu kỳ sống đầy đủ của hóa đơn từ nháp đến thanh toán.
- Các tài khoản khác nhau được liên kết để xử lý kế toán tự động (tiền mặt, phải thu, doanh thu chưa thực hiện).

## PurchaseOrderModel

Đại diện cho một đơn đặt hàng phát hành cho nhà cung cấp.

| Trường | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| po_title | CharField | Tiêu đề đơn đặt hàng |
| po_status | CharField | Trạng thái (nháp, đã duyệt, v.v.) |
| entity | ForeignKey(EntityModel) | Đơn vị liên quan |
| po_number | SlugField | Số đơn đặt hàng |
| date_draft | DateField | Ngày nháp |
| date_approved | DateField | Ngày duyệt |
| date_fulfilled | DateField | Ngày hoàn thành |
| date_canceled | DateField | Ngày hủy bỏ |
| date_void | DateField | Ngày hủy |
| markdown_notes | TextField | Ghi chú định dạng Markdown |
| additional_info | JSONField | Thông tin bổ sung |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: PurchaseOrderModel đại diện cho các đơn đặt hàng phát hành cho nhà cung cấp. Đơn đặt hàng là cam kết mua hàng từ nhà cung cấp nhưng không phải là nghĩa vụ chính thức như hóa đơn mua hàng.

## EstimateModel

Đại diện cho một báo giá hoặc dự toán phát hành cho khách hàng.

| Trường | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| title | CharField | Tiêu đề báo giá |
| status | CharField | Trạng thái (nháp, đã duyệt, v.v.) |
| entity | ForeignKey(EntityModel) | Đơn vị liên quan |
| customer | ForeignKey(CustomerModel) | Khách hàng liên quan |
| terms | CharField | Điều khoản báo giá |
| date_draft | DateField | Ngày nháp |
| date_approved | DateField | Ngày duyệt |
| date_expired | DateField | Ngày hết hạn |
| date_canceled | DateField | Ngày hủy bỏ |
| markdown_notes | TextField | Ghi chú định dạng Markdown |
| additional_info | JSONField | Thông tin bổ sung |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: EstimateModel đại diện cho các báo giá hoặc dự toán phát hành cho khách hàng. Báo giá là bước đầu tiên trong quy trình bán hàng và có thể được chuyển đổi thành hóa đơn.

## BankAccountModel

Đại diện cho một tài khoản ngân hàng liên kết với một tài khoản tiền mặt.

| Trường | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| name | CharField | Tên tài khoản ngân hàng |
| account_number | CharField | Số tài khoản (được che dấu) |
| routing_number | CharField | Số định tuyến (được che dấu) |
| account_type | CharField | Loại tài khoản (thanh toán, tiết kiệm, v.v.) |
| active | BooleanField | Tài khoản có đang hoạt động |
| entity_model | ForeignKey(EntityModel) | Đơn vị liên quan |
| account_model | ForeignKey(AccountModel) | Tài khoản liên quan |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: BankAccountModel đại diện cho một tài khoản ngân hàng thực tế liên kết với một tài khoản trong sơ đồ tài khoản. Nó cho phép theo dõi và đối chiếu các giao dịch ngân hàng với các bút toán kế toán.

## MuaHangModel

Đại diện cho thông tin mua hàng bao gồm giá cả và điều kiện mua từ nhà cung cấp.

| Trường | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| ma_vt | CharField | Mã vật tư/sản phẩm |
| dvt | CharField | Đơn vị tính (LY, CAI, THUNG, v.v.) |
| ngay_hl | DateField | Ngày hiệu lực của giá |
| ma_kh | CharField | Mã khách hàng/nhà cung cấp |
| ma_nt | CharField | Mã tiền tệ (VND, USD, v.v.) |
| sl_tu | FloatField | Số lượng tối thiểu để áp dụng giá này |
| gia_nt | DecimalField | Giá theo tiền tệ đã chọn |
| status | CharField | Trạng thái (1: hoạt động, 0: không hoạt động) |
| created_at | DateTimeField | Thời điểm tạo |
| updated_at | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: MuaHangModel lưu trữ thông tin về giá mua hàng và các điều kiện liên quan. Mỗi bản ghi đại diện cho một mức giá cụ thể áp dụng cho một sản phẩm từ một nhà cung cấp.

**Điểm đặc biệt**:
- Hỗ trợ đa tiền tệ thông qua trường ma_nt
- Cho phép thiết lập giá theo số lượng mua (giá bậc thang)
- Mỗi giá có thời điểm hiệu lực xác định
- Đảm bảo không có dữ liệu trùng lặp thông qua ràng buộc unique_together
- Có các index để tối ưu hiệu năng truy vấn
