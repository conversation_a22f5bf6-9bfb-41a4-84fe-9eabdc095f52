# Sơ Đồ Mố<PERSON>uan <PERSON>ệ Thực Thể - <PERSON><PERSON><PERSON> Ledger

Tài liệu này mô tả các mối quan hệ giữa các thực thể trong hệ thống Django Ledger thông qua các sơ đồ ER (Entity Relationship).

## <PERSON><PERSON><PERSON>

- [<PERSON><PERSON> Đồ Mối <PERSON>uan <PERSON>ệ <PERSON>hực Thể - D<PERSON>go <PERSON>](#sơ-đồ-mối-quan-hệ-thực-thể---django-ledger)
  - [<PERSON><PERSON><PERSON>](#mục-lục)
  - [S<PERSON> Đồ Thực Thể Cốt Lõi](#sơ-đồ-thực-thể-cốt-lõi)
  - [S<PERSON> Đồ Quan <PERSON>](#sơ-đồ-quan-hệ-kế-toán)
  - [S<PERSON> Đồ Quản Lý Chứng Từ](#sơ-đồ-quản-lý-chứng-từ)
  - [<PERSON><PERSON> Đồ Quản Lý Hàng Tồ<PERSON>](#sơ-đồ-quản-lý-hàng-tồn-kho)
  - [<PERSON><PERSON> <PERSON>uan <PERSON>](#sơ-đồ-quan-hệ-erp)

## <PERSON><PERSON> Đồ Thực Thể Cốt Lõi

Sơ đồ này hiển thị các thực thể cốt lõi trong hệ thống và mối quan hệ giữa chúng.

```mermaid
erDiagram
    EntityModel ||--o{ LedgerModel : "có"
    EntityModel ||--o{ ChartOfAccountModel : "có"
    EntityModel ||--o{ EntityUnitModel : "có"
    EntityModel ||--o{ BankAccountModel : "có"
    EntityModel ||--o{ CustomerModel : "có"
    EntityModel ||--o{ VendorModel : "có"
    EntityModel ||--o{ ItemModel : "có"
    EntityModel }|--o{ EntityManagementModel : "cấp quyền"
    
    UserModel ||--o{ EntityModel : "quản trị"
    UserModel }o--o{ EntityModel : "quản lý"
```

## Sơ Đồ Quan Hệ Kế Toán

Sơ đồ này minh họa mối quan hệ giữa các thực thể kế toán chính.

```mermaid
erDiagram
    ChartOfAccountModel ||--o{ AccountModel : "chứa"
    LedgerModel ||--o{ JournalEntryModel : "chứa"
    JournalEntryModel ||--o{ TransactionModel : "chứa"
    AccountModel ||--o{ TransactionModel : "tham chiếu"
    EntityUnitModel ||--o{ JournalEntryModel : "phân loại"
    
    EntityModel ||--o{ ChartOfAccountModel : "có"
    EntityModel ||--o{ LedgerModel : "có"
    EntityModel ||--o{ EntityUnitModel : "có"
```

## Sơ Đồ Quản Lý Chứng Từ

Sơ đồ này hiển thị các mối quan hệ liên quan đến quản lý chứng từ như hóa đơn và hóa đơn mua hàng.

```mermaid
erDiagram
    LedgerModel ||--o| BillModel : "quản lý"
    LedgerModel ||--o| InvoiceModel : "quản lý"
    
    BillModel }o--o{ ItemModel : "chứa"
    InvoiceModel }o--o{ ItemModel : "chứa"
    
    BillModel }o--|| VendorModel : "phát hành bởi" 
    InvoiceModel }o--|| CustomerModel : "phát hành đến"
    
    EntityModel ||--o{ BillModel : "có"
    EntityModel ||--o{ InvoiceModel : "có"
    EntityModel ||--o{ VendorModel : "có"
    EntityModel ||--o{ CustomerModel : "có"
    
    AccountModel ||--o{ BillModel : "tài khoản tiền mặt"
    AccountModel ||--o{ BillModel : "tài khoản trả trước"
    AccountModel ||--o{ BillModel : "tài khoản phải trả"
    
    AccountModel ||--o{ InvoiceModel : "tài khoản tiền mặt"
    AccountModel ||--o{ InvoiceModel : "tài khoản phải thu"
    AccountModel ||--o{ InvoiceModel : "tài khoản doanh thu chưa thực hiện"
```

## Sơ Đồ Quản Lý Hàng Tồn Kho

Sơ đồ này hiển thị các mối quan hệ liên quan đến quản lý hàng tồn kho và sản phẩm.

```mermaid
erDiagram
    EntityModel ||--o{ ItemModel : "có"
    EntityModel ||--o{ UnitOfMeasureModel : "có"
    EntityModel ||--o{ KhoHangModel : "có"
    EntityModel ||--o{ VAT_TU_SAN_PHAM : "có"
    EntityModel ||--o{ DON_VI_TINH : "có"
    
    ItemModel }o--|| UnitOfMeasureModel : "sử dụng"
    ItemModel }o--o{ ItemTransactionModel : "liên quan"
    
    KhoHangModel ||--o{ VAT_TU_SAN_PHAM : "lưu trữ"
    DON_VI_TINH ||--o{ VAT_TU_SAN_PHAM : "đo lường"
    
    AccountModel ||--o{ ItemModel : "tài khoản hàng tồn kho"
    AccountModel ||--o{ ItemModel : "tài khoản giá vốn"
    AccountModel ||--o{ ItemModel : "tài khoản doanh thu"
    AccountModel ||--o{ ItemModel : "tài khoản chi phí"
    
    BillModel }o--o{ ItemTransactionModel : "chứa"
    InvoiceModel }o--o{ ItemTransactionModel : "chứa"
    PurchaseOrderModel }o--o{ ItemTransactionModel : "chứa"
    
    ItemTransactionModel }o--|| EntityUnitModel : "thuộc"
    
    EntityRelatedModelAbstract ||--|| EntityModel : "thuộc về"
    KhoHangModel }|--|| EntityRelatedModelAbstract : "kế thừa"
    VAT_TU_SAN_PHAM }|--|| EntityRelatedModelAbstract : "kế thừa"
    DON_VI_TINH }|--|| EntityRelatedModelAbstract : "kế thừa"
```

## Sơ Đồ Quan Hệ ERP

Sơ đồ này hiển thị các mối quan hệ ERP trong hệ thống, bao gồm quản lý khách hàng, nhà cung cấp, vật tư, kho hàng và các thông tin liên quan.

```mermaid
erDiagram
    KHACH_HANG {
        varchar id PK
        varchar ma_khach_hang
        bool khach_hang
        bool nha_cung_cap
        integer loai_khach_hang
        varchar ten_khach_hang
        varchar ten_khac
        varchar dia_chi
        varchar ma_so_thue
        varchar nguoi_lien_he
        varchar nhan_vien_ban_hang FK
        varchar tai_khoan_ngam_dinh FK
        varchar ma_th_toan_cong_no FK
        varchar ph_th_th_toan FK
        integer gioi_han_tien_no
        varchar khu_vuc
        varchar dien_thoai
        timestamp ngay_sinh
        varchar so_tai_khoan
        varchar ten_ngan_hang
    }

    NHA_CUNG_CAP {
        varchar id PK
        varchar ma_nha_cung_cap
        bool khach_hang
        bool nha_cung_cap
        integer loai_khach_hang
        varchar ten_nha_cung_cap
        varchar ten_khac
        varchar dia_chi
        varchar ma_so_thue
        varchar nguoi_lien_he
        varchar nhan_vien_ban_hang FK
        varchar tai_khoan_ngam_dinh FK
        varchar ma_th_toan_cong_no FK
        varchar ph_th_th_toan FK
        integer gioi_han_tien_no
        varchar khu_vuc
        varchar dien_thoai
        timestamp ngay_sinh
        varchar so_tai_khoan
        varchar ten_ngan_hang
    }

    VAT_TU_SAN_PHAM {
        varchar id PK
        varchar ma_san_pham
        varchar ten_san_pham
        varchar ten_khac
        bool tao_nhap_thanh_phan
        varchar don_vi_tinh FK
        varchar dvt_nhap_lieu FK
        bool theo_doi_ton_kho
        bool theo_doi_lo
        integer cach_tinh_gia_ton_kho
        integer loai_vat_tu FK
        varchar ma_kho_mac_dinh FK
        varchar ma_thue_mac_dinh FK
        varchar tk_kho FK
        varchar tk_doanh_thu FK
        varchar tk_gia_von FK
        decimal the_tich
        decimal khoi_luong
    }

    DON_VI_TINH {
        varchar id PK
        varchar don_vi_tinh
        varchar ten_dvt
        varchar ten_khac
        varchar don_vi_tinh_2
        integer trang_thai
    }

    VAT_TU_SAN_PHAM_DON_VI_TINH {
        varchar vat_tu_san_pham_id PK,FK
        varchar don_vi_tinh_id PK,FK
        varchar ten_dvt
        decimal he_so
    }

    VAT_TU_SAN_PHAM_HANG_HOA {
        varchar id PK
        varchar vat_tu_san_pham_id FK
        varchar ma_hang_hoa
    }

    NHAN_VIEN {
        varchar id PK
        varchar ma_nhan_vien
        bool ban_hang
        bool mua_hang
        bool cong_no_tam_ung
        varchar ho_va_ten
        varchar chuc_vu
        varchar bo_phan
        varchar gioi_tinh
        timestamp ngay_sinh
        varchar dia_chi_thuong_tru
        varchar dien_thoai
        varchar email
        varchar ma_so_thue_tncn
        integer trang_thai
    }

    TAI_KHOAN {
        varchar id PK
        varchar tai_khoan
        varchar ten_tai_khoan
        varchar ten_tieng_anh
        varchar ten_khac
        varchar tai_khoan_me FK
        varchar ten_ngan
        bool tai_khoan_so_cai
        varchar ngoai_te_goc
        integer tk_theo_doi_cong_no
        integer phan_loai_tai_khoan
        integer trang_thai
    }

    RANG_BUOC_NHAP {
        varchar id PK
        varchar tai_khoan_id FK
        varchar ten_doi_tuong
        bool bat_buoc_nhap
    }

    KHE_UOC {
        varchar id PK
        varchar ma_khe_uoc
        varchar ten_khe_uoc
        varchar ten_khac
        integer loai_khe_uoc
        timestamp ngay
        timestamp ngay_vay
        timestamp ngay_dao_han
        decimal tien_vay_nt
        decimal tien_vay
        varchar hop_dong
        varchar khach_hang FK
        varchar tai_khoan FK
        integer trang_thai
    }

    KHE_UOC_THONG_TIN_LAI_SUAT {
        varchar id PK
        varchar khe_uoc_id FK
        decimal lai_suat
        timestamp hieu_luc_tu
        varchar ghi_chu
    }

    KHE_UOC_CHI_TIET_THANH_TOAN {
        varchar id PK
        varchar khe_uoc_id FK
        timestamp ngay_thanh_toan
        decimal tien_thanh_toan
        decimal tien_thanh_toan_nt
        varchar ghi_chu
    }

    NGOAI_TE {
        varchar id PK
        varchar ngoai_te
        varchar ten_ngoai_te
        varchar ten_khac
        varchar tk_phat_sinh_cl_no FK
        varchar tk_phat_sinh_cl_co FK
        integer stt
        integer doc_le
        varchar cach_doc
        integer trang_thai
    }

    KHO_HANG {
        varchar id PK
        varchar don_vi
        varchar ma_kho
        varchar ten_kho
        varchar ten_khac
        bool vi_tri
        bool dai_ly
        varchar dia_chi
        varchar dien_thoai
        integer trang_thai
    }

    HAN_THANH_TOAN {
        varchar id PK
        varchar ma_thanh_toan
        varchar ten_thanh_toan
        varchar ten_khac
        integer trang_thai
    }

    PHUONG_THUC_THANH_TOAN {
        varchar id PK
        varchar ma_phuong_thuc
        varchar ten_phuong_thuc
        varchar ma_ph_thuc_hddt
        integer trang_thai
    }

    NHOM_PHI {
        varchar id PK
        varchar ma_nhom
        varchar ten_phan_nhom
        varchar ten_2
        integer trang_thai
        integer loai_nhom
    }

    PHI {
        varchar id PK
        varchar ma_phi
        varchar ten_phi
        varchar ten_khac
        varchar nhom_phi_1 FK
        varchar nhom_phi_2 FK
        varchar nhom_phi_3 FK
        varchar bo_phan
        integer trang_thai
    }

    GIA_MUA {
        varchar id PK
        varchar ma_vat_tu FK
        varchar don_vi_tinh FK
        timestamp ngay_hieu_luc
        varchar nha_cung_cap FK
        varchar ngoai_te FK
        integer so_luong_tu
        decimal gia_mua
        integer trang_thai
    }

    THUE_SUAT_THUE_GTGT {
        varchar id PK
        varchar ma_thue
        varchar ten_thue
        varchar ten_khac
        decimal thue_suat
        decimal thue_suat_hddt
        varchar nhom_thue FK
        varchar tk_thue_dau_ra FK
        varchar tk_thue_dau_vao FK
        integer trang_thai
    }

    LOAI_VAT_TU {
        varchar id PK
        varchar ma_loai_vat_tu
        varchar ten_loai_vat_tu
        varchar ten_khac
        integer trang_thai
        varchar tk_kho FK
        varchar tk_doanh_thu FK
        varchar tk_gia_von FK
    }

    KHACH_HANG ||--o{ KHE_UOC : "places"
    NHAN_VIEN ||--o{ KHACH_HANG : "is sales rep for"
    NHAN_VIEN ||--o{ NHA_CUNG_CAP : "is sales rep for"
    TAI_KHOAN ||--o{ KHACH_HANG : "is default account for"
    TAI_KHOAN ||--o{ NHA_CUNG_CAP : "is default account for"
    HAN_THANH_TOAN ||--o{ KHACH_HANG : "is payment term for"
    HAN_THANH_TOAN ||--o{ NHA_CUNG_CAP : "is payment term for"
    PHUONG_THUC_THANH_TOAN ||--o{ KHACH_HANG : "is payment method for"
    PHUONG_THUC_THANH_TOAN ||--o{ NHA_CUNG_CAP : "is payment method for"
    
    DON_VI_TINH ||--o{ VAT_TU_SAN_PHAM : "is unit for"
    LOAI_VAT_TU ||--o{ VAT_TU_SAN_PHAM : "classifies"
    KHO_HANG ||--o{ VAT_TU_SAN_PHAM : "is default warehouse for"
    THUE_SUAT_THUE_GTGT ||--o{ VAT_TU_SAN_PHAM : "is default tax for"
    TAI_KHOAN ||--o{ VAT_TU_SAN_PHAM : "is inventory account for"
    
    VAT_TU_SAN_PHAM ||--o{ VAT_TU_SAN_PHAM_DON_VI_TINH : "has"
    DON_VI_TINH ||--o{ VAT_TU_SAN_PHAM_DON_VI_TINH : "used in"
    
    VAT_TU_SAN_PHAM ||--o{ VAT_TU_SAN_PHAM_HANG_HOA : "has"
    
    TAI_KHOAN ||--o{ TAI_KHOAN : "is parent account of"
    TAI_KHOAN ||--o{ RANG_BUOC_NHAP : "has"
    
    TAI_KHOAN ||--o{ KHE_UOC : "is account for"
    KHE_UOC ||--o{ KHE_UOC_THONG_TIN_LAI_SUAT : "has"
    KHE_UOC ||--o{ KHE_UOC_CHI_TIET_THANH_TOAN : "has"
    
    TAI_KHOAN ||--o{ NGOAI_TE : "is cl account for"
    
    NHOM_PHI ||--o{ PHI : "categorizes"
    
    VAT_TU_SAN_PHAM ||--o{ GIA_MUA : "has"
    DON_VI_TINH ||--o{ GIA_MUA : "used in"
    NHA_CUNG_CAP ||--o{ GIA_MUA : "provides"
    NGOAI_TE ||--o{ GIA_MUA : "is currency for"
    
    TAI_KHOAN ||--o{ THUE_SUAT_THUE_GTGT : "is tax account for"
    
    TAI_KHOAN ||--o{ LOAI_VAT_TU : "is account for"
```
