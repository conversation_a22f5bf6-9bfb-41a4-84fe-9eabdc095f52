"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for ChiTietChiTieuNganSach model.
"""
from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models.ngan_sach.chi_tieu_ngan_sach import ChiTietChiTieuNganSachModel

from django_ledger.api.serializers.unit import EntityUnitModelSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import KheUocModelSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer

class ChiTietChiTieuNganSachModelSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietChiTieuNganSachModel.
    """
    # Read-only fields for related objects
    related_items_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietChiTieuNganSachModel
        fields = [
            'uuid',
            'entity_model',
            'chi_tieu_ngan_sach',
            'ma_ctns',
            'line',
            'id_dt_loc',
            'ds_loc',
            'related_items_data',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'entity_model', 'ma_ctns', 'created', 'updated']

    def get_related_items_data(self, obj):
        """
        Get related items data based on id_dt_loc using switch-case pattern.
        Returns full object data instead of simplified format.
        """
        # Serializer không nên truy cập trực tiếp database
        # Dữ liệu này nên được chuẩn bị trước trong context hoặc từ service
        if not hasattr(obj, 'related_items'):
            return []

        # Import serializers for each type


        result = []
        for item in obj.related_items:
            if item is None:
                result.append("notfound")
                continue

            try:
                # Switch-case pattern based on id_dt_loc
                if obj.id_dt_loc == '1':  # EntityUnit (Đơn vị cơ sở)
                    serializer = EntityUnitModelSerializer(item)
                    result.append(serializer.data)

                elif obj.id_dt_loc == '2':  # Customer (Khách hàng)
                    serializer = CustomerModelSerializer(item)
                    result.append(serializer.data)

                elif obj.id_dt_loc == '3':  # Account (Tài khoản)
                    serializer = AccountModelSerializer(item)
                    result.append(serializer.data)

                elif obj.id_dt_loc == '4':  # Contract (Hợp đồng)
                    serializer = ContractModelSerializer(item)
                    result.append(serializer.data)

                elif obj.id_dt_loc == '5':  # KheUoc (Khế ước)
                    serializer = KheUocModelSerializer(item)
                    result.append(serializer.data)

                elif obj.id_dt_loc == '6':  # VuViec (Vụ việc)
                    serializer = VuViecModelSerializer(item)
                    result.append(serializer.data)

                elif obj.id_dt_loc == '7':  # BoPhan (Bộ phận)
                    serializer = BoPhanModelSerializer(item)
                    result.append(serializer.data)

                else:
                    # Unknown id_dt_loc type
                    result.append("unknown_type")

            except Exception:
                # Handle any serialization errors
                result.append("serialization_error")

        return result

    def to_representation(self, instance):
        """
        Override the default representation to handle UUID serialization.
        """
        representation = super().to_representation(instance)

        # Convert JSON fields containing UUID lists to lists of strings
        if 'ds_loc' in representation and representation['ds_loc'] is not None:
            if not isinstance(representation['ds_loc'], list):
                representation['ds_loc'] = [str(representation['ds_loc'])] if representation['ds_loc'] else []
            else:
                representation['ds_loc'] = [str(item) if item is not None else '' for item in representation['ds_loc']]

        # Convert other UUID fields to strings
        uuid_fields = ['ma_ctns', 'unit_id', 'ma_bp']
        for field in uuid_fields:
            if field in representation and representation[field] is not None:
                representation[field] = str(representation[field])

        return representation

    def validate_line(self, value):
        """
        Validate line field.
        """
        if not isinstance(value, int):
            raise serializers.ValidationError(_("Line must be an integer."))
        return value

    def validate_id_dt_loc(self, value):
        """
        Validate id_dt_loc field.
        """
        valid_id_dt_loc = [choice[0] for choice in ChiTietChiTieuNganSachModel.ID_DT_LOC_CHOICES]
        if value not in valid_id_dt_loc:
            raise serializers.ValidationError(_(f"Invalid id_dt_loc value. Must be one of {valid_id_dt_loc}."))
        return value

    def validate_ds_loc(self, value):
        """
        Validate ds_loc field.
        """
        if value is None:
            return []

        # If the field is a string, convert it to a list
        if isinstance(value, str):
            value = [value]
        # Ensure the field is a list
        elif not isinstance(value, list):
            value = []

        return value

    def validate(self, data):
        """
        Validate the data.
        """
        # Validate basic format and structure
        if 'id_dt_loc' in data:
            id_dt_loc = data['id_dt_loc']

            # Validate id_dt_loc is a valid choice
            valid_id_dt_loc = [choice[0] for choice in ChiTietChiTieuNganSachModel.ID_DT_LOC_CHOICES]
            if id_dt_loc not in valid_id_dt_loc:
                raise serializers.ValidationError({"id_dt_loc": _(f"Invalid id_dt_loc value. Must be one of {valid_id_dt_loc}.")})

            # Ensure ds_loc is properly formatted
            if 'ds_loc' in data and data['ds_loc'] is not None:
                ds_loc = data['ds_loc']

                # If the field is a string, convert it to a list
                if isinstance(ds_loc, str):
                    data['ds_loc'] = [ds_loc]
                # Ensure the field is a list
                elif not isinstance(ds_loc, list):
                    data['ds_loc'] = []

                # Validate UUIDs format
                for uuid in data['ds_loc']:
                    try:
                        # Just check if it's a valid UUID format
                        str(uuid)
                    except (ValueError, TypeError):
                        raise serializers.ValidationError({"ds_loc": _("One or more UUIDs have invalid format.")})

        return data
