"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for SoDuTucThoiTheoTaiKhoan model.
"""

from rest_framework import serializers

from django_ledger.models.ban_hang.so_du_tuc_thoi_theo_tai_khoan import SoDuTucThoiTheoTaiKhoanModel
from django_ledger.services.ban_hang.so_du_tuc_thoi_theo_tai_khoan import SoDuTucThoiTheoTaiKhoanService


class SoDuTucThoiTheoTaiKhoanModelSerializer(serializers.ModelSerializer):
    """
    Serializer for SoDuTucThoiTheoTaiKhoanModel.
    """
    # Add nested serializers for related objects
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    tk_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = SoDuTucThoiTheoTaiKhoanModel
        fields = [
            'uuid',
            'entity_model',
            'nam',
            'ma_kh',
            'ma_kh_data',
            'tk',
            'tk_data',
            'created_by',
            'updated_by',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated', 'entity_model']
        swagger_schema_fields = {
            'title': 'SoDuTucThoiTheoTaiKhoan',
            'description': 'Số dư tức thời theo tài khoản model serializer'
        }

    def get_ma_kh_data(self, instance):
        """
        Get customer data for the real-time account balance
        """
        if instance.ma_kh:
            return {
                'uuid': instance.ma_kh.uuid,
                'customer_name': instance.ma_kh.customer_name,
                'customer_number': instance.ma_kh.customer_number,
                'customer_code': getattr(instance.ma_kh, 'customer_code', None)
            }
        return None

    def get_tk_data(self, instance):
        """
        Get account data for the real-time account balance
        """
        if instance.tk:
            return {
                'uuid': instance.tk.uuid,
                'code': instance.tk.code,
                'name': instance.tk.name,
                'balance_type': instance.tk.balance_type
            }
        return None

    def create(self, validated_data):
        """
        Create a new real-time account balance
        """
        entity_slug = self.context.get('entity_slug')
        user_model = self.context.get('request').user
        service = SoDuTucThoiTheoTaiKhoanService(
            entity_slug=entity_slug,
            user_model=user_model
        )
        return service.create_so_du_tuc_thoi(validated_data)

    def update(self, instance, validated_data):
        """
        Update an existing real-time account balance
        """
        entity_slug = self.context.get('entity_slug')
        user_model = self.context.get('request').user
        service = SoDuTucThoiTheoTaiKhoanService(
            entity_slug=entity_slug,
            user_model=user_model
        )
        return service.update_so_du_tuc_thoi(
            uuid=instance.uuid,
            data=validated_data
        )
