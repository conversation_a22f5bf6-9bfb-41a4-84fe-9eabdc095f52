"""
Serializer for ChungTuPhaiTraKhacChungTuBuTruCongNoModel.
"""

from django.utils.translation import gettext_lazy as _
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.mua_hang.dieu_chinh_can_tru_cong_no.chung_tu_phai_tra_khac_chung_tu_bu_tru_cong_no.chi_tiet_chung_tu_phai_tra_khac_chung_tu_bu_tru_cong_no import (
    ChiTietChungTuPhaiTraKhacChungTuBuTruCongNoSerializer,
)
from django_ledger.api.serializers.mua_hang.dieu_chinh_can_tru_cong_no.chung_tu_phai_tra_khac_chung_tu_bu_tru_cong_no.thue_chung_tu_phai_tra_khac_chung_tu_bu_tru_cong_no import (
    ThueChungTuPhaiTraKhacChungTuBuTruCongNoSerializer,
)
from django_ledger.models.mua_hang.dieu_chinh_can_tru_cong_no.chung_tu_phai_tra_khac_chung_tu_bu_tru_cong_no import (
    ChungTuPhaiTraKhacChungTuBuTruCongNoModel,
    ChiTietChungTuPhaiTraKhacChungTuBuTruCongNoModel,
    ThueChungTuPhaiTraKhacChungTuBuTruCongNoModel,
)
from rest_framework import serializers


class ChungTuPhaiTraKhacChungTuBuTruCongNoSerializer(serializers.ModelSerializer):
    """
    Serializer for ChungTuPhaiTraKhacChungTuBuTruCongNoModel.
    """

    # Read-only fields for related objects
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    ma_nk_data = serializers.SerializerMethodField(read_only=True)
    so_ct_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)

    # Child data
    chi_tiet_data = serializers.SerializerMethodField(read_only=True)
    thue_data = serializers.SerializerMethodField(read_only=True)
    chi_tiet_items = serializers.ListField(required=False, write_only=True)
    thue_items = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = ChungTuPhaiTraKhacChungTuBuTruCongNoModel
        fields = [
            "uuid",
            "entity_model",
            "ma_ngv",
            "unit_id",
            "unit_id_data",
            "i_so_ct",
            "ma_nk",
            "ma_nk_data",
            "so_ct",
            "so_ct_data",
            "ngay_ct",
            "ngay_lct",
            "dien_giai",
            "ma_nt",
            "ma_nt_data",
            "ty_gia",
            "status",
            "transfer_yn",
            "t_ps_no_nt",
            "t_ps_no",
            "t_ps_co_nt",
            "t_ps_co",
            "chi_tiet_data",
            "thue_data",
            "chi_tiet_items",
            "thue_items",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "entity_model",
            "unit_id_data",
            "ma_nk_data",
            "so_ct_data",
            "ma_nt_data",
            "chi_tiet_data",
            "thue_data",
            "created",
            "updated",
        ]

    def get_unit_id_data(self, obj):
        """
        Get unit data.
        """
        if obj.unit_id:
            return {
                "uuid": str(obj.unit_id.uuid),
                "name": obj.unit_id.name if hasattr(obj.unit_id, "name") else None,
            }
        return None

    def get_ma_nk_data(self, obj):
        """
        Get document book data.
        """
        if obj.ma_nk:
            return {
                "uuid": str(obj.ma_nk.uuid),
                "ma_nk": obj.ma_nk.ma_nk if hasattr(obj.ma_nk, "ma_nk") else None,
                "ten_nk": obj.ma_nk.ten_nk if hasattr(obj.ma_nk, "ten_nk") else None,
            }
        return None

    def get_so_ct_data(self, obj):
        """
        Get document data.
        """
        if obj.so_ct:
            return {
                "uuid": str(obj.so_ct.uuid),
                "so_ct": obj.so_ct.so_ct if hasattr(obj.so_ct, "so_ct") else None,
                "dien_giai": (
                    obj.so_ct.dien_giai if hasattr(obj.so_ct, "dien_giai") else None
                ),
            }
        return None

    def get_ma_nt_data(self, obj):
        """
        Get currency data.
        """
        if obj.ma_nt:
            return {
                "uuid": str(obj.ma_nt.uuid),
                "ma_nt": obj.ma_nt.ma_nt if hasattr(obj.ma_nt, "ma_nt") else None,
                "ten_nt": obj.ma_nt.ten_nt if hasattr(obj.ma_nt, "ten_nt") else None,
            }
        return None

    def get_chi_tiet_data(self, obj):
        """
        Get detail data.
        """
        chi_tiet = obj.chi_tiet.all().order_by("line")
        return ChiTietChungTuPhaiTraKhacChungTuBuTruCongNoSerializer(
            chi_tiet, many=True
        ).data

    def get_thue_data(self, obj):
        """
        Get tax data.
        """
        thue = obj.thue.all().order_by("line")
        return ThueChungTuPhaiTraKhacChungTuBuTruCongNoSerializer(thue, many=True).data

    def validate(self, attrs):
        """
        Validate the serializer data.
        """
        # Check if this is an update or create operation
        is_update = self.instance is not None

        # Validate required fields for create operations
        if not is_update:
            required_fields = ["ma_ngv", "so_ct", "ngay_ct", "ngay_lct", "dien_giai"]
            for field in required_fields:
                if field not in attrs:
                    raise serializers.ValidationError(
                        {field: _("This field is required.")}
                    )

        # Set default values if not provided
        if "status" not in attrs:
            attrs["status"] = "1"

        if "transfer_yn" not in attrs:
            attrs["transfer_yn"] = False

        if "ty_gia" not in attrs:
            attrs["ty_gia"] = 1

        if "t_ps_no" not in attrs:
            attrs["t_ps_no"] = 0

        if "t_ps_co" not in attrs:
            attrs["t_ps_co"] = 0

        if "t_ps_no_nt" not in attrs:
            attrs["t_ps_no_nt"] = 0

        if "t_ps_co_nt" not in attrs:
            attrs["t_ps_co_nt"] = 0

        # Validate that total debits equal total credits
        if attrs["t_ps_no"] != attrs["t_ps_co"]:
            raise serializers.ValidationError(
                {
                    "non_field_errors": _(
                        "Total debit amount must equal total credit amount"
                    )
                }
            )

        if attrs["t_ps_no_nt"] != attrs["t_ps_co_nt"]:
            raise serializers.ValidationError(
                {
                    "non_field_errors": _(
                        "Total debit amount in foreign currency must equal total credit amount in foreign currency"
                    )
                }
            )

        return attrs
