"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Tai san va cong cu (Assets and Tools) routers package initialization.
"""

from django_ledger.api.routers.danh_muc.tai_san_va_cong_cu.loai_tai_san_cong_cu import urlpatterns as loai_tai_san_cong_cu_urlpatterns
from django_ledger.api.routers.danh_muc.tai_san_va_cong_cu.bo_phan_su_dung_ccdc import bo_phan_su_dung_ccdc_urlpatterns

__all__ = [
    'loai_tai_san_cong_cu_urlpatterns',
    'bo_phan_su_dung_ccdc_urlpatterns',
]
