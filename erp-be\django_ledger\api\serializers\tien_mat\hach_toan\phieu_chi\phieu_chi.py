"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for PhieuChi model.
"""

from django.utils.translation import gettext_lazy as _
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.chung_tu import ChungTuSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.han_thanh_toan import HanThanhToanModelSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer 
from django_ledger.api.serializers.quyen_chung_tu import QuyenChungTuDetailSerializer
from django_ledger.api.serializers.tien_mat.hach_toan.phieu_chi.phieu_chi_chi_tiet import (
    PhieuChiChiTietSerializer,
)
from django_ledger.api.serializers.unit import EntityUnitModelSerializer
from django_ledger.models import PhieuChiModel
from rest_framework import serializers


class PhieuChiSerializer(serializers.ModelSerializer):
    """
    Serializer for PhieuChi model.
    """

    # Read-only fields for related objects
    tk_data = serializers.SerializerMethodField(read_only=True)
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    ma_nk_data = serializers.SerializerMethodField(read_only=True)
    so_ct_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)
    so_ct0_data = serializers.SerializerMethodField(read_only=True)
    ma_tt_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)

    # Child details
    children_data = serializers.SerializerMethodField(read_only=True)
    children = serializers.ListField(write_only=True, required=False)

    class Meta:
        model = PhieuChiModel
        fields = [
            "uuid",
            "entity_model",
            "id",
            "ma_ngv",
            "dia_chi",
            "ong_ba",
            "dien_giai",
            "tk",
            "tk_data",
            "unit_id",
            "unit_id_data",
            "id_progress",
            "xprogress",
            "i_so_ct",
            "ma_nk",
            "ma_nk_data",
            "so_ct",
            "so_ct_data",
            "so_ct0",
            "so_ct0_data",
            "so_ct_goc",
            "dien_giai_ct_goc",
            "ngay_ct",
            "ngay_lct",
            "ngay_ct0",
            "xdatetime2",
            "ma_nt",
            "ma_nt_data",
            "ty_gia",
            "t_tien_nt",
            "t_tien",
            "t_thue_nt",
            "t_thue",
            "status",
            "transfer_yn",
            "hd_yn",
            "tg_dd",
            "cltg_yn",
            "ma_kh",
            "ma_kh_data",
            "ma_tt",
            "ma_tt_data",
            "xfile",
            "created_by",
            "children_data",
            "children",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "entity_model",
            "tk_data",
            "unit_id_data",
            "ma_nk_data",
            "so_ct_data",
            "ma_nt_data",
            "so_ct0_data",
            "ma_tt_data",
            "ma_kh_data",
            "children_data",
            "created",
            "updated",
        ]

    def get_tk_data(self, obj):
        """
        Get account data.
        """
        if obj.tk:
            return AccountModelSerializer(obj.tk).data
        return None

    def get_unit_id_data(self, obj):
        """
        Get unit data.
        """
        if obj.unit_id:
            return EntityUnitModelSerializer(obj.unit_id).data
        return None

    def get_ma_nk_data(self, obj):
        """
        Get document type data.
        """
        if obj.ma_nk:
            return QuyenChungTuDetailSerializer(obj.ma_nk).data
        return None

    def get_so_ct_data(self, obj):
        """
        Get document number data.
        """
        if obj.so_ct:
            return ChungTuSerializer(obj.so_ct).data
        return None

    def get_ma_nt_data(self, obj):
        """
        Get currency data.
        """
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_so_ct0_data(self, obj):
        """
        Get original document number data.
        """
        if obj.so_ct0:
            return ChungTuSerializer(obj.so_ct0).data
        return None

    def get_ma_tt_data(self, obj):
        """
        Get payment term data.
        """
        if obj.ma_tt:
            return HanThanhToanModelSerializer(obj.ma_tt).data
        return None

    def get_ma_kh_data(self, obj):
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_children_data(self, obj):
        """
        Get child details data.
        """
        children = obj.children.all()
        return PhieuChiChiTietSerializer(children, many=True).data

    def get_child_items(self, obj):
        """
        Get child items count.
        """
        return obj.children.count()


class PhieuChiListSerializer(serializers.ModelSerializer):
    """
    Serializer for PhieuChi list view.
    """

    # Read-only fields for related objects
    tk_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = PhieuChiModel
        fields = [
            "uuid",
            "i_so_ct",
            "ngay_ct",
            "ong_ba",
            "dien_giai",
            "tk",
            "tk_data",
            "ma_kh",
            "ma_kh_data",
            "ma_nt",
            "ma_nt_data",
            "t_tien_nt",
            "t_tien",
            "t_thue_nt",
            "t_thue",
            "status",
            "transfer_yn",
            "hd_yn",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "tk_data",
            "ma_kh_data",
            "ma_nt_data",
            "created",
            "updated",
        ]

    def get_tk_data(self, obj):
        """
        Get account data.
        """
        if obj.tk:
            return {
                "uuid": obj.tk.uuid,
                "code": obj.tk.code,
                "name": obj.tk.name,
            }
        return None

    def get_ma_kh_data(self, obj):
        """
        Get customer data.
        """
        if obj.ma_kh:
            return {
                "uuid": obj.ma_kh.uuid,
                "ma_kh": obj.ma_kh.ma_kh,
                "ten_kh": obj.ma_kh.ten_kh,
            }
        return None

    def get_ma_nt_data(self, obj):
        """
        Get currency data.
        """
        if obj.ma_nt:
            return {
                "uuid": obj.ma_nt.uuid,
                "ma_nt": obj.ma_nt.ma_nt,
                "ten_nt": obj.ma_nt.ten_nt,
            }
        return None
