"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

KhaiBaoThongTinCCDC (Tool Information Declaration) serializer implementation.
"""

from rest_framework import serializers

from django_ledger.models.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc.khai_bao_thong_tin_ccdc import KhaiBaoThongTinCCDCModel

# Import serializers for foreign key fields
from django_ledger.api.serializers.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc.chi_tiet_doi_tuong_hach_toan_ccdc import ChiTietDoiTuongHachToanCCDCModelSerializer
from django_ledger.api.serializers.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc.chi_tiet_phu_tung_kem_theo_ccdc import ChiTietPhuTungKemTheoCCDCModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.unit import EntityUnitModelSimpleSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.danh_muc.tai_san_va_cong_cu.loai_tai_san_cong_cu import LoaiTaiSanCongCuModelSerializer
from django_ledger.api.serializers.chung_tu import ChungTuSerializer
from django_ledger.api.serializers.group import GroupModelSerializer


class KhaiBaoThongTinCCDCModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the KhaiBaoThongTinCCDCModel.

    This serializer handles the conversion between KhaiBaoThongTinCCDCModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields (entity_model, ma_nk, so_ct, ma_kh, ma_nt)
    - Adds additional fields with "_data" suffix (entity_model_data, ma_nk_data, so_ct_data, ma_kh_data, ma_nt_data)
      that contain the complete nested object data with all available fields
    - When deserializing, accepts UUIDs for reference fields
    """
    # Add nested serializers for foreign key fields using SerializerMethodField
    ma_lcc_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)
    tk_cc_data = serializers.SerializerMethodField(read_only=True)
    tk_kh_data = serializers.SerializerMethodField(read_only=True)
    tk_cp_data = serializers.SerializerMethodField(read_only=True)
    so_ct_data = serializers.SerializerMethodField(read_only=True)
    nh_cc1_data = serializers.SerializerMethodField(read_only=True)
    nh_cc2_data = serializers.SerializerMethodField(read_only=True)
    nh_cc3_data = serializers.SerializerMethodField(read_only=True)

    # Add chi_tiet fields for nested serialization
    chi_tiet_doi_tuong_hach_toan_ccdc = serializers.SerializerMethodField(read_only=True)
    chi_tiet_phu_tung_kem_theo_ccdc = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = KhaiBaoThongTinCCDCModel
        fields = [
            'uuid',
            'entity_model',
            'ma_cc',
            'ten_cc',
            'ten_cc2',
            'ma_lcc',
            'ma_lcc_data',
            'ma_bp',
            'ma_bp_data',
            'ngay_mua',
            'ngay_kh0',
            'ngay_kh_kt',
            'ngay_ct',
            'so_ky_kh',
            'kieu_kh',
            'so_luong',
            'dvt',
            'dvt_data',
            'unit_id',
            'unit_id_data',
            'ma_nt',
            'ma_nt_data',
            'ty_gia',
            'tk_cc',
            'tk_cc_data',
            'tk_kh',
            'tk_kh_data',
            'tk_cp',
            'tk_cp_data',
            'nguyen_gia_nt',
            'gt_da_kh_nt',
            'gt_cl_nt',
            'gt_kh_ky_nt',
            'nguyen_gia',
            'gt_da_kh',
            'gt_cl',
            'gt_kh_ky',
            'status',
            'so_hieu_cc',
            'so_ct',
            'so_ct_data',
            'nh_cc1',
            'nh_cc1_data',
            'nh_cc2',
            'nh_cc2_data',
            'nh_cc3',
            'nh_cc3_data',
            'nuoc_sx',
            'nam_sx',
            'ghi_chu',
            'chi_tiet_doi_tuong_hach_toan_ccdc',
            'chi_tiet_phu_tung_kem_theo_ccdc',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'ma_lcc_data',
            'ma_bp_data',
            'dvt_data',
            'unit_id_data',
            'ma_nt_data',
            'tk_cc_data',
            'tk_kh_data',
            'tk_cp_data',
            'so_ct_data',
            'nh_cc1_data',
            'nh_cc2_data',
            'nh_cc3_data',
            'chi_tiet_doi_tuong_hach_toan_ccdc',
            'chi_tiet_phu_tung_kem_theo_ccdc',
            'created',
            'updated'
        ]
    def get_chi_tiet_doi_tuong_hach_toan_ccdc(self, obj):
        """
        Gets the chi_tiet_doi_tuong_hach_toan_ccdc for the KhaiBaoThongTinCCDCModel.

        Parameters
        ----------
        obj : KhaiBaoThongTinCCDCModel
            The KhaiBaoThongTinCCDCModel instance.

        Returns
        -------
        list
            List of serialized ChiTietDoiTuongHachToanCCDCModel instances.
        """
        chi_tiet = obj.chi_tiet_doi_tuong_hach_toan_ccdc.all()
        serializer = ChiTietDoiTuongHachToanCCDCModelSerializer(chi_tiet, many=True)
        return serializer.data

    def get_chi_tiet_phu_tung_kem_theo_ccdc(self, obj):
        """
        Gets the chi_tiet_phu_tung_kem_theo_ccdc for the KhaiBaoThongTinCCDCModel.

        Parameters
        ----------
        obj : KhaiBaoThongTinCCDCModel
            The KhaiBaoThongTinCCDCModel instance.

        Returns
        -------
        list
            List of serialized ChiTietPhuTungKemTheoCCDCModel instances.
        """
        chi_tiet = obj.chi_tiet_phu_tung_kem_theo_ccdc.all()
        serializer = ChiTietPhuTungKemTheoCCDCModelSerializer(chi_tiet, many=True)
        return serializer.data

    def get_ma_lcc_data(self, obj):
        """
        Gets the tool category data.

        Parameters
        ----------
        obj : KhaiBaoThongTinCCDCModel
            The KhaiBaoThongTinCCDCModel instance.

        Returns
        -------
        dict or None
            Dictionary with tool category information or None if ma_lcc is None.
        """
        if obj.ma_lcc:
            return LoaiTaiSanCongCuModelSerializer(obj.ma_lcc).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Gets the department data.

        Parameters
        ----------
        obj : KhaiBaoThongTinCCDCModel
            The KhaiBaoThongTinCCDCModel instance.

        Returns
        -------
        dict or None
            Dictionary with department information or None if ma_bp is None.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_dvt_data(self, obj):
        """
        Gets the unit of measure data.

        Parameters
        ----------
        obj : KhaiBaoThongTinCCDCModel
            The KhaiBaoThongTinCCDCModel instance.

        Returns
        -------
        dict or None
            Dictionary with unit of measure information or None if dvt is None.
        """
        if obj.dvt:
            return DonViTinhSerializer(obj.dvt).data
        return None

    def get_unit_id_data(self, obj):
        """
        Gets the entity unit data.

        Parameters
        ----------
        obj : KhaiBaoThongTinCCDCModel
            The KhaiBaoThongTinCCDCModel instance.

        Returns
        -------
        dict or None
            Dictionary with entity unit information or None if unit_id is None.
        """
        if obj.unit_id:
            return EntityUnitModelSimpleSerializer(obj.unit_id).data
        return None

    def get_ma_nt_data(self, obj):
        """
        Gets the currency data.

        Parameters
        ----------
        obj : KhaiBaoThongTinCCDCModel
            The KhaiBaoThongTinCCDCModel instance.

        Returns
        -------
        dict or None
            Dictionary with currency information or None if ma_nt is None.
        """
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_tk_cc_data(self, obj):
        """
        Gets the tool account data.

        Parameters
        ----------
        obj : KhaiBaoThongTinCCDCModel
            The KhaiBaoThongTinCCDCModel instance.

        Returns
        -------
        dict or None
            Dictionary with tool account information or None if tk_cc is None.
        """
        if obj.tk_cc:
            return AccountModelSerializer(obj.tk_cc).data
        return None

    def get_tk_kh_data(self, obj):
        """
        Gets the depreciation account data.

        Parameters
        ----------
        obj : KhaiBaoThongTinCCDCModel
            The KhaiBaoThongTinCCDCModel instance.

        Returns
        -------
        dict or None
            Dictionary with depreciation account information or None if tk_kh is None.
        """
        if obj.tk_kh:
            return AccountModelSerializer(obj.tk_kh).data
        return None

    def get_tk_cp_data(self, obj):
        """
        Gets the expense account data.

        Parameters
        ----------
        obj : KhaiBaoThongTinCCDCModel
            The KhaiBaoThongTinCCDCModel instance.

        Returns
        -------
        dict or None
            Dictionary with expense account information or None if tk_cp is None.
        """
        if obj.tk_cp:
            return AccountModelSerializer(obj.tk_cp).data
        return None

    def get_so_ct_data(self, obj):
        """
        Gets the document data.

        Parameters
        ----------
        obj : KhaiBaoThongTinCCDCModel
            The KhaiBaoThongTinCCDCModel instance.

        Returns
        -------
        dict or None
            Dictionary with document information or None if so_ct is None.
        """
        if obj.so_ct:
            return ChungTuSerializer(obj.so_ct).data
        return None

    def get_nh_cc1_data(self, obj):
        """
        Gets the tool group 1 data.

        Parameters
        ----------
        obj : KhaiBaoThongTinCCDCModel
            The KhaiBaoThongTinCCDCModel instance.

        Returns
        -------
        dict or None
            Dictionary with tool group 1 information or None if nh_cc1 is None.
        """
        if obj.nh_cc1:
            return GroupModelSerializer(obj.nh_cc1).data
        return None

    def get_nh_cc2_data(self, obj):
        """
        Gets the tool group 2 data.

        Parameters
        ----------
        obj : KhaiBaoThongTinCCDCModel
            The KhaiBaoThongTinCCDCModel instance.

        Returns
        -------
        dict or None
            Dictionary with tool group 2 information or None if nh_cc2 is None.
        """
        if obj.nh_cc2:
            return GroupModelSerializer(obj.nh_cc2).data
        return None

    def get_nh_cc3_data(self, obj):
        """
        Gets the tool group 3 data.

        Parameters
        ----------
        obj : KhaiBaoThongTinCCDCModel
            The KhaiBaoThongTinCCDCModel instance.

        Returns
        -------
        dict or None
            Dictionary with tool group 3 information or None if nh_cc3 is None.
        """
        if obj.nh_cc3:
            return GroupModelSerializer(obj.nh_cc3).data
        return None

