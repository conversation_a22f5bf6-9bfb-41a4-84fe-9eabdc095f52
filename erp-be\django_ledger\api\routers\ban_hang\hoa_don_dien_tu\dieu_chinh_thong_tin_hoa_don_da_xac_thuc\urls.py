from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.ban_hang.hoa_don_dien_tu.dieu_chinh_thong_tin_hoa_don_da_xac_thuc import (
    HoaDonDieuChinhThongTinViewSet,
)

# Main router for HoaDonDieuChinhThongTin
router = DefaultRouter()
router.register(
    "hoa-don-dieu-chinh-thong-tin",
    HoaDonDieuChinhThongTinViewSet,
    basename="hoa-don-dieu-chinh-thong-tin",
)

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path("", include(router.urls)),
]
