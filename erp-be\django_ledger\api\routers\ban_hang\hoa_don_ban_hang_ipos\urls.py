"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Hoa Don Ban Hang Ipos (Point of Sale Invoice) module.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.ban_hang.hoa_don_ban_hang_ipos import (
    HoaDonBanHangIPosViewSet,
    ChiTietHoaDonBanHangIPosViewSet,
    ThongTinThanhToanHoaDonBanHangIPosViewSet,
    ChietKhauHoaDonBanHangIPosViewSet,
    ChiTietChietKhauHoaDonBanHangIPosViewSet,
)

# Main router for HoaDonBanHangIPos
router = DefaultRouter()
router.register("", HoaDonBanHangIPosViewSet, basename="hoa-don-ban-hang-ipos")

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path("", include(router.urls)),
    # Nested routes for hoa-don-ban-hang-ipos
    path(
        "<uuid:hoa_don_uuid>/",
        include(
            [
                # Chi tiet hoa don ban hang ipos routes
                path(
                    "chi-tiet/",
                    ChiTietHoaDonBanHangIPosViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="chi-tiet-hoa-don-ban-hang-ipos-list",
                ),
                path(
                    "chi-tiet/<uuid:uuid>/",
                    ChiTietHoaDonBanHangIPosViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="chi-tiet-hoa-don-ban-hang-ipos-detail",
                ),
                # Thong tin thanh toan hoa don ban hang ipos routes
                path(
                    "thanh-toan/",
                    ThongTinThanhToanHoaDonBanHangIPosViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="thong-tin-thanh-toan-hoa-don-ban-hang-ipos-list",
                ),
                path(
                    "thanh-toan/<uuid:uuid>/",
                    ThongTinThanhToanHoaDonBanHangIPosViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="thong-tin-thanh-toan-hoa-don-ban-hang-ipos-detail",
                ),
                # Chiet khau hoa don ban hang ipos routes
                path(
                    "chiet-khau/",
                    ChietKhauHoaDonBanHangIPosViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="chiet-khau-hoa-don-ban-hang-ipos-list",
                ),
                path(
                    "chiet-khau/<uuid:uuid>/",
                    ChietKhauHoaDonBanHangIPosViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="chiet-khau-hoa-don-ban-hang-ipos-detail",
                ),
                # Chi tiet chiet khau hoa don ban hang ipos routes
                path(
                    "chi-tiet-chiet-khau/",
                    ChiTietChietKhauHoaDonBanHangIPosViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="chi-tiet-chiet-khau-hoa-don-ban-hang-ipos-list",
                ),
                path(
                    "chi-tiet-chiet-khau/<uuid:uuid>/",
                    ChiTietChietKhauHoaDonBanHangIPosViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="chi-tiet-chiet-khau-hoa-don-ban-hang-ipos-detail",
                ),
            ]
        ),
    ),
]
