"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for PhieuNhapDieuChinhGiaHangMua API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.mua_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_dieu_chinh_gia_hang_mua import (
    PhieuNhapDieuChinhGiaHangMuaViewSet,
    ChiTietPhieuNhapDieuChinhGiaHangMuaViewSet,
    ThuePhieuNhapDieuChinhGiaHangMuaViewSet
)

# Main router for PhieuNhapDieuChinhGiaHangMua
router = DefaultRouter()
router.register('', PhieuNhapDieuChinhGiaHangMuaViewSet, basename='phieu-nhap-dieu-chinh-gia-hang-mua')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),

    # Nested routes for phieu-nhap-dieu-chinh-gia-hang-mua
    path('<uuid:phieu_nhap_uuid>/', include([
        # Chi tiet phieu nhap dieu chinh gia hang mua routes
        path('chi-tiet/', ChiTietPhieuNhapDieuChinhGiaHangMuaViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-tiet-phieu-nhap-dieu-chinh-gia-hang-mua-list'),

        path('chi-tiet/<uuid:uuid>/', ChiTietPhieuNhapDieuChinhGiaHangMuaViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-tiet-phieu-nhap-dieu-chinh-gia-hang-mua-detail'),

        # Thue phieu nhap dieu chinh gia hang mua routes
        path('thue/', ThuePhieuNhapDieuChinhGiaHangMuaViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='thue-phieu-nhap-dieu-chinh-gia-hang-mua-list'),

        path('thue/<uuid:uuid>/', ThuePhieuNhapDieuChinhGiaHangMuaViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='thue-phieu-nhap-dieu-chinh-gia-hang-mua-detail'),
    ])),
]
