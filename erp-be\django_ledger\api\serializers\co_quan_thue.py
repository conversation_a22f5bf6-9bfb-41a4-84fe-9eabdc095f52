from rest_framework import serializers

from django_ledger.api.serializers.base import GlobalModelSerializer
from django_ledger.models import CoQuanThueModel


class CoQuanThueModelSerializer(GlobalModelSerializer):
    """
    Serializer for the CoQuanThueModel (Tax Authority) model.

    This serializer handles the conversion between CoQuanThueModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - ma_cqt: Unique code for the tax authority
    - ten_cqt: Name of the tax authority
    - ma_cqt0: Parent tax authority code (optional)
    - status: Status indicator ('0'=inactive, '1'=active)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    """
    parent_name = serializers.SerializerMethodField()

    class Meta:
        model = CoQuanThueModel
        fields = ['uuid', 'entity_model', 'ma_cqt', 'ten_cqt', 'ma_cqt0', 'parent_name', 'status', 'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated', 'parent_name']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_cqt": "CQT01",
                "ten_cqt": "Cục thuế TP Hồ Chí Minh",
                "ma_cqt0": None,
                "parent_name": None,
                "status": "1",
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z"
            }
        }

    def get_parent_name(self, obj):
        """
        Get the name of the parent tax authority.
        """
        if obj.ma_cqt0:
            return obj.ma_cqt0.ten_cqt
        return None
