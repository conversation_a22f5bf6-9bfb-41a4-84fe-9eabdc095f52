"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Bang Ke TSCD Het K<PERSON>u Hao Con Su Dung (Fixed Assets Fully Depreciated But Still In Use Report) API.
"""

from rest_framework import serializers


class BangKeTSCDHetKhauHaoConSuDungRequestSerializer(serializers.Serializer):
    """
    Serializer for validating Fixed Assets Fully Depreciated But Still In Use Report request data.
    Validates all POST body parameters from cURL request.
    """

    # Required date field - matching the cURL request format "20250505"
    ngay_bc = serializers.Char<PERSON><PERSON>(
        required=True, help_text="Ng<PERSON>y báo cáo (YYYYMMDD format)"
    )

    # Optional filter parameters (UUID or string for model references)
    ma_lts = serializers.Char<PERSON>ield(
        required=False, allow_blank=True, help_text="UUID hoặc mã loại tài sản filter"
    )
    ma_bp = serializers.Char<PERSON>ield(
        required=False, allow_blank=True, help_text="UUID hoặc mã bộ phận filter"
    )
    nh_ts1 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="UUID hoặc mã nhóm tài sản 1 filter",
    )
    nh_ts2 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="UUID hoặc mã nhóm tài sản 2 filter",
    )
    nh_ts3 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="UUID hoặc mã nhóm tài sản 3 filter",
    )
    ma_unit = serializers.CharField(
        required=False, allow_blank=True, help_text="Unit UUID"
    )
    mau_bc = serializers.IntegerField(
        required=False, default=20, help_text="Mẫu báo cáo"
    )
    data_analysis_struct = serializers.CharField(
        required=False, allow_blank=True, help_text="Cấu trúc phân tích dữ liệu"
    )

    def validate_ngay_bc(self, value):
        """
        Validate report date in YYYYMMDD format and convert to date object.
        """
        if not value:
            raise serializers.ValidationError("Ngày báo cáo là bắt buộc")

        # Convert YYYYMMDD string to date object
        try:
            from datetime import datetime

            if len(value) == 8 and value.isdigit():
                # Parse YYYYMMDD format
                parsed_date = datetime.strptime(value, "%Y%m%d").date()
                return parsed_date
            else:
                # Try standard date formats
                for fmt in ["%Y-%m-%d", "%d/%m/%Y", "%d-%m-%Y"]:
                    try:
                        parsed_date = datetime.strptime(value, fmt).date()
                        return parsed_date
                    except ValueError:
                        continue
                raise ValueError("Invalid date format")
        except ValueError:
            raise serializers.ValidationError(
                "Ngày báo cáo phải có định dạng YYYYMMDD hoặc YYYY-MM-DD"
            )

    def validate(self, attrs):
        """
        Cross-field validation if needed.
        """
        # Add any cross-field validation here
        return attrs


class BangKeTSCDHetKhauHaoConSuDungResponseSerializer(serializers.Serializer):
    """
    Serializer for formatting single fixed assets fully depreciated but still in use report record.
    """

    stt = serializers.IntegerField(help_text="Số thứ tự")
    ma_ts = serializers.CharField(help_text="Mã tài sản")
    ten_ts = serializers.CharField(help_text="Tên tài sản")
    ma_bp = serializers.CharField(help_text="Mã bộ phận")
    nguyen_gia = serializers.DecimalField(
        max_digits=20, decimal_places=2, help_text="Nguyên giá"
    )
    gt_da_kh = serializers.DecimalField(
        max_digits=20, decimal_places=2, help_text="Giá trị đã khấu hao"
    )
    ngay_mua = serializers.CharField(help_text="Ngày mua", allow_blank=True)
    ngay_kh0 = serializers.CharField(
        help_text="Ngày khấu hao lần đầu", allow_blank=True
    )
    so_ky_kh = serializers.IntegerField(help_text="Số kỳ khấu hao")
    ngay_kh_kt = serializers.CharField(
        help_text="Ngày khấu hao kết thúc", allow_blank=True
    )
    unit_id = serializers.CharField(help_text="Unit ID", allow_blank=True)
    ma_unit = serializers.CharField(help_text="Mã unit", allow_blank=True)
