"""
Purchase Order Status Report Serializers.

This module provides request and response serializers for the purchase order status report API.
"""

from rest_framework import serializers


class BaoCaoTinhTrangDonHangMuaRequestSerializer(serializers.Serializer):
    """
    Serializer for Purchase Order Status Report request parameters.
    Validates all filter parameters from the cURL request.
    """
    
    # Date range filters (required)
    ngay_ct1 = serializers.DateField(
        required=True,
        help_text="Start date for transaction date range (YYYY-MM-DD)"
    )
    ngay_ct2 = serializers.DateField(
        required=True,
        help_text="End date for transaction date range (YYYY-MM-DD)"
    )
    
    # Document number range filters (optional)
    so_ct1 = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Start document number range"
    )
    so_ct2 = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="End document number range"
    )
    
    # Customer filters
    ma_kh = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Customer/Supplier code filter"
    )
    
    # Customer group filters
    nh_kh1 = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=255,
        help_text="Customer group 1 filter"
    )
    nh_kh2 = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=255,
        help_text="Customer group 2 filter"
    )
    nh_kh3 = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=255,
        help_text="Customer group 3 filter"
    )
    
    # Material filters
    ma_vt = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Material code filter"
    )
    ma_lvt = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Material type filter"
    )
    
    # Material group filters
    nh_vt1 = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=255,
        help_text="Material group 1 filter"
    )
    nh_vt2 = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=255,
        help_text="Material group 2 filter"
    )
    nh_vt3 = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=255,
        help_text="Material group 3 filter"
    )
    
    # Warehouse and location filters
    ma_kho = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Warehouse code filter"
    )
    ma_lo = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Batch/Lot code filter"
    )
    ma_vi_tri = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Location code filter"
    )
    
    # Date and status filters
    ngay_giao = serializers.DateField(
        required=False,
        allow_null=True,
        help_text="Delivery date filter (YYYY-MM-DD)"
    )
    status = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Order status filter"
    )
    dien_giai = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=500,
        help_text="Description filter"
    )
    
    # Transaction code filter
    ma_gd = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Transaction code filter"
    )
    
    # Standard parameters
    ma_unit = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Unit filter"
    )
    mau_bc = serializers.IntegerField(
        required=False,
        default=20,
        help_text="Report template number"
    )

    def validate(self, data):
        """
        Validate the date range and other business rules.
        """
        ngay_ct1 = data.get('ngay_ct1')
        ngay_ct2 = data.get('ngay_ct2')
        
        if ngay_ct1 and ngay_ct2 and ngay_ct1 > ngay_ct2:
            raise serializers.ValidationError(
                "Start date (ngay_ct1) must be less than or equal to end date (ngay_ct2)"
            )
        
        # Validate document number range
        so_ct1 = data.get('so_ct1')
        so_ct2 = data.get('so_ct2')
        
        if so_ct1 and so_ct2 and so_ct1 > so_ct2:
            raise serializers.ValidationError(
                "Start document number (so_ct1) must be less than or equal to end document number (so_ct2)"
            )
        
        return data


class BaoCaoTinhTrangDonHangMuaResponseSerializer(serializers.Serializer):
    """
    Serializer for Purchase Order Status Report response data.
    Defines all fields that should be returned in the report.
    """
    
    # System fields
    id = serializers.CharField(
        max_length=50,
        help_text="Record UUID"
    )
    line = serializers.IntegerField(
        help_text="Line number"
    )
    unit_id = serializers.CharField(
        max_length=50,
        help_text="Unit ID"
    )
    
    # Document fields
    ngay_ct = serializers.DateField(
        help_text="Transaction date"
    )
    so_ct = serializers.CharField(
        max_length=50,
        help_text="Document number"
    )
    ma_kh = serializers.CharField(
        max_length=50,
        help_text="Customer/Supplier code"
    )
    status = serializers.CharField(
        max_length=50,
        help_text="Order status"
    )
    ma_ct = serializers.CharField(
        max_length=50,
        help_text="Document type code"
    )
    
    # Product fields
    ma_vt = serializers.CharField(
        max_length=50,
        help_text="Material code"
    )
    dvt = serializers.CharField(
        max_length=20,
        help_text="Unit of measure"
    )
    he_so = serializers.DecimalField(
        max_digits=15,
        decimal_places=6,
        help_text="Conversion factor"
    )
    
    # Date and pricing fields
    ngay_giao = serializers.DateField(
        allow_null=True,
        help_text="Delivery date"
    )
    gia = serializers.DecimalField(
        max_digits=15,
        decimal_places=3,
        help_text="Unit price"
    )
    tien = serializers.DecimalField(
        max_digits=15,
        decimal_places=3,
        help_text="Total amount"
    )
    
    # Quantity fields
    so_luong = serializers.DecimalField(
        max_digits=15,
        decimal_places=3,
        help_text="Order quantity"
    )
    sl_nhap = serializers.DecimalField(
        max_digits=15,
        decimal_places=3,
        help_text="Received quantity"
    )
    sl_hd = serializers.DecimalField(
        max_digits=15,
        decimal_places=3,
        help_text="Invoiced quantity"
    )
    sl_dh = serializers.DecimalField(
        max_digits=15,
        decimal_places=3,
        help_text="Order quantity (duplicate)"
    )
    sl_tl = serializers.DecimalField(
        max_digits=15,
        decimal_places=3,
        help_text="Remaining quantity"
    )
    sl_cl = serializers.DecimalField(
        max_digits=15,
        decimal_places=3,
        help_text="Outstanding quantity"
    )
    
    # Name fields
    ten_kh = serializers.CharField(
        max_length=255,
        help_text="Customer/Supplier name"
    )
    ten_vt = serializers.CharField(
        max_length=255,
        help_text="Material name"
    )
    ten_ttct = serializers.CharField(
        max_length=255,
        help_text="Status description"
    )
    ma_unit = serializers.CharField(
        max_length=50,
        help_text="Unit code"
    )
