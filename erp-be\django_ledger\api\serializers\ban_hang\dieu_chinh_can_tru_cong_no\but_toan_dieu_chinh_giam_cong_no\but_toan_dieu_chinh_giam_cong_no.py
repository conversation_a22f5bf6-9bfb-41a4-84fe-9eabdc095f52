"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ButToanDieuChinhGiamCongNo (Debt Reduction Adjustment) model.
"""

from rest_framework import serializers

from django_ledger.models.ban_hang.dieu_chinh_can_tru_cong_no.but_toan_dieu_chinh_giam_cong_no import (
    ButToanDieuChinhGiamCongNoModel,
    ChiTietButToanDieuChinhGiamCongNoModel
)
from django_ledger.api.serializers.ban_hang.dieu_chinh_can_tru_cong_no.but_toan_dieu_chinh_giam_cong_no.chi_tiet_but_toan_dieu_chinh_giam_cong_no import ChiTietButToanDieuChinhGiamCongNoSerializer
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.accounts import AccountModelSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.quyen_chung_tu import QuyenChungTuChiTietSerializer
from django_ledger.api.serializers.chung_tu import ChungTuSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer 
from django_ledger.api.serializers.han_thanh_toan import HanThanhToanModelSerializer


class ButToanDieuChinhGiamCongNoSerializer(serializers.ModelSerializer):
    """
    Serializer for the ButToanDieuChinhGiamCongNoModel
    """
    entity_model_data = serializers.SerializerMethodField()
    tk_data = serializers.SerializerMethodField()
    ma_nk_data = serializers.SerializerMethodField()
    so_ct_data = serializers.SerializerMethodField()
    ma_nt_data = serializers.SerializerMethodField()
    so_ct0_data = serializers.SerializerMethodField()
    ma_tt_data = serializers.SerializerMethodField()
    ma_kh_data = serializers.SerializerMethodField()
    so_ct_goc_data = serializers.SerializerMethodField()

    chi_tiet_data = ChiTietButToanDieuChinhGiamCongNoSerializer(source='chitiet', many=True, read_only=True)
    chitiet_data = serializers.ListField(write_only=True, required=False)

    class Meta:
        model = ButToanDieuChinhGiamCongNoModel
        fields = [
            'uuid',
            'entity_model',
            'entity_model_data',
            'ma_ngv',
            'dien_giai',
            'tk',
            'tk_data',
            'unit_id',
            'i_so_ct',
            'ma_nk',
            'ma_nk_data',
            'so_ct',
            'so_ct_data',
            'ngay_ct',
            'ngay_lct',
            'ma_nt',
            'ma_nt_data',
            'ty_gia',
            'status',
            'transfer_yn',
            'hd_yn',
            'so_ct0',
            'so_ct0_data',
            'ngay_ct0',
            'ma_tt',
            'ma_tt_data',
            'tg_dd',
            'cltg_yn',
            'ma_kh',
            'ma_kh_data',
            'so_ct_goc',
            'so_ct_goc_data',
            'dien_giai_ct_goc',
            't_tien_nt',
            't_tien',
            'chitiet_data',
            'chi_tiet_data',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'entity_model_data',
            'tk_data',
            'ma_nk_data',
            'so_ct_data',
            'ma_nt_data',
            'so_ct0_data',
            'ma_tt_data',
            'ma_kh_data',
            'so_ct_goc_data',
            'chi_tiet_data',
            'created',
            'updated'
        ]

    def get_entity_model_data(self, obj):
        if obj.entity_model:
            return EntityModelSerializer(obj.entity_model).data
        return None

    def get_tk_data(self, obj):
        if obj.tk:
            return AccountModelSerializer(obj.tk).data
        return None

    def get_ma_nk_data(self, obj):
        if obj.ma_nk:
            return QuyenChungTuChiTietSerializer(obj.ma_nk).data
        return None

    def get_so_ct_data(self, obj):
        if obj.so_ct:
            return ChungTuSerializer(obj.so_ct).data
        return None

    def get_ma_nt_data(self, obj):
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_so_ct0_data(self, obj):
        if obj.so_ct0:
            return ChungTuSerializer(obj.so_ct0).data
        return None

    def get_ma_tt_data(self, obj):
        if obj.ma_tt:
            return HanThanhToanModelSerializer(obj.ma_tt).data
        return None

    def get_ma_kh_data(self, obj):
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_so_ct_goc_data(self, obj):
        if obj.so_ct_goc:
            return ChungTuSerializer(obj.so_ct_goc).data
        return None
