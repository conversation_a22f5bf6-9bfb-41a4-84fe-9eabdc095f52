"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for DinhMucNguyenVatLieu model.
"""
from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models import DinhMucNguyenVatLieuModel
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.gia_thanh.dinh_muc_nguyen_vat_lieu.chi_tiet_dinh_muc_nguyen_vat_lieu import ChiTietDinhMucNguyenVatLieuSerializer



class DinhMucNguyenVatLieuSerializer(serializers.ModelSerializer):
    """
    Serializer for DinhMucNguyenVatLieu model.
    """
    # Read-only fields for related objects
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)

    # Child data
    chi_tiet_data = serializers.SerializerMethodField(read_only=True)
    chi_tiet = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = DinhMucNguyenVatLieuModel
        fields = [
            'uuid',
            'entity_model',
            'ma_sp',
            'ma_sp_data',
            'dvt',
            'dvt_data',
            'he_so',
            'ma_bp',
            'ma_bp_data',
            'id',
            'unit_id',
            'status',
            'ngay_ct',
            't_so_luong',
            'chi_tiet_data',
            'chi_tiet',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'ma_sp_data',
            'dvt_data',
            'ma_bp_data',
            'chi_tiet_count',
            'chi_tiet_data',
            'created',
            'updated'
        ]


    def get_ma_sp_data(self, obj):
        """
        Get product data.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_dvt_data(self, obj):
        """
        Get unit of measure data.
        """
        if obj.dvt:
            return DonViTinhSerializer(obj.dvt).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_chi_tiet_data(self, obj):
        """
        Get child details data.
        """
        chi_tiet = obj.chi_tiet.all()
        return ChiTietDinhMucNguyenVatLieuSerializer(chi_tiet, many=True).data




