"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Journal Entry serializer implementation.
"""

from django_ledger.models.journal_entry import (
    JournalEntryDetailModel,
    JournalEntryModel,
    TaxInformationModel,
)
from rest_framework import serializers


class JournalEntryDetailSerializer(serializers.ModelSerializer):
    """
    Serializer for JournalEntryDetailModel.
    """

    class Meta:
        model = JournalEntryDetailModel
        fields = [
            "uuid",
            "phieu_ke_toan",
            "line",
            "tk",
            "tk_cn",
            "ma_kh",
            "ty_gia2",
            "ps_no_nt",
            "ps_co_nt",
            "dien_giai",
            "so_ct0",
            "ngay_ct0",
            "ps_no",
            "ps_co",
            "ma_bp",
            "ma_vv",
            "ma_hd",
            "ma_dtt",
            "ma_ku",
            "ma_phi",
            "ma_sp",
            "ma_lsx",
            "ma_cp0",
            "id_tt",
        ]


class TaxInformationSerializer(serializers.ModelSerializer):
    """
    Serializer for TaxInformationModel.
    """

    class Meta:
        model = TaxInformationModel
        fields = [
            "uuid",
            "phieu_ke_toan",
            "line",
            "so_ct0",
            "so_ct2",
            "ngay_ct0",
            "ma_thue",
            "thue_suat",
            "ma_mau_ct",
            "ma_mau_bc",
            "ma_tc_thue",
            "ma_kh",
            "ten_kh_thue",
            "dia_chi",
            "ma_so_thue",
            "ten_vt_thue",
            "t_tien_nt",
            "t_tien",
            "tk_thue_no",
            "ten_tk_thue_no",
            "tk_du",
            "ten_tk_du",
            "t_thue_nt",
            "t_thue",
            "ma_kh9",
            "ten_kh9",
            "ma_tt",
            "ten_tt",
            "ghi_chu",
            "id_tt",
            "ma_bp",
            "ma_vv",
            "ma_hd",
            "ma_dtt",
            "ma_ku",
            "ma_phi",
            "ma_sp",
            "ma_lsx",
            "ma_cp0",
        ]


class JournalEntrySerializer(serializers.ModelSerializer):
    """
    Serializer for JournalEntryModel.
    """

    details = JournalEntryDetailSerializer(many=True, read_only=True)
    tax_information = TaxInformationSerializer(many=True, read_only=True)

    class Meta:
        model = JournalEntryModel
        fields = [
            "uuid",
            "ma_ngv",
            "unit_id",
            "i_so_ct",
            "ma_nk",
            "so_ct",
            "ngay_ct",
            "ngay_lct",
            "dien_giai",
            "ma_nt",
            "ty_gia",
            "status",
            "transfer_yn",
            "t_ps_no_nt",
            "t_ps_no",
            "t_ps_co_nt",
            "t_ps_co",
            "details",
            "tax_information",
        ]
