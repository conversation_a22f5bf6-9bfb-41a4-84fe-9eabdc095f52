"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Khe Uoc (Loan Agreement) module.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import KheUocModelViewSet
from django_ledger.api.views.danh_muc.hop_dong_khe_uoc.khe_uoc.chi_tiet_khe_uoc_lai_suat import ChiTietKheUocLaiSuatModelViewSet
from django_ledger.api.views.danh_muc.hop_dong_khe_uoc.khe_uoc.chi_tiet_khe_uoc_thanh_toan import ChiTietKheUocThanhToanModelViewSet

# Main router for KheUoc
router = DefaultRouter()
router.register('', KheUocModelViewSet, basename='khe-uoc')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),

    # Nested routes for khe-uoc
    path('<uuid:khe_uoc_uuid>/', include([
        # Chi tiet khe uoc lai suat routes
        path('chi-tiet-lai-suat/', ChiTietKheUocLaiSuatModelViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-tiet-khe-uoc-lai-suat-list'),

        path('chi-tiet-lai-suat/<uuid:uuid>/', ChiTietKheUocLaiSuatModelViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-tiet-khe-uoc-lai-suat-detail'),

        # Chi tiet khe uoc thanh toan routes
        path('chi-tiet-thanh-toan/', ChiTietKheUocThanhToanModelViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-tiet-khe-uoc-thanh-toan-list'),

        path('chi-tiet-thanh-toan/<uuid:uuid>/', ChiTietKheUocThanhToanModelViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-tiet-khe-uoc-thanh-toan-detail'),
    ])),
]
