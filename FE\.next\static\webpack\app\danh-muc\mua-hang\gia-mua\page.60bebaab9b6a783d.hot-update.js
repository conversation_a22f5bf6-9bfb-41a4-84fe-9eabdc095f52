"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/danh-muc/mua-hang/gia-mua/page",{

/***/ "(app-pages-browser)/./src/hooks/queries/useGiaMua.ts":
/*!****************************************!*\
  !*** ./src/hooks/queries/useGiaMua.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGiaMua: function() { return /* binding */ useGiaMua; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants */ \"(app-pages-browser)/./src/constants/index.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\n\n\n/**\r\n * Hook for managing GiaMua (Purchase Price) data\r\n *\r\n * This hook provides functions to fetch, create, update, and delete purchase prices.\r\n */ const useGiaMua = function() {\n    let initialGiaMuas = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];\n    const [giaMuas, setGiaMuas] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialGiaMuas);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const { entity } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const fetchGiaMuas = async ()=>{\n        if (!(entity === null || entity === void 0 ? void 0 : entity.slug)) return;\n        setIsLoading(true);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/entities/\".concat(entity.slug, \"/erp/\").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.QUERY_KEYS.GIA_MUA, \"/\"));\n            setGiaMuas(response.data.results);\n        } catch (error) {\n            console.error(\"Error fetching purchase prices:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const addGiaMua = async (newGiaMua)=>{\n        if (!(entity === null || entity === void 0 ? void 0 : entity.slug)) throw new Error(\"Entity slug is required\");\n        setIsLoading(true);\n        try {\n            let payload;\n            // Check if input is GiaMuaFormData and validate/convert it\n            if (isGiaMuaFormData(newGiaMua)) {\n                validateGiaMuaFormData(newGiaMua);\n                payload = convertFormDataToInput(newGiaMua);\n            } else {\n                payload = newGiaMua;\n            }\n            // Thêm header Content-Type để đảm bảo server hiểu đúng định dạng dữ liệu\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/entities/\".concat(entity.slug, \"/erp/\").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.QUERY_KEYS.GIA_MUA, \"/\"), payload, {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const addedGiaMua = response.data;\n            setGiaMuas((prev)=>[\n                    ...prev,\n                    addedGiaMua\n                ]);\n            return addedGiaMua;\n        } catch (error) {\n            console.error(\"Error adding purchase price:\", error);\n            if (error.response) {\n                var _error_response_data, _error_response_data1;\n                // Tạo thông báo lỗi chi tiết\n                let errorMessage = ((_error_response_data = error.response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || \"\";\n                // Handle validation errors which come as an object with field names as keys\n                if (typeof error.response.data === \"object\" && !((_error_response_data1 = error.response.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.detail)) {\n                    const validationErrors = [];\n                    for(const field in error.response.data){\n                        if (Array.isArray(error.response.data[field])) {\n                            validationErrors.push(\"\".concat(field, \": \").concat(error.response.data[field].join(\", \")));\n                        }\n                    }\n                    if (validationErrors.length > 0) {\n                        errorMessage = validationErrors.join(\"\\n\");\n                    } else {\n                        errorMessage = JSON.stringify(error.response.data);\n                    }\n                }\n                if (!errorMessage) {\n                    errorMessage = error.message || \"Unknown error\";\n                }\n                throw new Error(errorMessage);\n            }\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const updateGiaMua = async (uuid, updatedGiaMua)=>{\n        if (!(entity === null || entity === void 0 ? void 0 : entity.slug)) throw new Error(\"Entity slug is required\");\n        setIsLoading(true);\n        try {\n            let payload;\n            // Check if input is GiaMuaFormData and validate/convert it\n            if (isGiaMuaFormData(updatedGiaMua)) {\n                validateGiaMuaFormData(updatedGiaMua);\n                payload = convertFormDataToInput(updatedGiaMua);\n            } else {\n                payload = updatedGiaMua;\n            }\n            // Thêm header Content-Type để đảm bảo server hiểu đúng định dạng dữ liệu\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].put(\"/entities/\".concat(entity.slug, \"/erp/\").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.QUERY_KEYS.GIA_MUA, \"/\").concat(uuid, \"/\"), payload, {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const updatedGiaMuaData = response.data;\n            // Cập nhật state giaMuas với dữ liệu mới\n            setGiaMuas((prev)=>prev.map((giaMua)=>giaMua.uuid === updatedGiaMuaData.uuid ? updatedGiaMuaData : giaMua));\n            return updatedGiaMuaData;\n        } catch (error) {\n            console.error(\"Error updating purchase price:\", error);\n            if (error.response) {\n                var _error_response_data, _error_response_data1;\n                // Tạo thông báo lỗi chi tiết\n                let errorMessage = ((_error_response_data = error.response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || \"\";\n                // Handle validation errors which come as an object with field names as keys\n                if (typeof error.response.data === \"object\" && !((_error_response_data1 = error.response.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.detail)) {\n                    const validationErrors = [];\n                    for(const field in error.response.data){\n                        if (Array.isArray(error.response.data[field])) {\n                            validationErrors.push(\"\".concat(field, \": \").concat(error.response.data[field].join(\", \")));\n                        }\n                    }\n                    if (validationErrors.length > 0) {\n                        errorMessage = validationErrors.join(\"\\n\");\n                    } else {\n                        errorMessage = JSON.stringify(error.response.data);\n                    }\n                }\n                if (!errorMessage) {\n                    errorMessage = error.message || \"Unknown error\";\n                }\n                throw new Error(errorMessage);\n            }\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const deleteGiaMua = async (uuid)=>{\n        if (!(entity === null || entity === void 0 ? void 0 : entity.slug)) throw new Error(\"Entity slug is required\");\n        setIsLoading(true);\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].delete(\"/entities/\".concat(entity.slug, \"/erp/purchase-prices/\").concat(uuid, \"/\"));\n            setGiaMuas((prev)=>prev.filter((giaMua)=>giaMua.uuid !== uuid));\n        } catch (error) {\n            console.error(\"Error deleting purchase price:\", error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const refreshGiaMuas = async ()=>{\n        await fetchGiaMuas();\n    };\n    // Helper function to check if data is GiaMuaFormData\n    const isGiaMuaFormData = (data)=>{\n        return \"formData\" in data && \"vatTu\" in data && \"donViTinh\" in data && \"nhaCungCap\" in data && \"ngoaiTe\" in data;\n    };\n    // Helper function to validate GiaMuaFormData\n    const validateGiaMuaFormData = (data)=>{\n        var _data_vatTu, _data_donViTinh, _data_nhaCungCap, _data_ngoaiTe;\n        // 1. Mã vật tư - bắt buộc\n        if (!((_data_vatTu = data.vatTu) === null || _data_vatTu === void 0 ? void 0 : _data_vatTu.uuid)) {\n            throw new Error(\"M\\xe3 vật tư kh\\xf4ng được bỏ trống\");\n        }\n        // 2. Đơn vị tính - bắt buộc (có thể có giá trị mặc định)\n        if (!((_data_donViTinh = data.donViTinh) === null || _data_donViTinh === void 0 ? void 0 : _data_donViTinh.uuid)) {\n            throw new Error(\"Đơn vị t\\xednh kh\\xf4ng được bỏ trống\");\n        }\n        // 3. Ngày hiệu lực - bắt buộc\n        if (!data.formData.ngay_hieu_luc || data.formData.ngay_hieu_luc.trim() === \"\") {\n            throw new Error(\"Ng\\xe0y hiệu lực kh\\xf4ng được bỏ trống\");\n        }\n        // 4. Nhà cung cấp - bắt buộc\n        if (!((_data_nhaCungCap = data.nhaCungCap) === null || _data_nhaCungCap === void 0 ? void 0 : _data_nhaCungCap.uuid)) {\n            throw new Error(\"Nh\\xe0 cung cấp kh\\xf4ng được bỏ trống\");\n        }\n        // 5. Ngoại tệ - bắt buộc (có thể có giá trị mặc định)\n        if (!((_data_ngoaiTe = data.ngoaiTe) === null || _data_ngoaiTe === void 0 ? void 0 : _data_ngoaiTe.uuid)) {\n            throw new Error(\"Ngoại tệ kh\\xf4ng được bỏ trống\");\n        }\n        // 6. Số lượng từ - bắt buộc\n        if (data.formData.so_luong_tu === \"\" || data.formData.so_luong_tu === null || data.formData.so_luong_tu === undefined) {\n            throw new Error(\"Số lượng từ kh\\xf4ng được bỏ trống\");\n        }\n    };\n    // Helper function to convert GiaMuaFormData to GiaMuaInput\n    const convertFormDataToInput = (data)=>{\n        var _data_vatTu, _data_donViTinh, _data_nhaCungCap, _data_ngoaiTe;\n        return {\n            ma_vat_tu: ((_data_vatTu = data.vatTu) === null || _data_vatTu === void 0 ? void 0 : _data_vatTu.uuid) || \"\",\n            don_vi_tinh: ((_data_donViTinh = data.donViTinh) === null || _data_donViTinh === void 0 ? void 0 : _data_donViTinh.uuid) || \"\",\n            ngay_hieu_luc: data.formData.ngay_hieu_luc || null,\n            nha_cung_cap: ((_data_nhaCungCap = data.nhaCungCap) === null || _data_nhaCungCap === void 0 ? void 0 : _data_nhaCungCap.uuid) || \"\",\n            ngoai_te: ((_data_ngoaiTe = data.ngoaiTe) === null || _data_ngoaiTe === void 0 ? void 0 : _data_ngoaiTe.uuid) || \"\",\n            so_luong_tu: Number(data.formData.so_luong_tu) || 0,\n            gia_mua: Number(data.formData.gia_mua) || 0,\n            trang_thai: Number(data.formData.trang_thai) || 1\n        };\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        fetchGiaMuas();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        entity === null || entity === void 0 ? void 0 : entity.slug\n    ]);\n    return {\n        giaMuas,\n        isLoading,\n        addGiaMua,\n        updateGiaMua,\n        deleteGiaMua,\n        refreshGiaMuas\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/queries/useGiaMua.ts\n"));

/***/ })

});