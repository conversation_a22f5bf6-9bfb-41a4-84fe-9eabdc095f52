"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for tien_mat.hach_toan.phieu_thu module.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.tien_mat.hach_toan.phieu_thu import (
    PhieuThuViewSet,
    PhieuThuChiTietViewSet,
)

# Main router for PhieuThu
router = DefaultRouter()
router.register("", PhieuThuViewSet, basename="phieu-thu")

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path("", include(router.urls)),
    # Nested routes for phieu_thu
    path(
        "<uuid:phieu_thu_uuid>/",
        include(
            [
                # PhieuThuChiTiet routes
                path(
                    "chi-tiet/",
                    PhieuThuChiTietViewSet.as_view({"get": "list", "post": "create"}),
                    name="phieu-thu-chi-tiet-list",
                ),
                path(
                    "chi-tiet/<uuid:uuid>/",
                    PhieuThuChiTietViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="phieu-thu-chi-tiet-detail",
                ),
            ]
        ),
    ),
]
