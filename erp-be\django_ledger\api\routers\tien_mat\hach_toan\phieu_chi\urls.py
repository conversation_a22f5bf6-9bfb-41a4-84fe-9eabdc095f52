"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL configuration for PhieuChi API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.tien_mat.hach_toan.phieu_chi.phieu_chi import PhieuChiViewSet
from django_ledger.api.views.tien_mat.hach_toan.phieu_chi.phieu_chi_chi_tiet import PhieuChiChiTietViewSet
from django_ledger.api.views.tien_mat.hach_toan.phieu_chi.phieu_chi_thue import PhieuChiThueViewSet

# Main router for PhieuChi
router = DefaultRouter()
router.register("", PhieuChiViewSet, basename="phieu-chi")

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path("", include(router.urls)),
    # Nested routes for phieu_chi
    path(
        "<uuid:phieu_chi_uuid>/",
        include(
            [
                # PhieuChiChiTiet routes
                path(
                    "chi-tiet/",
                    PhieuChiChiTietViewSet.as_view({"get": "list", "post": "create"}),
                    name="phieu-chi-chi-tiet-list",
                ),
                path(
                    "chi-tiet/<uuid:uuid>/",
                    PhieuChiChiTietViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="phieu-chi-chi-tiet-detail",
                ),
                # PhieuChiThue routes
                path(
                    "thue/",
                    PhieuChiThueViewSet.as_view({"get": "list", "post": "create"}),
                    name="phieu-chi-thue-list",
                ),
                path(
                    "thue/<uuid:uuid>/",
                    PhieuChiThueViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="phieu-chi-thue-detail",
                ),
            ]
        ),
    ),
]
