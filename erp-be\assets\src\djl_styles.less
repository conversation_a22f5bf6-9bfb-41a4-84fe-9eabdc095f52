@margins: range(1, 10, 1);
@sectionMinHeights: range(100, 500, 100);
@pf: djetler;

each(@margins, {
  .@{pf}_my_@{value} {
    margin-top: (@value * 1rem);
    margin-bottom: (@value * 1rem);
  }
  .@{pf}_mt_@{value} {
    margin-top: (@value * 1rem);
  }
  .@{pf}_mb_@{value} {
    margin-bottom: (@value * 1rem);
  }
})

each(@margins, {
  .@{pf}_mx_@{value} {
    margin-left: (@value * 1rem);
    margin-right: (@value * 1rem);
  }
  .@{pf}_ml_@{value} {
    margin-left: (@value * 1rem);
  }
  .@{pf}_mr_@{value} {
    margin-right: (@value * 1rem);
  }
})

each(@sectionMinHeights, {
  .@{pf}-section-min@{value} {
    min-height: (@value * 1px);
  }
})


.djetler-box-green {
  background-color: #18ff6f;
}

.djetler-box-yellow {
  background-color: #ffd931;
}

.djetler-box-blue {
  background-color: #374bff;
}

.django-ledger-login {
  height: 100vh;
  background-size: cover;
  background-position: center;
}

@iconSizes: range(12, 120, 12);

each(@iconSizes, {
  .djl-icon-@{value} {
    font-size: (@value * 1px);
  }
})

.django-ledger-table-bottom-margin-75 {
  margin-bottom: 75px;
}

.django-ledger-table-bottom-margin-150 {
  margin-bottom: 150px;
}

.djl-is-strikethrough {
  text-decoration: line-through;
}

#djl-login-bg-image {
  background-repeat: no-repeat;
  background-size: cover;
}