"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Admin configuration for AccountModel.
"""

from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from django_ledger.models import AccountModel


class AccountModelAdmin(admin.ModelAdmin):
    """
    Admin class for AccountModel.
    """
    list_display = [
        'code',
        'name',
        'role',
        'balance_type',
        'active',
        'coa_model',
    ]
    list_filter = ['active', 'role', 'balance_type', 'coa_model']
    search_fields = ['code', 'name', 'role', 'uuid']
    readonly_fields = ['uuid', 'created', 'updated']
    fieldsets = [
        (_('Basic Information'), {
            'fields': [
                'uuid',
                'code',
                'name',
                'role',
                'balance_type',
                'active',
                'coa_model',
            ]
        }),
        (_('Metadata'), {
            'fields': [
                'created',
                'updated',
            ]
        }),
    ]
