"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for MauSoHD (Contract Number Format) model
"""

from rest_framework import serializers

from django_ledger.models import MauSoHDModel
from django_ledger.api.serializers.nhom_loai_hd import NhomLoaiHDModelSerializer


class MauSoHDModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the MauSoHDModel
    """
    nhom_loai_hd_data = NhomLoaiHDModelSerializer(source='nhom_loai_hd', read_only=True)

    class Meta:
        model = MauSoHDModel
        fields = [
            'uuid',
            'entity_model',
            'ma_mau_so',
            'ten_mau_so',
            'ten_mau_so2',
            'nhom_loai_hd',
            'nhom_loai_hd_data',
            'hddt_yn',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated', 'nhom_loai_hd_data', 'entity_model']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_mau_so": "HDMB-2023",
                "ten_mau_so": "Mẫu hợp đồng mua bán 2023",
                "ten_mau_so2": "Sales Contract Template 2023",
                "nhom_loai_hd": "123e4567-e89b-12d3-a456-************",
                "nhom_loai_hd_data": {
                    "uuid": "123e4567-e89b-12d3-a456-************",
                    "ma_nhom": "HDMB",
                    "ten_nhom": "Hợp đồng mua bán",
                    "ten_nhom2": "Sales Contract",
                    "status": "1",
                    "created": "2023-01-01T00:00:00Z",
                    "updated": "2023-01-02T00:00:00Z"
                },
                "hddt_yn": True,
                "status": "1",
                "created": "2023-01-01T00:00:00Z",
                "updated": "2023-01-02T00:00:00Z"
            }
        }

    def create(self, validated_data):
        # Get the entity_slug from the context
        entity_slug = self.context['view'].kwargs['entity_slug']

        # Get the EntityModel instance
        from django_ledger.models.entity import EntityModel
        entity_model = EntityModel.objects.for_user(
            user_model=self.context['request'].user
        ).get(slug__exact=entity_slug)

        # Set the entity_model in the validated_data
        validated_data['entity_model'] = entity_model

        # Create the MauSoHDModel instance
        return super().create(validated_data)
