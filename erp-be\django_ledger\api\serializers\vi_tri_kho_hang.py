"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for the ViTriKhoHang (Warehouse Location) model.
"""

from rest_framework import serializers
from django_ledger.models.vi_tri_kho_hang import ViTriKhoHangModel
from django_ledger.models.kho_hang import KhoHangModel
from django_ledger.api.serializers.unit import EntityUnitModelSerializer


class KhoHangModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the KhoHangModel when included in ViTriKhoHangModel.
    """
    unit_data = serializers.SerializerMethodField(read_only=True)

    def get_unit_data(self, obj):
        """
        Get the entity unit data corresponding to the unit_id field
        """
        if obj.unit_id:
            return EntityUnitModelSerializer(obj.unit_id).data
        return None

    class Meta:
        model = KhoHangModel
        fields = [
            'uuid',
            'ma_kho',
            'ten_kho',
            'dia_chi',
            'unit_id',
            'unit_data',
            'status'
        ]
        read_only_fields = ['uuid', 'unit_data']


class ViTriKhoHangModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the ViTriKhoHangModel.
    """
    ma_kho_data = KhoHangModelSerializer(source='ma_kho', read_only=True)

    class Meta:
        model = ViTriKhoHangModel
        fields = [
            'uuid',
            'ma_kho',
            'ma_kho_data',
            'ma_vi_tri',
            'ten_vi_tri',
            'ten_vi_tri2',
            'ghi_chu',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated']
        swagger_schema_fields = {
            'title': 'Vị trí kho hàng',
            'description': 'Vị trí trong kho hàng'
        }
