"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for TheGiaThanhSanPham (Product Cost Card) API.
"""

from rest_framework import serializers
from decimal import Decimal
from datetime import date


class TheGiaThanhSanPhamRequestSerializer(serializers.Serializer):
    """
    Serializer for TheGiaThanhSanPham request parameters.
    Validates all filter parameters for product cost card report.
    """
    
    # Period filters (required)
    tu_ky = serializers.IntegerField(
        required=True,
        min_value=1,
        max_value=12,
        help_text="Start period (month) from 1 to 12"
    )
    tu_nam = serializers.IntegerField(
        required=True,
        min_value=1900,
        help_text="Start year (e.g., 2025)"
    )
    den_ky = serializers.IntegerField(
        required=True,
        min_value=1,
        max_value=12,
        help_text="End period (month) from 1 to 12"
    )
    den_nam = serializers.IntegerField(
        required=True,
        min_value=1900,
        help_text="End year (e.g., 2025)"
    )
    
    # Product filter (optional)
    ma_sp = serializers.ListField(
        child=serializers.UUIDField(),
        required=False,
        allow_empty=True,
        help_text="List of product UUIDs to filter"
    )
    
    # Department filter (optional)
    ma_bp = serializers.ListField(
        child=serializers.UUIDField(),
        required=False,
        allow_empty=True,
        help_text="List of department UUIDs to filter"
    )
    
    # Production order filter (optional)
    ma_lsx = serializers.ListField(
        child=serializers.UUIDField(),
        required=False,
        allow_empty=True,
        help_text="List of production order UUIDs to filter"
    )
    
    # Unit ID filter (optional)
    unit_id = serializers.IntegerField(
        required=False,
        help_text="Unit ID filter"
    )
    
    # Report template (optional)
    mau_bc = serializers.IntegerField(
        required=False,
        default=20,
        help_text="Report template number"
    )
    
    # Data analysis structure (optional)
    data_analysis_struct = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=255,
        help_text="Data analysis structure"
    )

    def validate(self, data):
        """
        Validate the period range.
        """
        tu_ky = data.get('tu_ky')
        tu_nam = data.get('tu_nam')
        den_ky = data.get('den_ky')
        den_nam = data.get('den_nam')
        
        if tu_ky and tu_nam and den_ky and den_nam:
            start_period = tu_nam * 12 + tu_ky
            end_period = den_nam * 12 + den_ky
            
            if start_period > end_period:
                raise serializers.ValidationError(
                    "Start period (tu_ky/tu_nam) must be less than or equal to end period (den_ky/den_nam)"
                )
        
        return data


class TheGiaThanhSanPhamResponseSerializer(serializers.Serializer):
    """
    Serializer for TheGiaThanhSanPham response data.
    Defines all fields that should be returned in the product cost card report.
    """
    
    stt = serializers.CharField(
        max_length=10,
        help_text="Sequential number"
    )
    ma_sp = serializers.CharField(
        max_length=50,
        help_text="Product code (e.g., A1010010001)"
    )
    ma_bp = serializers.CharField(
        max_length=50,
        help_text="Department code (e.g., TTM-001)"
    )
    ma_lsx = serializers.CharField(
        max_length=50,
        help_text="Production order code (e.g., LSX001)"
    )
    sl_tp = serializers.DecimalField(
        max_digits=15,
        decimal_places=3,
        help_text="Finished product quantity"
    )
    gia = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Unit cost"
    )
    tien = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Total amount"
    )
    ten_sp = serializers.CharField(
        max_length=255,
        help_text="Product name"
    )
    dvt = serializers.CharField(
        max_length=50,
        help_text="Unit of measure"
    )
