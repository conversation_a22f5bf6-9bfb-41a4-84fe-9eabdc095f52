"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for ChiTietPhieuNhapHangBanTraLai (Customer Return Receipt Detail) model.
"""

from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai import ChiTietPhieuNhapHangBanTraLaiModel
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.warehouse import KhoHangModelSerializer
from django_ledger.api.serializers.vi_tri import ViTriModelSerializer
from django_ledger.api.serializers.lo import LoModelSerializer
from django_ledger.api.serializers.account import AccountModelSerializer


class ChiTietPhieuNhapHangBanTraLaiModelSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietPhieuNhapHangBanTraLai model.
    """
    # Read-only fields for related objects
    phieu_nhap_hang_ban_tra_lai_data = serializers.SerializerMethodField(read_only=True)
    ma_vt_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)
    ma_kho_data = serializers.SerializerMethodField(read_only=True)
    ma_vi_tri_data = serializers.SerializerMethodField(read_only=True)
    ma_lo_data = serializers.SerializerMethodField(read_only=True)
    tk_no_data = serializers.SerializerMethodField(read_only=True)
    tk_co_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietPhieuNhapHangBanTraLaiModel
        fields = [
            'uuid',
            'phieu_nhap_hang_ban_tra_lai',
            'phieu_nhap_hang_ban_tra_lai_data',
            'line',
            'ma_vt',
            'ma_vt_data',
            'ten_vt',
            'dvt',
            'dvt_data',
            'so_luong',
            'don_gia',
            'tien',
            'ty_le_ck',
            'tien_ck',
            'ty_le_thue',
            'tien_thue',
            'tien_tt',
            'ma_kho',
            'ma_kho_data',
            'ma_vi_tri',
            'ma_vi_tri_data',
            'ma_lo',
            'ma_lo_data',
            'tk_no',
            'tk_no_data',
            'tk_co',
            'tk_co_data',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'phieu_nhap_hang_ban_tra_lai',
            'phieu_nhap_hang_ban_tra_lai_data',
            'ma_vt_data',
            'dvt_data',
            'ma_kho_data',
            'ma_vi_tri_data',
            'ma_lo_data',
            'tk_no_data',
            'tk_co_data',
            'created',
            'updated'
        ]

    def get_phieu_nhap_hang_ban_tra_lai_data(self, obj):
        """
        Get phieu_nhap_hang_ban_tra_lai data.
        """
        if obj.phieu_nhap_hang_ban_tra_lai:
            return {
                'uuid': obj.phieu_nhap_hang_ban_tra_lai.uuid,
                'so_ct': obj.phieu_nhap_hang_ban_tra_lai.so_ct,
                'ngay_ct': obj.phieu_nhap_hang_ban_tra_lai.ngay_ct
            }
        return None

    def get_ma_vt_data(self, obj):
        """
        Get ma_vt data.
        """
        if obj.ma_vt:
            return VatTuSerializer(obj.ma_vt).data
        return None

    def get_dvt_data(self, obj):
        """
        Get dvt data.
        """
        if obj.dvt:
            return DonViTinhSerializer(obj.dvt).data
        return None

    def get_ma_kho_data(self, obj):
        """
        Get ma_kho data.
        """
        if obj.ma_kho:
            return KhoHangModelSerializer(obj.ma_kho).data
        return None

    def get_ma_vi_tri_data(self, obj):
        """
        Get ma_vi_tri data.
        """
        if obj.ma_vi_tri:
            return ViTriModelSerializer(obj.ma_vi_tri).data
        return None

    def get_ma_lo_data(self, obj):
        """
        Get ma_lo data.
        """
        if obj.ma_lo:
            return LoModelSerializer(obj.ma_lo).data
        return None

    def get_tk_no_data(self, obj):
        """
        Get tk_no data.
        """
        if obj.tk_no:
            return AccountModelSerializer(obj.tk_no).data
        return None

    def get_tk_co_data(self, obj):
        """
        Get tk_co data.
        """
        if obj.tk_co:
            return AccountModelSerializer(obj.tk_co).data
        return None


class ChiTietPhieuNhapHangBanTraLaiModelCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating ChiTietPhieuNhapHangBanTraLai model.
    """
    class Meta:
        model = ChiTietPhieuNhapHangBanTraLaiModel
        fields = [
            'phieu_nhap_hang_ban_tra_lai',
            'line',
            'ma_vt',
            'ten_vt',
            'dvt',
            'so_luong',
            'don_gia',
            'tien',
            'ty_le_ck',
            'tien_ck',
            'ty_le_thue',
            'tien_thue',
            'tien_tt',
            'ma_kho',
            'ma_vi_tri',
            'ma_lo',
            'tk_no',
            'tk_co'
        ]
