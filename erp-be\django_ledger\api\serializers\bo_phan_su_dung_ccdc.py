"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for BoPhanSuDungCCDC (Department for Tool and Equipment Usage) model
"""

from rest_framework import serializers

from django_ledger.models import BoPhanSuDungCCDCModel, BoPhanModel
from django_ledger.api.serializers.organization import BoPhanModelSerializer


class BoPhanSuDungCCDCModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the BoPhanSuDungCCDCModel (Department for Tool and Equipment Usage) model.

    This serializer handles the conversion between BoPhanSuDungCCDCModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - ma_bp: Department code
    - ten_bp: Primary name of the department
    - ten_bp2: Secondary/alternative name (optional)
    - ma_bp_phi: Reference to the related fee department (ForeignKey to BoPhanModel)
    - ma_bp_phi_data: Full data of the related fee department (nested BoPhanModelSerializer)
    - ghi_chu: Notes or description for the department (optional)
    - status: Status indicator ('0'=inactive, '1'=active)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    """
    ma_bp_phi_name = serializers.SerializerMethodField()
    ma_bp_phi_data = serializers.SerializerMethodField()

    class Meta:
        model = BoPhanSuDungCCDCModel
        fields = ['uuid', 'ma_bp', 'ten_bp', 'ten_bp2', 'ma_bp_phi', 'ma_bp_phi_name', 'ma_bp_phi_data',
                  'ghi_chu', 'status', 'entity_model',
                  'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated', 'ma_bp_phi_name', 'ma_bp_phi_data', 'entity_model']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_bp": "CCKT",
                "ten_bp": "Bộ phận sử dụng công cụ dụng cụ kỹ thuật",
                "ten_bp2": "Technical Tool and Equipment Usage Department",
                "ma_bp_phi": "123e4567-e89b-12d3-a456-************",
                "ma_bp_phi_name": "Kế toán",
                "ma_bp_phi_data": {
                    "uuid": "123e4567-e89b-12d3-a456-************",
                    "ma_bp": "KT",
                    "ten_bp": "Kế toán",
                    "ten_bp2": "Accounting",
                    "ghi_chu": "Bộ phận kế toán của công ty",
                    "status": "1",
                    "entity_model": "123e4567-e89b-12d3-a456-************",
                    "created": "2023-04-21T10:30:00Z",
                    "updated": "2023-04-21T10:30:00Z"
                },
                "ghi_chu": "Bộ phận sử dụng công cụ dụng cụ kỹ thuật của công ty",
                "status": "1",
                "entity_model": "123e4567-e89b-12d3-a456-************",
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z"
            }
        }

    def get_ma_bp_phi_name(self, obj):
        """
        Get the name of the related fee department.

        Parameters
        ----------
        obj : BoPhanSuDungCCDCModel
            The department instance

        Returns
        -------
        str or None
            The name of the related fee department, or None if not set
        """
        if obj.ma_bp_phi:
            return obj.ma_bp_phi.ten_bp
        return None

    def get_ma_bp_phi_data(self, obj):
        """
        Get the full data of the related fee department.

        Parameters
        ----------
        obj : BoPhanSuDungCCDCModel
            The department instance

        Returns
        -------
        dict or None
            The serialized data of the related fee department, or None if not set
        """
        if obj.ma_bp_phi:
            return BoPhanModelSerializer(obj.ma_bp_phi).data
        return None
