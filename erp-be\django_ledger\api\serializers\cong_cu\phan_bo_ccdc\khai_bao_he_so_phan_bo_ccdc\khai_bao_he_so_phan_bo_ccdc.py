"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

KhaiBaoHeSoPhanBoCCDC (CCDC Allocation Coefficient Declaration) serializer implementation.
"""

from rest_framework import serializers

# Import serializers for foreign key fields
from django_ledger.api.serializers.bo_phan_su_dung_ccdc import BoPhanSuDungCCDCModelSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.danh_muc.ke_toan.phi.phi import PhiSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.models.cong_cu.phan_bo_ccdc.khai_bao_he_so_phan_bo_ccdc import KhaiBaoHeSoPhanBoCCDCModel
from django_ledger.api.serializers.account import AccountModelSerializer


class KhaiBaoHeSoPhanBoCCDCModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the KhaiBaoHeSoPhanBoCCDCModel.

    This serializer handles the conversion between KhaiBaoHeSoPhanBoCCDCModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields
    - Adds additional fields with "_data" suffix that contain the complete nested object data
    - When deserializing, accepts UUIDs for reference fields
    - Supports filtering by ky (period) and nam (year)
    """
    # Add nested serializers for foreign key fields
    tk_kh_data = AccountModelSerializer(source='tk_kh', read_only=True)
    tk_cp_data = AccountModelSerializer(source='tk_cp', read_only=True)
    
    # For optional foreign key fields, we'll use SerializerMethodField to handle None values
    ma_bp_cc_data = serializers.SerializerMethodField()
    ma_bp_data = serializers.SerializerMethodField()
    ma_vv_data = serializers.SerializerMethodField()
    ma_phi_data = serializers.SerializerMethodField()
    ma_hd_data = serializers.SerializerMethodField()

    class Meta:
        model = KhaiBaoHeSoPhanBoCCDCModel
        fields = [
            'uuid',
            'entity_model',
            'ky',
            'nam',
            'ma_cc',
            'tk_kh',
            'tk_kh_data',
            'tk_cp',
            'tk_cp_data',
            'ma_bp_cc',
            'ma_bp_cc_data',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_phi',
            'ma_phi_data',
            'ma_hd',
            'ma_hd_data',
            'he_so',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',  # Auto-set from entity_slug in URL
            'ky',  # Used for filtering only, not for CRUD operations
            'nam',  # Used for filtering only, not for CRUD operations
            'tk_kh_data',
            'tk_cp_data',
            'ma_bp_cc_data',
            'ma_bp_data',
            'ma_vv_data',
            'ma_phi_data',
            'ma_hd_data',
            'created',
            'updated'
        ]

    def get_ma_bp_cc_data(self, obj):
        """
        Returns the ma_bp_cc data for the ma_bp_cc field.
        """
        if obj.ma_bp_cc:
            return BoPhanSuDungCCDCModelSerializer(obj.ma_bp_cc).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Returns the ma_bp data for the ma_bp field.
        """
        if obj.ma_bp:
            return  BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):
        """
        Returns the ma_vv data for the ma_vv field.
        """
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_phi_data(self, obj):
        """
        Returns the ma_phi data for the ma_phi field.
        """
        if obj.ma_phi:
            return PhiSerializer(obj.ma_phi).data
        return None

    def get_ma_hd_data(self, obj):
        """
        Returns the ma_hd data for the ma_hd field.
        """
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def validate(self, data):
        """
        Validate the data before creating or updating.
        Note: ky and nam are read-only fields used for filtering only.
        """
        # Validate he_so (coefficient)
        if 'he_so' in data and data['he_so'] < 0:
            raise serializers.ValidationError("Hệ số phải lớn hơn hoặc bằng 0")

        return data
