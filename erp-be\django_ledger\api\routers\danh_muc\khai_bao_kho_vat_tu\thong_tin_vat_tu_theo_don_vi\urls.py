"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Thong Tin Vat Tu Theo Don Vi (Product Information by Unit) module.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.danh_muc.khai_bao_kho_vat_tu.thong_tin_vat_tu_theo_don_vi import ThongTinVatTuTheoDonViViewSet

# Main router for ThongTinVatTuTheoDonVi
router = DefaultRouter()
router.register('', ThongTinVatTuTheoDonViViewSet, basename='thong-tin-vat-tu-theo-don-vi')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
