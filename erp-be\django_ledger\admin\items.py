from django.contrib import admin
from django_ledger.models import UnitOfMeasureModel, ItemModel

class UnitOfMeasureModelAdmin(admin.ModelAdmin):
    list_display = ['name', 'unit_abbr', 'is_active']
    search_fields = ['name', 'unit_abbr']
    list_filter = ['is_active']

class ItemModelAdmin(admin.ModelAdmin):
    list_display = ['name', 'item_role', 'item_type', 'uom', 'is_active']  # Changed 'active' to 'is_active'
    search_fields = ['name']
    list_filter = ['item_role', 'item_type', 'is_active']  # Changed 'active' to 'is_active'
