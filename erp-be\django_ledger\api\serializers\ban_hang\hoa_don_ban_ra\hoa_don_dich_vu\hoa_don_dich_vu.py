"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for HoaDonDichVu model.
"""

from django.db import transaction
from django_ledger.api.serializers.ban_hang.hoa_don_ban_ra.hoa_don_dich_vu.chi_tiet_hoa_don import (
    ChiTietHoaDonNestedSerializer,
)
from django_ledger.api.serializers.ban_hang.hoa_don_ban_ra.hoa_don_dich_vu.thong_tin_thanh_toan_nested import (
    ThongTinThanhToanHoaDonDichVuNestedSerializer,
)
from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_dich_vu import (
    HoaDonDichVuModel,
)
from django_ledger.services.ban_hang.hoa_don_ban_ra import HoaDonDichVuService
from rest_framework import serializers


class HoaDonDichVuSerializer(serializers.ModelSerializer):
    """
    Serializer for HoaDonDichVuModel.
    """

    chi_tiet = ChiTietHoaDonNestedSerializer(many=True, required=False)
    thong_tin_thanh_toan = ThongTinThanhToanHoaDonDichVuNestedSerializer(
        many=True, read_only=True
    )

    # Reference data fields (only for ForeignKey fields)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    ma_httt_data = serializers.SerializerMethodField(read_only=True)
    tk_data = serializers.SerializerMethodField(read_only=True)
    ma_nvbh_data = serializers.SerializerMethodField(read_only=True)
    ma_nk_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)
    ma_tt_data = serializers.SerializerMethodField(read_only=True)
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    so_ct_data = serializers.SerializerMethodField(read_only=True)
    ma_pttt_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = HoaDonDichVuModel
        fields = [
            "uuid",
            "ma_kh",
            "ma_kh_data",
            "ma_so_thue",
            "pt_tao_yn",
            "ma_httt",
            "ma_httt_data",
            "ten_kh_thue",
            "dia_chi",
            "ong_ba",
            "ma_nvbh",
            "ma_nvbh_data",
            "e_mail",
            "tk",
            "tk_data",
            "ma_tt",
            "ma_tt_data",
            "dien_giai",
            "ma_ngv",
            "unit_id",
            "unit_id_data",
            "i_so_ct",
            "ma_nk",
            "ma_nk_data",
            "so_ct",
            "so_ct_data",
            "ngay_ct",
            "ngay_lct",
            "so_ct2",
            "ma_nt",
            "ma_nt_data",
            "ty_gia",
            "status",
            "transfer_yn",
            "ma_kh9",
            "ly_do_huy",
            "ly_do",
            "ten_vt_thue",
            "ghi_chu",
            "ma_tthddt",
            "ma_pttt",
            "ma_pttt_data",
            "so_ct_hddt",
            "ngay_ct_hddt",
            "so_ct2_hddt",
            "ma_mau_ct_hddt",
            "t_tien_nt2",
            "t_tien2",
            "t_thue_nt",
            "t_thue",
            "t_ck_nt",
            "t_ck",
            "t_tt_nt",
            "t_tt",
            "chi_tiet",
            "thong_tin_thanh_toan",
            "created",
            "updated",
        ]
        read_only_fields = ["uuid", "created", "updated"]
        swagger_schema_fields = {
            "title": "HoaDonDichVu",
            "description": "Hóa đơn dịch vụ model serializer",
        }

    def get_ma_kh_data(self, obj):
        """Return customer data if available"""
        if obj.ma_kh:
            return {
                "uuid": str(obj.ma_kh.uuid),
                "customer_code": obj.ma_kh.customer_code,
                "customer_name": obj.ma_kh.customer_name,
            }
        return None

    def get_ma_httt_data(self, obj):
        """Return payment method data if available"""
        if obj.ma_httt:
            return {
                "uuid": str(obj.ma_httt.uuid),
                "ma_httt": obj.ma_httt.ma_httt,
                "ten_httt": obj.ma_httt.ten_httt,
            }
        return None

    def get_tk_data(self, obj):
        """Return account data if available"""
        if obj.tk:
            return {
                "uuid": str(obj.tk.uuid),
                "code": obj.tk.code,
                "name": obj.tk.name,
            }
        return None

    def get_ma_nvbh_data(self, obj):
        """Return sales staff data if available"""
        if obj.ma_nvbh:
            return {
                "uuid": str(obj.ma_nvbh.uuid),
                "ma_nv": obj.ma_nvbh.ma_nhan_vien,
                "ten_nv": obj.ma_nvbh.ho_ten_nhan_vien,
            }
        return None

    def get_ma_nk_data(self, obj):
        """Return document number data if available"""
        if obj.ma_nk:
            return {
                "uuid": str(obj.ma_nk.uuid),
                "ma_quyen": obj.ma_nk.ma_quyen,
                "ten_quyen": obj.ma_nk.ten_quyen,
            }
        return None

    def get_ma_nt_data(self, obj):
        """Return currency data if available"""
        if obj.ma_nt:
            return {
                "uuid": str(obj.ma_nt.uuid),
                "ma_nt": obj.ma_nt.ma_nt,
                "ten_nt": obj.ma_nt.ten_nt,
            }
        return None

    def get_ma_tt_data(self, obj):
        """Return payment term data if available"""
        if obj.ma_tt:
            return {
                "uuid": str(obj.ma_tt.uuid),
                "ma_htt": obj.ma_tt.ma_htt,
                "ten_htt": obj.ma_tt.ten_htt,
            }
        return None

    def get_unit_id_data(self, obj):
        """Return entity unit data if available"""
        if obj.unit_id:
            return {
                "uuid": str(obj.unit_id.uuid),
                "name": obj.unit_id.name,
                "slug": obj.unit_id.slug,
            }
        return None

    def get_so_ct_data(self, obj):
        """Return document data if available"""
        if obj.so_ct:
            return {
                "uuid": str(obj.so_ct.uuid),
                "so_ct": obj.so_ct.so_ct,
                "ten_ct": obj.so_ct.ten_ct,
            }
        return None

    def get_ma_pttt_data(self, obj):
        """Return payment method data if available"""
        if obj.ma_pttt:
            return {
                "uuid": str(obj.ma_pttt.uuid),
                "ma_pttt": obj.ma_pttt.ma_pttt,
                "ten_pttt": obj.ma_pttt.ten_pttt,
            }
        return None

    def create(self, validated_data):
        """
        Create a new HoaDonDichVuModel with nested ChiTietHoaDonModel instances.
        """
        entity_slug = self.context["entity_slug"]
        user_model = self.context["request"].user

        service = HoaDonDichVuService(entity_slug=entity_slug, user_model=user_model)

        # Let the service handle both the main invoice and its detail items
        with transaction.atomic():
            hoa_don = service.create(data=validated_data)

        return hoa_don

    def update(self, instance, validated_data):
        """
        Update an existing HoaDonDichVuModel with nested ChiTietHoaDonModel instances.
        """
        entity_slug = self.context["entity_slug"]
        user_model = self.context["request"].user

        service = HoaDonDichVuService(entity_slug=entity_slug, user_model=user_model)

        with transaction.atomic():
            # Let the service handle both the main invoice and its detail items
            instance = service.update(uuid=instance.uuid, data=validated_data)

        return instance
