"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for DieuChinhBoPhanSuDungCCDC model.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from django_ledger.models import DieuChinhBoPhanSuDungCCDCModel
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.bo_phan_su_dung_ccdc import BoPhanSuDungCCDCModelSerializer
from django_ledger.api.serializers.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc import KhaiBaoThongTinCCDCModelSerializer


class DieuChinhBoPhanSuDungCCDCModelSerializer(serializers.ModelSerializer):
    """
    Serializer for DieuChinhBoPhanSuDungCCDCModel.

    This serializer handles the serialization and deserialization of DieuChinhBoPhanSuDungCCDCModel instances.
    It includes nested serializers for foreign key relationships to provide complete object data.

    Key features:
    - Maintains original UUID references for foreign key fields (entity_model, ma_cc, ma_bp, tk_cc, tk_kh, tk_cp)
    - Adds additional fields with "_data" suffix (ma_cc_data, ma_bp_data, tk_cc_data, tk_kh_data, tk_cp_data)
      that contain the complete nested object data with all available fields
    - When deserializing, accepts UUIDs for reference fields
    """
    # Read-only fields for related objects
    ma_cc_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    tk_cc_data = serializers.SerializerMethodField(read_only=True)
    tk_kh_data = serializers.SerializerMethodField(read_only=True)
    tk_cp_data = serializers.SerializerMethodField(read_only=True)

    # Override foreign key fields to make them not required
    ma_cc = serializers.UUIDField(required=False, allow_null=True)
    ma_bp = serializers.UUIDField(required=False, allow_null=True)
    tk_cc = serializers.UUIDField(required=False, allow_null=True)
    tk_kh = serializers.UUIDField(required=False, allow_null=True)
    tk_cp = serializers.UUIDField(required=False, allow_null=True)

    class Meta:
        model = DieuChinhBoPhanSuDungCCDCModel
        fields = [
            'uuid',
            'entity_model',
            'ky',
            'nam',
            'ma_cc',
            'ma_cc_data',
            'ma_bp',
            'ma_bp_data',
            'tk_cc',
            'tk_cc_data',
            'tk_kh',
            'tk_kh_data',
            'tk_cp',
            'tk_cp_data',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'ma_cc_data',
            'ma_bp_data',
            'tk_cc_data',
            'tk_kh_data',
            'tk_cp_data',
            'created',
            'updated'
        ]

    def get_ma_cc_data(self, obj):
        """
        Get tool information declaration data.
        """
        if obj.ma_cc:
            return KhaiBaoThongTinCCDCModelSerializer(obj.ma_cc).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Get department usage data.
        """
        if obj.ma_bp:
            return BoPhanSuDungCCDCModelSerializer(obj.ma_bp).data
        return None

    def get_tk_cc_data(self, obj):
        """
        Get cost center account data.
        """
        if obj.tk_cc:
            return AccountModelSerializer(obj.tk_cc).data
        return None

    def get_tk_kh_data(self, obj):
        """
        Get customer account data.
        """
        if obj.tk_kh:
            return AccountModelSerializer(obj.tk_kh).data
        return None

    def get_tk_cp_data(self, obj):
        """
        Get expense account data.
        """
        if obj.tk_cp:
            return AccountModelSerializer(obj.tk_cp).data
        return None

    def validate_ky(self, value):
        """
        Validate accounting period
        """
        if value is not None and (value < 1 or value > 12):
            raise serializers.ValidationError(_("Kỳ kế toán phải từ 1 đến 12"))
        return value

    def validate_nam(self, value):
        """
        Validate accounting year
        """
        if value is not None and (value < 1900 or value > 2100):
            raise serializers.ValidationError(_("Năm kế toán không hợp lệ"))
        return value

    def validate(self, attrs):
        """
        Validate the serializer data.
        """
        # Check if this is an update or create operation
        is_update = self.instance is not None

        # Validate required fields for create operations
        if not is_update:
            required_fields = ['ky', 'nam']
            for field in required_fields:
                if field not in attrs or attrs[field] is None:
                    raise serializers.ValidationError({
                        field: _('This field is required.')
                    })

        # Check if both ky and nam are provided when one is provided
        if attrs.get('ky') and not attrs.get('nam'):
            raise serializers.ValidationError({
                'nam': _("Năm kế toán là bắt buộc khi có kỳ kế toán")
            })

        if attrs.get('nam') and not attrs.get('ky'):
            raise serializers.ValidationError({
                'ky': _("Kỳ kế toán là bắt buộc khi có năm kế toán")
            })

        # Validate status value if provided
        if 'status' in attrs and attrs['status'] not in ['0', '1']:
            raise serializers.ValidationError({
                'status': _('Invalid status value. Must be "0" or "1".')
            })

        return attrs


