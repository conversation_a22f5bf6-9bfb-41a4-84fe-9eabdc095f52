"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for ButToanHuy API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.tong_hop.but_toan_cuoi_ky.but_toan_huy import (
    ButToanHuyViewSet,
    ChiTietButToanHuyViewSet,
    ThueButToanHuyViewSet
)

# Main router for ButToanHuy
router = DefaultRouter()
router.register('', ButToanHuyViewSet, basename='but-toan-huy')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),

    # Nested routes for but-toan-huy
    path('<uuid:but_toan_huy_uuid>/', include([
        # Chi tiet but toan huy routes
        path('chi-tiet-but-toan-huy/', ChiTietButToanHuyViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-tiet-but-toan-huy-list'),

        path('chi-tiet-but-toan-huy/<uuid:uuid>/', ChiTietButToanHuyViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-tiet-but-toan-huy-detail'),

        # Thue but toan huy routes
        path('thue-but-toan-huy/', ThueButToanHuyViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='thue-but-toan-huy-list'),

        path('thue-but-toan-huy/<uuid:uuid>/', ThueButToanHuyViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='thue-but-toan-huy-detail'),
    ])),
]
