"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for GiaThanhCacBuoc model.
"""

from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models import GiaThanhCacBuocModel
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.warehouse import KhoHangModelSerializer
from django_ledger.api.serializers.group import GroupModelSerializer


class GiaThanhCacBuocSerializer(serializers.ModelSerializer):
    """
    Serializer for GiaThanhCacBuoc model.
    """
    # Read-only fields for related objects
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_kho_data = serializers.SerializerMethodField(read_only=True)
    ma_nh_data = serializers.SerializerMethodField(read_only=True)

    # JSON field data following Ye<PERSON><PERSON>o pattern
    ma_yt_data = serializers.SerializerMethodField(read_only=True)
    loai_yt_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = GiaThanhCacBuocModel
        fields = [
            'uuid',
            'entity_model',
            'xcode',
            'xline',
            'xstored',
            'ma_bp',
            'ma_bp_data',
            'ma_kho',
            'ma_kho_data',
            'ma_nh',
            'ma_nh_data',
            'ma_yt',
            'ma_yt_data',
            'loai_yt',
            'loai_yt_data',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'ma_bp_data',
            'ma_kho_data',
            'ma_nh_data',
            'ma_yt_data',
            'loai_yt_data',
            'created',
            'updated'
        ]

    def get_ma_bp_data(self, obj):
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_kho_data(self, obj):
        """
        Get warehouse data.
        """
        if obj.ma_kho:
            return KhoHangModelSerializer(obj.ma_kho).data
        return None

    def get_ma_nh_data(self, obj):
        """
        Get group data.
        """
        if obj.ma_nh:
            return GroupModelSerializer(obj.ma_nh).data
        return None

    def get_ma_yt_data(self, obj):
        """
        Get factor data following YeuTo pattern.
        """
        if hasattr(obj, 'ma_yt_data'):
            return [
                {
                    'uuid': str(factor.uuid),
                    'ma_yt': factor.ma_yt
                } if factor else "notfound"
                for factor in obj.ma_yt_data
            ]
        return []

    def get_loai_yt_data(self, obj):
        """
        Get factor type data following YeuTo pattern.
        """
        if hasattr(obj, 'loai_yt_data'):
            return [
                {
                    'uuid': str(factor_type.uuid),
                    'ma_loai': factor_type.ma_loai
                } if factor_type else "notfound"
                for factor_type in obj.loai_yt_data
            ]
        return []

    def to_representation(self, instance):
        """
        Override the default representation to handle UUID serialization.
        """
        representation = super().to_representation(instance)

        # Convert JSON fields to lists of strings following YeuTo pattern
        json_fields = ['ma_yt', 'loai_yt']
        for field in json_fields:
            if field in representation and representation[field] is not None:
                if not isinstance(representation[field], list):
                    representation[field] = [str(representation[field])] if representation[field] else []
                else:
                    representation[field] = [str(item) if item is not None else '' for item in representation[field]]

        # Convert other UUID fields to strings following YeuTo pattern
        uuid_fields = ['unit_id', 'ma_bp', 'ma_kho', 'ma_nh']
        for field in uuid_fields:
            if field in representation and representation[field] is not None:
                representation[field] = str(representation[field])

        return representation
