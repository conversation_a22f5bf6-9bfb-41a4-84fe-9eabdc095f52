"""
Django <PERSON>ger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Base Serializers Module
"""

from rest_framework import serializers


class GlobalModelSerializerMixin:
    """
    A mixin for serializers that handle global models (models not directly tied to an entity).
    This mixin removes the entity_model field from validated_data during creation,
    which is necessary when using EntityRelatedViewSet with models that don't have an entity_model field.
    """

    def create(self, validated_data):
        """
        Remove entity_model from validated_data if it exists.
        This is needed because EntityRelatedViewSet adds entity_model to validated_data,
        but global models don't have this field.
        
        Parameters
        ----------
        validated_data : dict
            The validated data from the serializer
            
        Returns
        -------
        Model instance
            The created model instance
        """
        if 'entity_model' in validated_data:
            validated_data.pop('entity_model')
        return super().create(validated_data)


class BaseModelSerializer(serializers.ModelSerializer):
    """
    Base serializer class for all models in the system.
    Provides common functionality and fields.
    """
    
    class Meta:
        fields = ['uuid', 'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated']


class GlobalModelSerializer(GlobalModelSerializerMixin, BaseModelSerializer):
    """
    Base serializer for global models (models not directly tied to an entity).
    Combines GlobalModelSerializerMixin and BaseModelSerializer.
    """
    pass
