from django_ledger.models import UserProfileModel
from rest_framework import serializers


class UserProfileSerializer(serializers.ModelSerializer):
    entity_slug = serializers.CharField(read_only=True)
    department_code_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = UserProfileModel
        fields = [
            "uuid",
            "nickname",
            "phone",
            "birthday",
            "sex",
            "address",
            "department_code",
            "department_code_data",
            "user_ref",
            "language",
            "status",
            "image",
            "pq_enabled",
            "entity_slug",
        ]
        read_only_fields = ["uuid", "entity_slug", "department_code_data"]

    def get_department_code_data(self, obj):
        """
        Get department code data.
        """
        if obj.department_code:
            from django_ledger.api.serializers.organization import BoPhanModelSerializer

            return BoPhanModelSerializer(obj.department_code).data
        return None
