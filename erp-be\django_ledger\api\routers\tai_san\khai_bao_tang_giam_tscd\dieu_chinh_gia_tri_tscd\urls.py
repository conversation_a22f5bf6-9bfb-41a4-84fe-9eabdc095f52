"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for DieuChinhGiaTriTSCD (Fixed Asset Value Adjustment) module.
"""

from django.urls import path

from django_ledger.api.views.tai_san.khai_bao_tang_giam_tscd.dieu_chinh_gia_tri_tscd import DieuChinhGiaTriTSCDViewSet

# DieuChinhGiaTriTSCD routes
urlpatterns = [
    # DieuChinhGiaTriTSCD routes
    path('', DieuChinhGiaTriTSCDViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='dieu-chinh-gia-tri-tscd-list'),

    # Filter endpoint
    path('filter/', DieuChinhGiaTriTSCDViewSet.as_view({
        'get': 'filter_records'
    }), name='dieu-chinh-gia-tri-tscd-filter'),

    # Search endpoint
    path('search/', DieuChinhGiaTriTSCDViewSet.as_view({
        'get': 'search_records'
    }), name='dieu-chinh-gia-tri-tscd-search'),

    path('<uuid:pk>/', DieuChinhGiaTriTSCDViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='dieu-chinh-gia-tri-tscd-detail'),
]
