"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for LoaiTaiSanCongCu model.
"""

from rest_framework import serializers

from django_ledger.api.serializers.base import GlobalModelSerializer
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.danh_muc.ke_toan.phi import PhiSerializer
from django_ledger.models.danh_muc.tai_san_va_cong_cu.loai_tai_san_cong_cu import LoaiTaiSanCongCuModel
from django_ledger.models import AccountModel, PhiModel


class LoaiTaiSanCongCuModelSerializer(GlobalModelSerializer):
    """
    Serializer for LoaiTaiSanCongCuModel.
    """

    # Define method fields for nested data
    tk_ts_data = serializers.SerializerMethodField(read_only=True)
    tk_kh_data = serializers.SerializerMethodField(read_only=True)
    tk_cp_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = LoaiTaiSanCongCuModel
        fields = [
            "uuid",
            "entity_model",
            "loai_tscc",
            "ma_lts",
            "ten_lts",
            "ten_lts2",
            "tk_ts",
            "tk_ts_data",
            "tk_kh",
            "tk_kh_data",
            "tk_cp",
            "tk_cp_data",
            "ma_phi",
            "ma_phi_data",
            "status",
            "created",
            "updated",
        ]
        read_only_fields = ["uuid", "created", "updated", "entity_model"]
        swagger_schema_fields = {
            "title": "LoaiTaiSanCongCu",
            "description": "Loại tài sản công cụ model serializer",
        }

    def validate_ma_lts(self, value):
        """
        Validate ma_lts field

        Parameters
        ----------
        value : str
            The ma_lts value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError("Mã loại tài sản không được để trống")
        return value.strip()

    def validate_ten_lts(self, value):
        """
        Validate ten_lts field

        Parameters
        ----------
        value : str
            The ten_lts value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError("Tên loại tài sản không được để trống")
        return value.strip()

    def validate_tk_ts(self, value):
        """
        Validate tk_ts field

        Parameters
        ----------
        value : AccountModel
            The tk_ts value to validate

        Returns
        -------
        AccountModel
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError("Tài khoản tài sản không được để trống")
        return value

    def validate_tk_kh(self, value):
        """
        Validate tk_kh field

        Parameters
        ----------
        value : AccountModel
            The tk_kh value to validate

        Returns
        -------
        AccountModel
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError("Tài khoản khấu hao không được để trống")
        return value

    def validate_tk_cp(self, value):
        """
        Validate tk_cp field

        Parameters
        ----------
        value : AccountModel
            The tk_cp value to validate

        Returns
        -------
        AccountModel
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError("Tài khoản chi phí không được để trống")
        return value

    def get_tk_ts_data(self, obj):
        """
        Method field for tk_ts_data

        Returns the serialized data for the related AccountModel
        """
        if obj.tk_ts:
           return AccountModelSerializer(obj.tk_ts).data
        return None

    def get_tk_kh_data(self, obj):
        """
        Method field for tk_kh_data

        Returns the serialized data for the related AccountModel
        """
        if obj.tk_kh:
           return AccountModelSerializer(obj.tk_kh).data
        return None

    def get_tk_cp_data(self, obj):
        """
        Method field for tk_cp_data

        Returns the serialized data for the related AccountModel
        """
        if obj.tk_cp:
           return AccountModelSerializer(obj.tk_cp).data
        return None

    def get_ma_phi_data(self, obj):
        """
        Method field for ma_phi_data

        Returns the serialized data for the related PhiModel
        """
        if obj.ma_phi:
           return PhiSerializer(obj.ma_phi).data
        return None
