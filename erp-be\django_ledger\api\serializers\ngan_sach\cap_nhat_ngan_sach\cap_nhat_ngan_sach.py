"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for CapNhatNganSach model.
"""
from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models import CapNhatNganSachModel, EntityModel
from django_ledger.api.serializers.unit import EntityUnitModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.api.serializers.ngan_sach.chi_tieu_ngan_sach import ChiTieuNganSachModelSerializer
from django_ledger.api.serializers.ngan_sach.cap_nhat_ngan_sach.chi_tiet_cap_nhat_ngan_sach import ChiTietCapNhatNganSachSerializer


class CapNhatNganSachSerializer(serializers.ModelSerializer):
    """
    Serializer for CapNhatNganSach model.
    """
    # Read-only fields for related objects
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_so_data = serializers.SerializerMethodField(read_only=True)

    # Child data
    chi_tiet_data = serializers.SerializerMethodField(read_only=True)
    chi_tiet = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = CapNhatNganSachModel
        fields = [
            'uuid',
            'entity_model',
            'id_maubc',
            'nam',
            'unit_id',
            'unit_id_data',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_so',
            'ma_so_data',
            'status',
            't_tien_nt',
            't_tien',
            'chi_tiet_data',
            'chi_tiet',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'unit_id_data',
            'ma_bp_data',
            'ma_vv_data',
            'ma_so_data',
            'chi_tiet_data',
            'created',
            'updated'
        ]
        extra_kwargs = {
            'ma_bp': {'required': False, 'allow_null': True},
            'ma_vv': {'required': False, 'allow_null': True},
            'ma_so': {'required': False, 'allow_null': True},
            'unit_id': {'required': False, 'allow_null': True}
        }


    def get_unit_id_data(self, obj):
        """
        Get unit data.
        """
        if obj.unit_id:
            return EntityUnitModelSerializer(obj.unit_id).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):
        """
        Get project data.
        """
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_so_data(self, obj):
        """
        Get budget item data.
        """
        if obj.ma_so:
            return ChiTieuNganSachModelSerializer(obj.ma_so).data
        return None

    def get_chi_tiet_data(self, obj):
        """
        Get child details data.
        """

        chi_tiet = obj.chi_tiet.all()
        return ChiTietCapNhatNganSachSerializer(chi_tiet, many=True, context=self.context).data

    def validate(self, attrs):
        """
        Validate the serializer data.
        """
        # Check if this is an update or create operation
        is_update = self.instance is not None
        entity_slug = self.context.get('entity_slug')
        request = self.context.get('request')

        # Set entity_model from context if not provided
        if not is_update and entity_slug:
            try:
                entity_model = EntityModel.objects.get(slug=entity_slug)
                attrs['entity_model'] = entity_model
            except EntityModel.DoesNotExist:
                raise serializers.ValidationError({
                    'entity_slug': _('Entity with the provided slug does not exist.')
                })

        # Validate status value if provided
        if 'status' in attrs and attrs['status'] not in ['0', '1']:
            raise serializers.ValidationError({
                'status': _('Invalid status value. Must be "0" or "1".')
            })

        # For create operation, validate required fields
        if not is_update:
            required_fields = ['id_maubc', 'nam']
            for field in required_fields:
                if field not in attrs:
                    raise serializers.ValidationError({
                        field: _('This field is required.')
                    })

        # Set default values for optional fields if not provided
        attrs.setdefault('ma_bp', None)
        attrs.setdefault('ma_vv', None)
        attrs.setdefault('ma_so', None)
        attrs.setdefault('unit_id', None)

        return attrs

