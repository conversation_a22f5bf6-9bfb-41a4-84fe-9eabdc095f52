from rest_framework import serializers

from django_ledger.models.color import ColorModel
from django_ledger.api.serializers.base import GlobalModelSerializer


class ColorModelSerializer(GlobalModelSerializer):
    """
    Serializer for the ColorModel
    """
    class Meta:
        model = ColorModel
        fields = [
            'uuid',
            'entity_model',
            'ma_mau_sac',
            'ten_mau_sac',
            'ten_mau_sac2',
            'color',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated']

    def validate_color(self, value):
        """
        Validate that the color is a valid HEX color code
        """
        if not value.startswith('#') or len(value) != 7:
            raise serializers.ValidationError('Invalid HEX color code. Must be in format #RRGGBB')
        try:
            int(value[1:], 16)
        except ValueError:
            raise serializers.ValidationError('Invalid HEX color code. Must contain valid hexadecimal characters')
        return value
