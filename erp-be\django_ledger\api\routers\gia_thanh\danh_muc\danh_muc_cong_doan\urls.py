"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the URL patterns for the DanhMucCongDoan API.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.gia_thanh.danh_muc.danh_muc_cong_doan import DanhMucCongDoanViewSet

# Main router for DanhMucCongDoan
router = DefaultRouter()
router.register('danh-muc-cong-doan', DanhMucCongDoanViewSet, basename='danh-muc-cong-doan')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
