"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for the ViTri (Location) model.
"""

from rest_framework import serializers
from django_ledger.models.vi_tri import ViTriModel


class ViTriModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the ViTriModel.
    """
    class Meta:
        model = ViTriModel
        fields = [
            'uuid',
            'ma_vi_tri',
            'ten_vi_tri',
            'kho',
            'tang',
            'day',
            'stt',
            'suc_chua',
            'loai_hang',
            'trang_thai',
            'ghi_chu',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated']
        swagger_schema_fields = {
            'title': 'Vị trí',
            'description': 'Vị trí trong kho'
        }
