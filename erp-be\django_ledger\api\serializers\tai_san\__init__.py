"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Tai San (Fixed Assets) serializers package initialization.
"""

from django_ledger.api.serializers.tai_san.dieu_chuyen_tscd.dieu_chuyen_bo_phan_su_dung_tscd import (
    DieuChuyenBoPhanSuDungTSCDModelSerializer,
)
from django_ledger.api.serializers.tai_san.dung_khau_hao_tscd import (
    KhaiBaoTamDungKhauHaoTSCDSerializer,
    KhaiBaoThoiKhauHaoTSCDSerializer,
)
from django_ledger.api.serializers.tai_san.khai_bao_tang_giam_tscd import (
    ChiTietDoiTuongHachToanTSCDSerializer,
    ChiTietPhuTungKemTheoTSCDSerializer,
    KhaiBaoThongTinTaiSanCoDinhSerializer,
)
from django_ledger.api.serializers.tai_san.kiem_ke_tscd import (
    TheTSCDRequestSerializer,
    TheTSCDResponseSerializer,
)
from django_ledger.api.serializers.tai_san.tinh_khau_hao_tscd import (
    KhaiBaoHeSoPhanBoTSCDSerializer,
)

__all__ = [
    "KhaiBaoThongTinTaiSanCoDinhSerializer",
    "ChiTietDoiTuongHachToanTSCDSerializer",
    "ChiTietPhuTungKemTheoTSCDSerializer",
    "KhaiBaoThoiKhauHaoTSCDSerializer",
    "KhaiBaoTamDungKhauHaoTSCDSerializer",
    "KhaiBaoHeSoPhanBoTSCDSerializer",
    "DieuChuyenBoPhanSuDungTSCDModelSerializer",
    "TheTSCDRequestSerializer",
    "TheTSCDResponseSerializer",
]
