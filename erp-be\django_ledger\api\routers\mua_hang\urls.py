"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Mua Hang module.
"""

from django.urls import include, path

# URL patterns for the module
urlpatterns = [
    # Include thanh_toan_tam_ung URLs
    path(
        "thanh-toan-tam-ung/",
        include("django_ledger.api.routers.mua_hang.thanh_toan_tam_ung.urls"),
    ),
    # Include hoa_don_mua_vao URLs
    path(
        "hoa-don-mua-vao/",
        include("django_ledger.api.routers.mua_hang.hoa_don_mua_vao.urls"),
    ),
    # Include hoa_don_dieu_chinh_tra_lai URLs
    path(
        "hoa-don-dieu-chinh-tra-lai/",
        include("django_ledger.api.routers.mua_hang.hoa_don_dieu_chinh_tra_lai.urls"),
    ),
    # Include dieu_chinh_can_tru_cong_no URLs
    path(
        "dieu-chinh-can-tru-cong-no/",
        include("django_ledger.api.routers.mua_hang.dieu_chinh_can_tru_cong_no.urls"),
    ),
    # Include bao_cao_mua_hang URLs
    path(
        "bao-cao-mua-hang/",
        include("django_ledger.api.routers.mua_hang.bao_cao_mua_hang.urls"),
    ),
]
