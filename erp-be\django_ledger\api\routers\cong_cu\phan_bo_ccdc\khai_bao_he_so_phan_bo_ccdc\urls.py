"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

KhaiBaoHeSoPhanBoCCDC (CCDC Allocation Coefficient Declaration) router implementation.
"""

from django.urls import path

from django_ledger.api.views.cong_cu.phan_bo_ccdc.khai_bao_he_so_phan_bo_ccdc import KhaiBaoHeSoPhanBoCCDCViewSet

urlpatterns = [
    # KhaiBaoHeSoPhanBoCCDC routes
    path('', KhaiBaoHeSoPhanBoCCDCViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='khai-bao-he-so-phan-bo-ccdc-list'),

    path('<uuid:pk>/', KhaiBaoHeSoPhanBoCCDCViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='khai-bao-he-so-phan-bo-ccdc-detail'),

    # Custom action for filtering by period
    path('filter-by-period/', KhaiBaoHeSoPhanBoCCDCViewSet.as_view({
        'get': 'filter_by_period'
    }), name='khai-bao-he-so-phan-bo-ccdc-filter-by-period'),
]
