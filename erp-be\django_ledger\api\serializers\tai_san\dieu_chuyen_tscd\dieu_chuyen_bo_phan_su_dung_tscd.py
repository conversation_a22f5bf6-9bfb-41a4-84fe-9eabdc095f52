"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

DieuChuyenBoPhanSuDungTSCD (Fixed Asset Department Transfer) serializer implementation.
"""

from rest_framework import serializers

from django_ledger.models.tai_san.dieu_chuyen_tscd import DieuChuyenBoPhanSuDungTSCDModel

# Import serializers for foreign key fields
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.tai_san.khai_bao_tang_giam_tscd.khai_bao_thong_tin_tai_san_co_dinh import KhaiBaoThongTinTaiSanCoDinhSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer


class DieuChuyenBoPhanSuDungTSCDModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the DieuChuyenBoPhanSuDungTSCDModel.

    This serializer handles the conversion between DieuChuyenBoPhanSuDungTSCDModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields (entity_model, ma_ts, ma_bp, tk_ts, tk_kh, tk_cp)
    - Adds additional fields with "_data" suffix (ma_ts_data, ma_bp_data, tk_ts_data, tk_kh_data, tk_cp_data)
      that contain the complete nested object data with all available fields
    - When deserializing, accepts UUIDs for reference fields
    - The entity_model field is write_only and is set automatically from the URL
    """

    # Reference data fields
    ma_ts_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    tk_ts_data = serializers.SerializerMethodField(read_only=True)
    tk_kh_data = serializers.SerializerMethodField(read_only=True)
    tk_cp_data = serializers.SerializerMethodField(read_only=True)

    # Make entity_model write-only
    entity_model = serializers.PrimaryKeyRelatedField(read_only=True)

    class Meta:
        model = DieuChuyenBoPhanSuDungTSCDModel
        fields = [
            'uuid',
            'entity_model',
            'ma_ts',
            'ma_ts_data',
            'ky',
            'nam',
            'ma_bp',
            'ma_bp_data',
            'tk_ts',
            'tk_ts_data',
            'tk_kh',
            'tk_kh_data',
            'tk_cp',
            'tk_cp_data',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'ky',
            'nam',
            'ma_ts_data',
            'ma_bp_data',
            'tk_ts_data',
            'tk_kh_data',
            'tk_cp_data',
            'entity_model',
            'created',
            'updated'
        ]

    def get_ma_ts_data(self, obj):
        """
        Returns the fixed asset data for the ma_ts field.
        """
        if obj.ma_ts:
            return KhaiBaoThongTinTaiSanCoDinhSerializer(obj.ma_ts).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Returns the department data for the ma_bp field.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_tk_ts_data(self, obj):
        """
        Returns the asset account data for the tk_ts field.
        """
        if obj.tk_ts:
            return AccountModelSerializer(obj.tk_ts).data
        return None

    def get_tk_kh_data(self, obj):
        """
        Returns the depreciation account data for the tk_kh field.
        """
        if obj.tk_kh:
            return AccountModelSerializer(obj.tk_kh).data
        return None

    def get_tk_cp_data(self, obj):
        """
        Returns the expense account data for the tk_cp field.
        """
        if obj.tk_cp:
            return AccountModelSerializer(obj.tk_cp).data
        return None
