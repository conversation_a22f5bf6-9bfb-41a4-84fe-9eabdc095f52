"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for Bao Cao Tinh Trang De <PERSON> (Payment Request Status Report) API endpoints.
"""

from django.urls import path
from django_ledger.api.views.tien_gui.de_nghi_thanh_toan.bao_cao_tinh_trang_de_nghi_chi import BaoCaoTinhTrangDeNghiChiViewSet

# URL patterns - Single endpoint for payment request status report with filters as POST body data
urlpatterns = [
    # Payment Request Status Report endpoint - returns report directly with filter POST body data
    path("", BaoCaoTinhTrangDeNghiChiViewSet.as_view({"post": "get_report"}), name="bao-cao-tinh-trang-de-nghi-chi-report"),
]
