"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

DieuChuyenBoPhanSuDungTSCD (Fixed Asset Department Transfer) router implementation.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.tai_san.dieu_chuyen_tscd import DieuChuyenBoPhanSuDungTSCDViewSet

router = DefaultRouter()
router.register('dieu-chuyen-bo-phan-su-dung-tscd', DieuChuyenBoPhanSuDungTSCDViewSet, basename='dieu-chuyen-bo-phan-su-dung-tscd')

urlpatterns = [
    path('', include(router.urls))
]
