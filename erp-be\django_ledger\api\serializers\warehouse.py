"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Warehouse-related models
"""

from rest_framework import serializers

from django_ledger.models import (
    KhoHangModel, LoaiVatTuModel, VatTuSanPhamDonViTinhModel,
    VatTuSanPhamHangHoaModel, GiaMuaModel, NhomHangModel
)
from django_ledger.api.serializers.unit import EntityUnitModelSerializer


class KhoHangModelSerializer(serializers.ModelSerializer):
    unit_data = serializers.SerializerMethodField(read_only=True)

    def get_unit_data(self, obj):
        """
        Get the entity unit data corresponding to the unit_id field
        """
        if obj.unit_id:
            return EntityUnitModelSerializer(obj.unit_id).data
        return None

    class Meta:
        model = KhoHangModel
        fields = ['uuid', 'unit_id', 'unit_data', 'ma_unit', 'ma_kho', 'ten_kho', 'ten_kho2','entity_model',
                  'vi_tri_yn', 'dai_ly_yn', 'ma_nh', 'dia_chi', 'dien_thoai', 'ghi_chu', 'status',
                  'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated', 'unit_data']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "unit_id": "123e4567-e89b-12d3-a456-************",
                "unit_data": {
                    "uuid": "123e4567-e89b-12d3-a456-************",
                    "name": "Main Unit",
                    "slug": "main-unit",
                    "entity": "123e4567-e89b-12d3-a456-************",
                    "entity_slug": "main-entity",
                    "entity_name": "Main Entity",
                    "document_prefix": "MU",
                    "active": True,
                    "hidden": False,
                    "unit_id": "UNIT01",
                    "ma_unit": "UNIT01",
                    "ten_unit": "Main Unit",
                    "ten_unit2": "Main Unit 2",
                    "ten_unit3": "Main Unit 3",
                    "ma_so_thue": "123456789",
                    "ngay_ks": "2023-01-01",
                    "nh_dv1": "123e4567-e89b-12d3-a456-************",
                    "nh_dv1_data": {
                        "uuid": "123e4567-e89b-12d3-a456-************",
                        "ma_nhom": "NH01",
                        "ten_nhom": "Group 1"
                    },
                    "entity_line11": "Address Line 1",
                    "entity_line12": "Address Line 2",
                    "entity_line21": "City",
                    "entity_line22": "State",
                    "entity_line31": "Country",
                    "entity_line32": "Postal Code",
                    "entity_line41": "Phone",
                    "entity_line42": "Email",
                    "entity_line51": "Website",
                    "entity_line52": "Additional Info",
                    "gia_ton": "1",
                    "bp_yn": True,
                    "lsx_yn": True,
                    "bankAccountName": "Bank Account",
                    "bankName": "Bank Name",
                    "chiefAccountantName": "Chief Accountant",
                    "directorName": "Director",
                    "cashierName": "Cashier",
                    "storeKeeperName": "Store Keeper",
                    "signatureFullname": "Signature",
                    "signatureFullnameSeal": "Signature Seal",
                    "ximage": "image.jpg",
                    "created": "2023-01-01T00:00:00Z",
                    "updated": "2023-01-02T00:00:00Z"
                },
                "ma_unit": "UNIT01",
                "ma_kho": "KHO01",
                "ten_kho": "Kho Hàng Chính",
                "ten_kho2": "Main Warehouse",
                "vi_tri_yn": True,
                "dai_ly_yn": False,
                "ma_nh": "123e4567-e89b-12d3-a456-************",
                "dia_chi": "123 Đường ABC, Quận 1, TP.HCM",
                "dien_thoai": "**********",
                "ghi_chu": "Kho chính của công ty",
                "status": "1",
                "created": "2023-01-01T00:00:00Z",
                "updated": "2023-01-02T00:00:00Z"
            }
        }


class LoaiVatTuModelSerializer(serializers.ModelSerializer):
    tk_kho = serializers.PrimaryKeyRelatedField(required=False, allow_null=True, read_only=True)
    tk_doanh_thu = serializers.PrimaryKeyRelatedField(required=False, allow_null=True, read_only=True)
    tk_gia_von = serializers.PrimaryKeyRelatedField(required=False, allow_null=True, read_only=True)

    class Meta:
        model = LoaiVatTuModel
        fields = ['uuid', 'entity_model', 'ma_loai_vat_tu', 'ten_loai_vat_tu', 'ten_khac',
                  'trang_thai', 'tk_kho', 'tk_doanh_thu', 'tk_gia_von',
                  'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated']


class VatTuSanPhamDonViTinhModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = VatTuSanPhamDonViTinhModel
        fields = ['uuid', 'vat_tu_san_pham_id', 'don_vi_tinh_id',
                  'ten_dvt', 'he_so',
                  'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated']


class VatTuSanPhamHangHoaModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = VatTuSanPhamHangHoaModel
        fields = ['uuid', 'vat_tu_san_pham_id', 'ma_hang_hoa',
                  'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated']




class NhomHangModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = NhomHangModel
        fields = ['uuid', 'ma_nh', 'ten_nh', 'ten_nh2', 'status',
                  'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_nh": "NH01",
                "ten_nh": "Hàng Tiêu Dùng",
                "ten_nh2": "Consumer Goods",
                "status": "1",
                "created": "2023-01-01T00:00:00Z",
                "updated": "2023-01-02T00:00:00Z"
            }
        }
