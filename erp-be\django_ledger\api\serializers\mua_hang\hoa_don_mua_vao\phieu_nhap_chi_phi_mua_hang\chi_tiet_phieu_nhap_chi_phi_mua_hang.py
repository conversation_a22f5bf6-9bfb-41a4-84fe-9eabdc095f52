"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiTietPhieuNhapChiPhiMuaHangSerializer, which handles serialization
for the ChiTietPhieuNhapChiPhiMuaHangModel.
"""

from rest_framework import serializers

from django_ledger.models.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import ChiTietPhieuNhapChiPhiMuaHangModel


class ChiTietPhieuNhapChiPhiMuaHangSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietPhieuNhapChiPhiMuaHangModel.
    """

    # Reference data fields
    ma_vt_data = serializers.SerializerMethodField()
    dvt_data = serializers.SerializerMethodField()
    ma_kho_data = serializers.SerializerMethodField()
    tk_vt_data = serializers.SerializerMethodField()
    ma_bp_data = serializers.SerializerMethodField()

    class Meta:
        model = ChiTietPhieuNhapChiPhiMuaHangModel
        fields = [
            'uuid', 'phieu_nhap', 'line', 'ma_vt', 'dvt', 'ten_dvt', 'ma_kho', 'ten_kho',
            'he_so', 'so_luong', 'tien0', 'cp_nt', 'tk_vt', 'ten_tk_vt', 'ma_bp',
            'ma_vt_data', 'dvt_data', 'ma_kho_data', 'tk_vt_data', 'ma_bp_data'
        ]
        read_only_fields = ['uuid', 'phieu_nhap']

    def get_ma_vt_data(self, obj):
        """
        Returns the material data for the ma_vt field.
        """
        if obj.ma_vt:
            return {
                'uuid': obj.ma_vt.uuid,
                'ma_vt': obj.ma_vt.ma_vt,
                'ten_vt': obj.ma_vt.ten_vt
            }
        return None

    def get_dvt_data(self, obj):
        """
        Returns the unit data for the dvt field.
        """
        if obj.dvt:
            return {
                'uuid': obj.dvt.uuid,
                'ma_dvt': obj.dvt.ma_dvt,
                'ten_dvt': obj.dvt.ten_dvt
            }
        return None

    def get_ma_kho_data(self, obj):
        """
        Returns the warehouse data for the ma_kho field.
        """
        if obj.ma_kho:
            return {
                'uuid': obj.ma_kho.uuid,
                'ma_kho': obj.ma_kho.ma_kho,
                'ten_kho': obj.ma_kho.ten_kho
            }
        return None

    def get_tk_vt_data(self, obj):
        """
        Returns the account data for the tk_vt field.
        """
        if obj.tk_vt:
            return {
                'uuid': obj.tk_vt.uuid,
                'code': obj.tk_vt.code,
                'name': obj.tk_vt.name
            }
        return None

    def get_ma_bp_data(self, obj):
        """
        Returns the department data for the ma_bp field.
        """
        if obj.ma_bp:
            return {
                'uuid': obj.ma_bp.uuid,
                'ma_bp': obj.ma_bp.ma_bp,
                'ten_bp': obj.ma_bp.ten_bp
            }
        return None


