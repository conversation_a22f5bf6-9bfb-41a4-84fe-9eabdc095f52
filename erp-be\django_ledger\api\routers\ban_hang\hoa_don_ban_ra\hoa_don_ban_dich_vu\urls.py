from django.urls import include, path
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.ban_hang.hoa_don_ban_ra.hoa_don_dich_vu import (
    ChiTietHoaDonViewSet,
    HoaDonDichVuViewSet,
    ThongTinThanhToanHoaDonDichVuViewSet,
)

# Main router for HoaDonDichVu
router = DefaultRouter()
router.register("", HoaDonDichVuViewSet, basename="hoa-don-dich-vu")

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path("", include(router.urls)),
    # Nested routes for hoa-don-dich-vu
    path(
        "hoa-don-ban-dich-vu/<uuid:hoa_don_uuid>/",
        include(
            [
                # Chi tiet hoa don dich vu routes
                path(
                    "chi-tiet/",
                    ChiTietHoaDonViewSet.as_view({"get": "list", "post": "create"}),
                    name="chi-tiet-hoa-don-dich-vu-list",
                ),
                path(
                    "chi-tiet/<uuid:uuid>/",
                    ChiTietHoaDonViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="chi-tiet-hoa-don-dich-vu-detail",
                ),
                # Thong tin thanh toan hoa don dich vu routes
                path(
                    "thanh-toan/",
                    ThongTinThanhToanHoaDonDichVuViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="thong-tin-thanh-toan-hoa-don-dich-vu-list",
                ),
                path(
                    "thanh-toan/<uuid:uuid>/",
                    ThongTinThanhToanHoaDonDichVuViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="thong-tin-thanh-toan-hoa-don-dich-vu-detail",
                ),
            ]
        ),
    ),
]
