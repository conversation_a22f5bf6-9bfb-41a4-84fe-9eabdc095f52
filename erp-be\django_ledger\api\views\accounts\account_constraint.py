"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Account constraint view implementation.
"""

from rest_framework import status, viewsets
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from django_ledger.api.permissions import IsEntityAdminOrManager
from django_ledger.api.serializers.accounts.account_constraint import (
    AccountConstraintSerializer,
)
from django_ledger.models import AccountConstraintModel  # Used in docstrings
from django_ledger.services.accounts.account_constraint import AccountConstraintService


class AccountConstraintViewSet(viewsets.ModelViewSet):
    """
    A viewset for viewing and editing account constraints.
    """

    permission_classes = [IsAuthenticated, IsEntityAdminOrManager]
    serializer_class = AccountConstraintSerializer

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.service = AccountConstraintService()

    def get_queryset(self):
        """
        Get the queryset of account constraints for the specified account.
        """
        account_uuid = self.kwargs.get("uuid")
        entity_slug = self.kwargs.get("entity_slug")
        coa_slug = self.kwargs.get("coa_slug")
        user_model = self.request.user

        # Use service to get constraints for account
        return self.service.list_for_account_by_entity(
            entity_slug=entity_slug,
            user_model=user_model,
            account_uuid=account_uuid,
            coa_slug=coa_slug,
        )

    def create(self, request, **kwargs):
        """
        Create a new account constraint
        """
        account_uuid = self.kwargs.get("uuid")
        entity_slug = self.kwargs.get("entity_slug")
        coa_slug = self.kwargs.get("coa_slug")
        user_model = self.request.user

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            # Create the constraint using the service
            data = serializer.validated_data
            instance = self.service.create_for_account(
                entity_slug=entity_slug,
                user_model=user_model,
                account_uuid=account_uuid,
                coa_slug=coa_slug,
                data=data,
            )

            # Return the created constraint
            serializer = self.get_serializer(instance)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def update(self, request, **kwargs):
        """
        Update an account constraint
        """
        constraint_id = self.kwargs.get("pk")

        # Get the constraint using service
        instance = self.service.get_by_id(constraint_id=constraint_id)
        if not instance:
            return Response(
                {"error": "Constraint not found"}, status=status.HTTP_404_NOT_FOUND
            )

        serializer = self.get_serializer(
            instance, data=request.data, partial=kwargs.get("partial", False)
        )
        serializer.is_valid(raise_exception=True)

        try:
            # Update the constraint using the service
            data = serializer.validated_data
            updated_instance = self.service.update(
                constraint_id=constraint_id, data=data
            )

            # Return the updated constraint
            serializer = self.get_serializer(updated_instance)
            return Response(serializer.data)
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def destroy(self, request, **kwargs):
        """
        Delete an account constraint
        """
        constraint_id = self.kwargs.get("pk")

        try:
            # Delete the constraint using the service
            success = self.service.delete(constraint_id=constraint_id)

            if success:
                return Response(status=status.HTTP_204_NO_CONTENT)
            else:
                return Response(
                    {"error": "Constraint not found"}, status=status.HTTP_404_NOT_FOUND
                )
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
