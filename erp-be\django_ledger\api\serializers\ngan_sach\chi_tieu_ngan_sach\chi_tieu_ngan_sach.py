"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for ChiTieuNganSach model.
"""
from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models.ngan_sach.chi_tieu_ngan_sach import ChiTieuNganSachModel
from django_ledger.api.serializers.ngan_sach.chi_tieu_ngan_sach.chi_tiet_chi_tieu_ngan_sach import ChiTietChiTieuNganSachModelSerializer


class ChiTieuNganSachModelSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTieuNganSachModel.
    """
    # Read-only fields for related objects
    tk_data = serializers.SerializerMethodField(read_only=True)
    tk_du_data = serializers.SerializerMethodField(read_only=True)
    tk_gt_data = serializers.SerializerMethodField(read_only=True)

    # Child data
    chi_tiet_data = serializers.SerializerMethodField(read_only=True)
    chi_tiet = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = ChiTieuNganSachModel
        fields = [
            'uuid',
            'entity_model',
            'ma_ctns',
            'ten_ctns',
            'ten_ctns2',
            'status',
            'no_co',
            'dau',
            'tk',
            'tk_data',
            'tk_du',
            'tk_du_data',
            'tk_gt',
            'tk_gt_data',
            'chi_tiet_data',
            'chi_tiet',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'entity_model', 'created', 'updated']

    def get_tk_data(self, obj):
        """
        Get account data for tk.
        """
        # Use standard naming pattern from utils
        if hasattr(obj, 'tk_data'):
            return [
                {
                    'uuid': str(account.uuid),
                    'code': account.code,
                    'name': account.name
                } if account else "notfound"
                for account in obj.tk_data
            ]
        return []

    def get_tk_du_data(self, obj):
        """
        Get account data for tk_du.
        """
        # Use standard naming pattern from utils
        if hasattr(obj, 'tk_du_data'):
            return [
                {
                    'uuid': str(account.uuid),
                    'code': account.code,
                    'name': account.name
                } if account else "notfound"
                for account in obj.tk_du_data
            ]
        return []

    def get_tk_gt_data(self, obj):
        """
        Get account data for tk_gt.
        """
        # Use standard naming pattern from utils
        if hasattr(obj, 'tk_gt_data'):
            return [
                {
                    'uuid': str(account.uuid),
                    'code': account.code,
                    'name': account.name
                } if account else "notfound"
                for account in obj.tk_gt_data
            ]
        return []

    def get_chi_tiet_data(self, obj):
        """
        Get child detail data.
        """
        if hasattr(obj, 'chi_tiet_prepared'):
            return ChiTietChiTieuNganSachModelSerializer(obj.chi_tiet_prepared, many=True).data
        # Fallback if no prepared data
        return ChiTietChiTieuNganSachModelSerializer(obj.chi_tiet.all(), many=True).data

    def to_representation(self, instance):
        """
        Override the default representation to handle UUID serialization.
        """
        representation = super().to_representation(instance)

        # Convert JSON fields containing UUID lists to lists of strings
        json_fields = ['tk', 'tk_du', 'tk_gt']
        for field in json_fields:
            if field in representation and representation[field] is not None:
                if not isinstance(representation[field], list):
                    representation[field] = [str(representation[field])] if representation[field] else []
                else:
                    representation[field] = [str(item) if item is not None else '' for item in representation[field]]

        # Convert other UUID fields to strings
        uuid_fields = ['entity_model']
        for field in uuid_fields:
            if field in representation and representation[field] is not None:
                representation[field] = str(representation[field])

        return representation

    def validate_ma_ctns(self, value):
        """
        Validate ma_ctns field.
        """
        if not value:
            raise serializers.ValidationError(_("ma_ctns is required."))
        return value

    def validate_chi_tiet(self, value):
        """
        Validate chi_tiet data.
        """
        if not isinstance(value, list):
            raise serializers.ValidationError(_("chi_tiet must be a list."))

        # Validate each item in the list
        for item in value:
            if not isinstance(item, dict):
                raise serializers.ValidationError(_("Each chi_tiet item must be a dictionary."))

            # Check required fields
            required_fields = ['line', 'id_dt_loc']
            for field in required_fields:
                if field not in item:
                    raise serializers.ValidationError(_(f"Field '{field}' is required in chi_tiet item."))

        return value

    def validate(self, data):
        """
        Validate the data.
        """
        # Validate required fields
        required_fields = ['ma_ctns', 'ten_ctns', 'no_co', 'dau']
        for field in required_fields:
            if field not in data:
                raise serializers.ValidationError({field: _(f"Field '{field}' is required.")})



        return data
