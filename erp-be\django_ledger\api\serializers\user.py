from django.contrib.auth import get_user_model
from django_ledger.models import UserProfileModel
from rest_framework import serializers

UserModel = get_user_model()


class UserProfileSerializer(serializers.ModelSerializer):
    entity_slug = serializers.SlugRelatedField(
        source="entity", slug_field="slug", read_only=True
    )
    # Add _data fields for reference fields
    parent_user_data = serializers.SerializerMethodField(read_only=True)
    entity_data = serializers.SerializerMethodField(read_only=True)
    department_code_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = UserProfileModel
        fields = [
            "uuid",
            "nickname",
            "phone",
            "birthday",
            "sex",
            "address",
            "department_code",
            "department_code_data",
            "parent_user",
            "parent_user_data",
            "user_ref",
            "language",
            "status",
            "image",
            "pwd_change_required",
            "pq_enabled",
            "entity_slug",  # Include the entity slug
            "entity_data",  # Include entity data
        ]
        read_only_fields = [
            "uuid",
            "entity_slug",
            "entity_data",
            "parent_user_data",
            "department_code_data",
        ]

    def get_parent_user_data(self, obj):
        """
        Get parent user profile data.
        """
        if obj.parent_user:
            # Use a simplified serializer to avoid infinite recursion
            from django_ledger.api.serializers.profile import (
                UserProfileSerializer as SimpleProfileSerializer,
            )

            return SimpleProfileSerializer(obj.parent_user).data
        return None

    def get_department_code_data(self, obj):
        """
        Get department code data.
        """
        if obj.department_code:
            from django_ledger.api.serializers.organization import BoPhanModelSerializer

            return BoPhanModelSerializer(obj.department_code).data
        return None

    def get_entity_data(self, obj):
        """
        Get entity data.
        """
        if obj.entity:
            from django_ledger.api.serializers.entity import EntityModelSerializer

            return EntityModelSerializer(obj.entity).data
        return None


class UserProfileWritableSerializer(UserProfileSerializer):
    """
    Serializer for updating UserProfile data
    """

    class Meta(UserProfileSerializer.Meta):
        pass


class UserSerializer(serializers.ModelSerializer):
    profile = UserProfileSerializer(read_only=True)

    class Meta:
        model = UserModel
        fields = [
            "id",
            "username",
            "email",
            "first_name",
            "last_name",
            "is_staff",
            "is_active",
            "date_joined",
            "profile",  # Include the nested profile data
        ]
        read_only_fields = ["id", "date_joined", "groups_data"]


class UserWritableSerializer(UserSerializer):
    """
    Serializer for updating User data with nested profile
    """

    profile_data = UserProfileWritableSerializer(write_only=True, required=False)

    class Meta(UserSerializer.Meta):
        fields = UserSerializer.Meta.fields + ["profile_data"]

    def update(self, instance, validated_data):
        profile_data = validated_data.pop("profile_data", None)

        # Update user fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        instance.save()

        # Update profile if profile data is provided
        if profile_data:
            profile = instance.profile
            for attr, value in profile_data.items():
                setattr(profile, attr, value)
            profile.save()

        return instance
