"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Account constraint serializer implementation.
"""

from rest_framework import serializers

from django_ledger.models import AccountConstraintModel


class AccountConstraintSerializer(serializers.ModelSerializer):
    """
    Serializer for the AccountConstraintModel
    """
    class Meta:
        model = AccountConstraintModel
        fields = [
            "id",
            "account",
            "constraint_code",
            "constraint_line",
            "constraint_name",
            "is_mandatory",
            "created",
            "updated"
        ]
        read_only_fields = ["id", "created", "updated"]
