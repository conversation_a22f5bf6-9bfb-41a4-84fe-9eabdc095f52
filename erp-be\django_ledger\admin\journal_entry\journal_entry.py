from django.contrib import admin
from django_ledger.models.journal_entry import (
    JournalEntryDetailModel,
    JournalEntryModel,
    TaxInformationModel,
)


@admin.register(JournalEntryModel)
class JournalEntryAdmin(admin.ModelAdmin):
    list_display = ("so_ct", "ngay_ct", "dien_giai", "status")
    search_fields = ("so_ct", "dien_giai")
    list_filter = ("status", "ngay_ct")
    date_hierarchy = "ngay_ct"


@admin.register(JournalEntryDetailModel)
class JournalEntryDetailAdmin(admin.ModelAdmin):
    list_display = ("phieu_ke_toan", "line", "tk", "dien_giai", "ps_no", "ps_co")
    search_fields = ("phieu_ke_toan__so_ct", "tk", "dien_giai")
    list_filter = ("tk", "ma_bp", "ma_vv")


@admin.register(TaxInformationModel)
class TaxInformationAdmin(admin.ModelAdmin):
    list_display = ("phieu_ke_toan", "line", "ma_thue", "thue_suat", "t_thue")
    search_fields = ("phieu_ke_toan__so_ct", "ma_thue", "ma_so_thue")
    list_filter = ("ma_thue", "ma_tc_thue")
