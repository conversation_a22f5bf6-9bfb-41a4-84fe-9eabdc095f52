"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for ButToanDieuChinhGiamCongNo API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.ban_hang.dieu_chinh_can_tru_cong_no.but_toan_dieu_chinh_giam_cong_no import (
    ButToanDieuChinhGiamCongNoViewSet,
    ChiTietButToanDieuChinhGiamCongNoViewSet
)

# Main router for ButToanDieuChinhGiamCongNo
router = DefaultRouter()
router.register('', ButToanDieuChinhGiamCongNoViewSet, basename='but-toan-dieu-chinh-giam-cong-no')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),

    # Nested routes for but_toan_dieu_chinh_giam_cong_no
    path('<uuid:but_toan_dieu_chinh_giam_cong_no_uuid>/', include([
        # ChiTietButToanDieuChinhGiamCongNo routes
        path('chi-tiet/', ChiTietButToanDieuChinhGiamCongNoViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-tiet-but-toan-dieu-chinh-giam-cong-no-list'),

        path('chi-tiet/<uuid:uuid>/', ChiTietButToanDieuChinhGiamCongNoViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-tiet-but-toan-dieu-chinh-giam-cong-no-detail'),
    ])),
]
