"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for Bang Ke TSCD Het Khau Hao Con Su Dung (Fixed Assets Fully Depreciated But Still In Use Report) API endpoints.
"""

from django.urls import path
from django_ledger.api.views.tai_san.tang_giam_tscd.bang_ke_tscd_het_khau_hao_con_su_dung import BangKeTSCDHetKhauHaoConSuDungViewSet

# URL patterns - Single endpoint for fixed assets fully depreciated but still in use report with filters as POST body data
urlpatterns = [
    # Fixed Assets Fully Depreciated But Still In Use Report endpoint - returns report directly with filter POST body data
    path("", BangKeTSCDHetKhauHaoConSuDungViewSet.as_view({"post": "get_report"}), name="bang-ke-tscd-het-khau-hao-con-su-dung-report"),
]
