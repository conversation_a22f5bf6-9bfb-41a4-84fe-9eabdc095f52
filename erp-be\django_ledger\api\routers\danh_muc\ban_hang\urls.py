"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Ban Hang (Sales) catalog module.
"""

from django.urls import path, include

# URL patterns for the module
urlpatterns = [
    # Include kenh_ban_hang URLs
    path('kenh-ban-hang/', include('django_ledger.api.routers.danh_muc.ban_hang.kenh_ban_hang.urls')),

    # Include hinh_thuc_thanh_toan URLs
    path('hinh-thuc-thanh-toan/', include('django_ledger.api.routers.danh_muc.ban_hang.hinh_thuc_thanh_toan.urls')),

    # Include gia_ban URLs
    path('gia-ban/', include('django_ledger.api.routers.danh_muc.ban_hang.gia_ban.urls')),

    # Add other ban_hang-related URLs here
]
