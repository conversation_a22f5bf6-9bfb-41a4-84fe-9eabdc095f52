"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

<PERSON><PERSON> Tin <PERSON>h <PERSON>an Hoa Don Ban Hang (Sales Invoice Payment Information) serializer implementation.
"""

from rest_framework import serializers

from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang import (
    ThongTinThanhToanHoaDonBanHangModel,
)


class ThongTinThanhToanHoaDonBanHangSerializer(serializers.ModelSerializer):
    """
    Serializer for ThongTinThanhToanHoaDonBanHangModel.
    """

    ma_httt_data = serializers.SerializerMethodField()
    tknh_data = serializers.SerializerMethodField()
    tk_data = serializers.SerializerMethodField()
    ma_ct_data = serializers.SerializerMethodField()
    ma_nk_data = serializers.SerializerMethodField()
    id_ct_tt_data = serializers.SerializerMethodField()

    class Meta:
        model = ThongTinThanhToanHoaDonBanHangModel
        fields = '__all__'
        read_only_fields = ['uuid', 'created', 'updated']

    def get_ma_httt_data(self, obj):
        if obj.ma_httt:
            return {
                'uuid': obj.ma_httt.uuid,
                'ma_httt': obj.ma_httt.ma_httt,
                'ten_httt': obj.ma_httt.ten_httt,
            }
        return None

    def get_tknh_data(self, obj):
        if obj.tknh:
            return {
                'uuid': obj.tknh.uuid,
                'account_number': obj.tknh.account_number,
                'name': obj.tknh.name,  # Use 'name' field instead of 'account_name'
                'chu_tk': obj.tknh.chu_tk,  # Account owner name
            }
        return None

    def get_tk_data(self, obj):
        if obj.tk:
            return {'uuid': obj.tk.uuid, 'code': obj.tk.code, 'name': obj.tk.name}
        return None

    def get_ma_ct_data(self, obj):
        if obj.ma_ct:
            return {
                'uuid': obj.ma_ct.uuid,
                'ma_ct': obj.ma_ct.ma_ct,
                'ten_ct': obj.ma_ct.ten_ct,
            }
        return None

    def get_ma_nk_data(self, obj):
        if obj.ma_nk:
            return {
                'uuid': obj.ma_nk.uuid,
                'ma_nk': obj.ma_nk.ma_nk,  # Use 'ma_nk' field instead of 'ma_quyen'
                'ten_nk': obj.ma_nk.ten_nk,  # Use 'ten_nk' field instead of 'ten_quyen'
            }
        return None

    def get_id_ct_tt_data(self, obj):
        if obj.id_ct_tt:
            return {
                'uuid': obj.id_ct_tt.uuid,
                'ma_ct': obj.id_ct_tt.ma_ct,
                'ten_ct': obj.id_ct_tt.ten_ct,
            }
        return None
