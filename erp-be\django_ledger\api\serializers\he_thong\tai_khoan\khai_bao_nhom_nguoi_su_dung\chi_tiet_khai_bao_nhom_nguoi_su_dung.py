"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietKhaiBaoNhomNguoiSuDung model.
"""
from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models import ChiTietKhaiBaoNhomNguoiSuDungModel
from django_ledger.api.serializers.entity import EntityModelSerializer


class ChiTietKhaiBaoNhomNguoiSuDungSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietKhaiBaoNhomNguoiSuDung model.
    """
    # Read-only fields for related objects
    group_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietKhaiBaoNhomNguoiSuDungModel
        fields = [
            'uuid',
            'entity_model',
            'group',
            'group_data',
            'line',
            'user_id',
            'username',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'group',
            'group_data',
            'created',
            'updated'
        ]


    def get_group_data(self, obj):
        """
        Get group data.
        """
        if obj.group:
            return {
                'uuid': str(obj.group.uuid),
                'group_id': obj.group.group_id,
                'groupname': obj.group.groupname,
                'dien_giai': obj.group.dien_giai
            }
        return None


