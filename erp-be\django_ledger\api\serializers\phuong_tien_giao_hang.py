"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for the PhuongTienGiaoHang (Delivery Method) model.
"""

from rest_framework import serializers
from django_ledger.models.phuong_tien_giao_hang import PhuongTienGiaoHangModel


class PhuongTienGiaoHangModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the PhuongTienGiaoHangModel (Delivery Method) model.

    This serializer handles the conversion between PhuongTienGiaoHangModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - entity_model: Reference to the entity (ForeignKey to EntityModel)
    - ma_ptgh: Delivery method code
    - ten_ptgh: Primary name of the delivery method
    - ten_ptgh2: Secondary/alternative name (optional)
    - status: Status indicator ('0'=inactive, '1'=active)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    """
    class Meta:
        model = PhuongTienGiaoHangModel
        fields = ['uuid', 'entity_model', 'ma_ptgh', 'ten_ptgh', 'ten_ptgh2', 'status',
                  'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_ptgh": "STD",
                "ten_ptgh": "Giao hàng tiêu chuẩn",
                "ten_ptgh2": "Standard Delivery",
                "status": "1",
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z"
            }
        }

    def validate_ma_ptgh(self, value):
        """
        Validate ma_ptgh field
        
        Parameters
        ----------
        value : str
            The ma_ptgh value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Mã phương tiện giao hàng không được để trống')
        if len(value) > 100:
            raise serializers.ValidationError('Mã phương tiện giao hàng không được vượt quá 100 ký tự')
        return value.strip()

    def validate_ten_ptgh(self, value):
        """
        Validate ten_ptgh field
        
        Parameters
        ----------
        value : str
            The ten_ptgh value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Tên phương tiện giao hàng không được để trống')
        return value.strip()
