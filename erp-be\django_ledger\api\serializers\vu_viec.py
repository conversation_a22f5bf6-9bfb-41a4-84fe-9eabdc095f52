from rest_framework import serializers

from django_ledger.models.vu_viec import VuViecModel

class VuViecModelSerializer(serializers.ModelSerializer):
    class Meta:
       model = VuViecModel
       fields = [
            'uuid',
            'entity_model',
            'don_vi_id',
            'ma_vu_viec',
            'ten_vu_viec',
            'ten_vu_viec_phu',
            'ngay_vu_viec',
            'so_vu_viec',
            'vu_viec_su_dung_pslk',
            'ma_ngoai_te',
            'tien_ngoai_te',
            'tien_viet_nam',
            'ngay_vu_viec_1',
            'ngay_vu_viec_2',
            'ma_vu_viec_me',
            'trang_thai',
            'ma_khach_hang',
            'ma_nhan_vien_ban_hang',
            'ma_bo_phan',
            'nhom_vu_viec_1',
            'nhom_vu_viec_2',
            'nhom_vu_viec_3',
            'ghi_chu_vu_viec',
            'created',
            'updated'
        ]
    read_only_fields = ['uuid', 'created', 'updated']