"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Ledger Serializers Module
"""
from rest_framework import serializers
from decimal import Decimal

from django_ledger.models import LedgerModel, AccountModel
from django_ledger.services.provider import service_provider

class LedgerModelSerializer(serializers.ModelSerializer):
    """Serializer for ledgers."""
    
    balance = serializers.SerializerMethodField()
    
    class Meta:
        model = LedgerModel
        fields = [
            'uuid',
            'name',
            'entity',
            'posted',
            'locked',
            'balance'
        ]
        read_only_fields = ['uuid', 'posted', 'locked']

    def get_balance(self, instance) -> dict:
        """Get ledger account balances."""
        entity_slug = self.context['entity_slug']
        factory = service_provider.get_factory(entity_slug)
        
        balances = {}
        for account in AccountModel.objects.filter(coa_model__entity=instance.entity):
            balance = factory.ledger.get_account_balance(
                ledger=instance,
                account=account
            )
            if balance != Decimal('0.00'):
                balances[account.code] = {
                    'code': account.code,
                    'name': account.name,
                    'balance': str(balance)
                }
        return balances

    def create(self, validated_data):
        """Create ledger using service."""
        entity_slug = self.context['entity_slug']
        factory = service_provider.get_factory(entity_slug)
        
        return factory.ledger.create_ledger(
            name=validated_data['name']
        )

class LedgerModelUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating ledgers."""
    
    class Meta:
        model = LedgerModel
        fields = ['name']

class LedgerReportSerializer(serializers.Serializer):
    """Serializer for ledger reports."""
    
    from_date = serializers.DateField(required=False)
    to_date = serializers.DateField(required=False)
    
    def validate(self, attrs):
        """Validate date range."""
        from_date = attrs.get('from_date')
        to_date = attrs.get('to_date')
        
        if from_date and to_date and from_date > to_date:
            raise serializers.ValidationError(
                'From date must be before to date'
            )
            
        return attrs
