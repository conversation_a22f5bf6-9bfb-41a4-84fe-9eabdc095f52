"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Views for ThongTinThanhToanHoaDonBanHangIPosModel.
"""

from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import permissions, status
from rest_framework.response import Response

from django_ledger.api.serializers.ban_hang.hoa_don_ban_hang_ipos import ThongTinThanhToanHoaDonBanHangIPosSerializer
from django_ledger.api.views.common import ERPPagination
from django_ledger.api.viewsets import EntityRelatedViewSet
from django_ledger.services.ban_hang.hoa_don_ban_hang_ipos import ThongTinThanhToanHoaDonBanHangIPosService


@extend_schema_view(
    list=extend_schema(tags=["ThongTinThanhToanHoaDonBanHangIPOS"]),
    create=extend_schema(tags=["ThongTinThanhToanHoaDonBanHangIPOS"]),
    retrieve=extend_schema(tags=["ThongTinThanhToanHoaDonBanHangIPOS"]),
    update=extend_schema(tags=["ThongTinThanhToanHoaDonBanHangIPOS"]),
    partial_update=extend_schema(tags=["ThongTinThanhToanHoaDonBanHangIPOS"]),
    destroy=extend_schema(tags=["ThongTinThanhToanHoaDonBanHangIPOS"])
)
class ThongTinThanhToanHoaDonBanHangIPosViewSet(EntityRelatedViewSet):
    """
    A ViewSet for ThongTinThanhToanHoaDonBanHangIPosModel.
    """
    serializer_class = ThongTinThanhToanHoaDonBanHangIPosSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination
    lookup_field = 'uuid'
    http_method_names = ['get', 'post', 'put', 'delete']

    def __init__(self, *args, **kwargs):
        """
        Initialize the ViewSet.

        Parameters
        ----------
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments
        """
        super().__init__(*args, **kwargs)
        self.service = ThongTinThanhToanHoaDonBanHangIPosService()

    def list(self, request, *args, **kwargs):
        """
        List all ThongTinThanhToanHoaDonBanHangIPosModel instances for a specific HoaDonBanHangIPosModel.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get the parent UUID from the URL
        hoa_don_uuid = self.kwargs.get('hoa_don_ban_hang_ipos_uuid')
        entity_slug = self.kwargs['entity_slug']

        # Get data from service
        instances = self.service.list_by_hoa_don(
            hoa_don_uuid=hoa_don_uuid,
            entity_slug=entity_slug,
            user_model=request.user
        )

        # Always paginate the queryset
        page = self.paginate_queryset(instances)
        serializer = self.get_serializer(page, many=True)
        return self.get_paginated_response(serializer.data)

    def create(self, request, *args, **kwargs):
        """
        Create a new ThongTinThanhToanHoaDonBanHangIPosModel instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get the parent UUID from the URL
        hoa_don_uuid = self.kwargs.get('hoa_don_ban_hang_ipos_uuid')
        entity_slug = self.kwargs['entity_slug']

        # Validate data
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Create instance
        instance = self.service.create(
            hoa_don_uuid=hoa_don_uuid,
            entity_slug=entity_slug,
            user_model=request.user,
            data=serializer.validated_data
        )

        # Serialize response
        response_serializer = self.get_serializer(instance)

        # Return response
        return Response(
            response_serializer.data,
            status=status.HTTP_201_CREATED
        )

    def retrieve(self, request, *args, **kwargs):
        """
        Retrieve a ThongTinThanhToanHoaDonBanHangIPosModel instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        entity_slug = self.kwargs['entity_slug']

        # Get instance
        instance = self.service.get_by_id(
            uuid=kwargs['uuid'],
            entity_slug=entity_slug,
            user_model=request.user
        )
        if not instance:
            return Response(
                {'detail': 'Not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Serialize response
        serializer = self.get_serializer(instance)

        # Return response
        return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        """
        Update a ThongTinThanhToanHoaDonBanHangIPosModel instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        entity_slug = self.kwargs['entity_slug']

        # Check if instance exists
        instance = self.service.get_by_id(
            uuid=kwargs['uuid'],
            entity_slug=entity_slug,
            user_model=request.user
        )
        if not instance:
            return Response(
                {'detail': 'Not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Validate data
        serializer = self.get_serializer(instance, data=request.data, partial=kwargs.get('partial', False))
        serializer.is_valid(raise_exception=True)

        # Update instance
        updated_instance = self.service.update(
            uuid=kwargs['uuid'],
            entity_slug=entity_slug,
            user_model=request.user,
            data=serializer.validated_data
        )

        # Serialize response
        response_serializer = self.get_serializer(updated_instance)

        # Return response
        return Response(response_serializer.data)

    def destroy(self, request, *args, **kwargs):
        """
        Delete a ThongTinThanhToanHoaDonBanHangIPosModel instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        entity_slug = self.kwargs['entity_slug']

        # Check if instance exists
        instance = self.service.get_by_id(
            uuid=kwargs['uuid'],
            entity_slug=entity_slug,
            user_model=request.user
        )
        if not instance:
            return Response(
                {'detail': 'Not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Delete instance
        success = self.service.delete(
            uuid=kwargs['uuid'],
            entity_slug=entity_slug,
            user_model=request.user
        )

        if success:
            return Response(status=status.HTTP_204_NO_CONTENT)
        else:
            return Response(
                {'detail': 'Failed to delete.'},
                status=status.HTTP_400_BAD_REQUEST
            )
