"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for PhiNganHang model.
"""

from rest_framework import serializers

from django_ledger.api.serializers.base import BaseModelSerializer
from django_ledger.api.serializers.accounts import AccountModelSerializer
from django_ledger.api.serializers.tax import TaxModelSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.models import PhiNganHangModel


class PhiNganHangModelSerializer(BaseModelSerializer):
    """
    Serializer for PhiNganHangModel.
    """
    # Add data fields for foreign key relationships
    tk_cpnh_data = serializers.SerializerMethodField(read_only=True)
    ma_thue_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_data = serializers.SerializerMethodField(read_only=True)
    ma_kh9_data = serializers.SerializerMethodField(read_only=True)

    def get_tk_cpnh_data(self, obj):
        """Method field for tk_cpnh_data"""
        if obj.tk_cpnh:
            return AccountModelSerializer(obj.tk_cpnh).data
        return None

    def get_ma_thue_data(self, obj):
        """Method field for ma_thue_data"""
        if obj.ma_thue:
            return TaxModelSerializer(obj.ma_thue).data
        return None

    def get_tk_thue_data(self, obj):
        """Method field for tk_thue_data"""
        if obj.tk_thue:
            return AccountModelSerializer(obj.tk_thue).data
        return None

    def get_ma_kh9_data(self, obj):
        """Method field for ma_kh9_data"""
        if obj.ma_kh9:
            return CustomerModelSerializer(obj.ma_kh9).data
        return None

    class Meta:
        model = PhiNganHangModel
        fields = [
            'uuid',
            'ma_cpnh',
            'ten_cpnh',
            'ten_cpnh2',
            'tk_cpnh',
            'tk_cpnh_data',
            'ma_thue',
            'ma_thue_data',
            'tk_thue',
            'tk_thue_data',
            'ma_kh9',
            'ma_kh9_data',
            'ma_mau_bc',
            'ma_tc_thue',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated']
        swagger_schema_fields = {
            'title': 'PhiNganHang',
            'description': 'Phí ngân hàng model serializer'
        }
