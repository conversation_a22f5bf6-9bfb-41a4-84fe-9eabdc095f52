"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for PhieuNhapHangBanTraLai (Customer Return Receipt) model.
"""

from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai import PhieuNhapHangBanTraLaiModel
from django_ledger.api.serializers.nhap_xuat import NhapXuatModelSerializer
from django_ledger.api.serializers.nhan_vien import NhanVienModelSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer
from django_ledger.api.serializers.tax import TaxModelSerializer
from django_ledger.api.serializers.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai.chi_tiet_phieu_nhap_hang_ban_tra_lai import ChiTietPhieuNhapHangBanTraLaiModelSerializer


class PhieuNhapHangBanTraLaiModelSerializer(serializers.ModelSerializer):
    """
    Serializer for PhieuNhapHangBanTraLai model.
    """
    # Read-only fields for related objects
    ma_nk_data = serializers.SerializerMethodField(read_only=True)
    ma_nv_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)
    ma_thue_data = serializers.SerializerMethodField(read_only=True)

    # Child data
    chi_tiet_data = serializers.SerializerMethodField(read_only=True)
    chi_tiet_items = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = PhieuNhapHangBanTraLaiModel
        fields = [
            'uuid',
            'entity_model',
            'ma_nk',
            'ma_nk_data',
            'so_ct',
            'ngay_ct',
            'dien_giai',
            'ma_kh',
            'ma_so_thue',
            'ten_kh_thue',
            'ong_ba',
            'dia_chi',
            'ma_ngv',
            'ma_nv',
            'ma_nv_data',
            'ma_nt',
            'ma_nt_data',
            'ty_gia',
            'ma_thue',
            'ma_thue_data',
            'status',
            't_so_luong',
            't_tien',
            't_thue',
            't_tt',
            'chi_tiet_data',
            'chi_tiet_items',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'ma_nk_data',
            'ma_nv_data',
            'ma_nt_data',
            'ma_thue_data',
            'chi_tiet_data',
            't_so_luong',
            't_tien',
            't_thue',
            't_tt',
            'created',
            'updated'
        ]



    def get_ma_nk_data(self, obj):
        """
        Get ma_nk data.
        """
        if obj.ma_nk:
            return NhapXuatModelSerializer(obj.ma_nk).data
        return None

    def get_ma_nv_data(self, obj):
        """
        Get ma_nv data.
        """
        if obj.ma_nv:
            return NhanVienModelSerializer(obj.ma_nv).data
        return None

    def get_ma_nt_data(self, obj):
        """
        Get ma_nt data.
        """
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_ma_thue_data(self, obj):
        """
        Get ma_thue data.
        """
        if obj.ma_thue:
            return TaxModelSerializer(obj.ma_thue).data
        return None

    def get_chi_tiet_data(self, obj):
        """
        Get chi_tiet data.
        """
        chi_tiet_list = obj.chi_tiet_phieu_nhap_hang_ban_tra_lai.all()
        return ChiTietPhieuNhapHangBanTraLaiModelSerializer(chi_tiet_list, many=True).data


class PhieuNhapHangBanTraLaiModelCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating PhieuNhapHangBanTraLai model.
    """
    chi_tiet_items = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = PhieuNhapHangBanTraLaiModel
        fields = [
            'ma_nk',
            'so_ct',
            'ngay_ct',
            'dien_giai',
            'ma_kh',
            'ma_so_thue',
            'ten_kh_thue',
            'ong_ba',
            'dia_chi',
            'ma_ngv',
            'ma_nv',
            'ma_nt',
            'ty_gia',
            'ma_thue',
            'status',
            'chi_tiet_items'
        ]
