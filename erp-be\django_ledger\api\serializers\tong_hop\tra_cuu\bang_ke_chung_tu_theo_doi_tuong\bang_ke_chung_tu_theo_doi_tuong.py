"""
Django <PERSON>ger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Bang Ke Chung Tu Theo <PERSON> (Document Tracking by Object Report) API.
"""

from rest_framework import serializers


class BangKeChungTuTheoDoiTuongRequestSerializer(serializers.Serializer):
    """
    Serializer for validating document tracking report POST body data.
    Validates essential POST body parameters for the report.
    """

    # Date range filters
    ngay_ct1 = serializers.DateField(
        required=False,
        help_text="Start date filter (YYYYMMDD format)",
    )
    ngay_ct2 = serializers.DateField(
        required=False,
        help_text="End date filter (YYYYMMDD format)",
    )

    # Group by parameter
    group_by = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Group by parameter",
    )

    # Not blank filter
    not_blank = serializers.BooleanField(
        required=False,
        default=False,
        help_text="Not blank filter",
    )

    # Account filters
    tk = serializers.Char<PERSON>ield(
        required=False,
        allow_blank=True,
        help_text="Account codes filter (comma-separated)",
    )
    tk_du = serializers.Char<PERSON>ield(
        required=False,
        allow_blank=True,
        help_text="Corresponding account codes filter (comma-separated)",
    )

    # Currency filter
    ma_nt = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Currency code filter",
    )

    # Document type filter
    ma_ct = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Document type codes filter (comma-separated)",
    )

    # Unit filter
    ma_unit = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Unit code filter",
    )

    # Report template
    mau_bc = serializers.IntegerField(
        required=False,
        help_text="Report template ID",
    )

    # Object filters
    ma_kh = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Customer code filter",
    )
    ma_bp = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Department code filter",
    )
    ma_vv = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Job code filter",
    )
    ma_hd = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Contract code filter",
    )
    ma_dtt = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Payment term code filter",
    )
    ma_ku = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Area code filter",
    )
    ma_phi = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Fee code filter",
    )
    ma_sp = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Product code filter",
    )
    ma_lsx = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Production order code filter",
    )
    ma_cp0 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Invalid cost code filter",
    )

    # Customer group filters
    nh_kh1 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Customer group 1 filter",
    )
    nh_kh2 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Customer group 2 filter",
    )
    nh_kh3 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Customer group 3 filter",
    )

    # Region filter
    rg_code = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Region code filter",
    )

    # Job group filters
    nh_vv1 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Job group 1 filter",
    )
    nh_vv2 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Job group 2 filter",
    )
    nh_vv3 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Job group 3 filter",
    )

    # Area group filters
    nh_ku1 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Area group 1 filter",
    )
    nh_ku2 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Area group 2 filter",
    )
    nh_ku3 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Area group 3 filter",
    )

    # Area type filter
    loai_ku = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Area type filter",
    )

    # Document number range
    so_ct1 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Document number from",
    )
    so_ct2 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Document number to",
    )

    # Description filter
    dien_giai = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Description filter",
    )

    # Credit amount filter
    no_co = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Credit amount filter condition",
    )

    # Data analysis structure
    data_analysis_struct = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Data analysis structure",
    )


class BangKeChungTuTheoDoiTuongResponseSerializer(serializers.Serializer):
    """
    Serializer for document tracking report response data.
    """

    stt = serializers.IntegerField(help_text="Sequential number")
    tk = serializers.CharField(help_text="Account code")
    id = serializers.CharField(help_text="Record ID")
    unit_id = serializers.CharField(help_text="Unit ID")
    ma_ct = serializers.CharField(help_text="Document type code")
    ngay_ct = serializers.DateField(help_text="Document date")
    so_ct = serializers.CharField(help_text="Document number")
    tk_du = serializers.CharField(help_text="Corresponding account")
    nh_dk = serializers.CharField(help_text="Balance type")
    xgroup = serializers.CharField(help_text="Group")
    ps_no = serializers.DecimalField(
        max_digits=18, decimal_places=2, help_text="Debit amount"
    )
    ps_co = serializers.DecimalField(
        max_digits=18, decimal_places=2, help_text="Credit amount"
    )
    dien_giai = serializers.CharField(help_text="Description")
    ma_bp = serializers.CharField(help_text="Department code")
    ma_vv = serializers.CharField(help_text="Job code")
    ma_hd = serializers.CharField(help_text="Contract code")
    ma_ku = serializers.CharField(help_text="Area code")
    ma_phi = serializers.CharField(help_text="Fee code")
    ma_sp = serializers.CharField(help_text="Product code")
    ma_lsx = serializers.CharField(help_text="Production order code")
    ma_dtt = serializers.CharField(help_text="Payment term code")
    ma_cp0 = serializers.CharField(help_text="Invalid cost code")
    ma = serializers.CharField(help_text="Code")
    ten = serializers.CharField(help_text="Name")
