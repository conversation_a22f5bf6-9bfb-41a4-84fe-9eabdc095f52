"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ThongTinVatTuTheoDonVi model.
"""

from rest_framework import serializers

from django_ledger.models.danh_muc.khai_bao_kho_vat_tu.thong_tin_vat_tu_theo_don_vi import ThongTinVatTuTheoDonViModel
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.warehouse import KhoHangModelSerializer
from django_ledger.api.serializers.vi_tri_kho_hang import ViTriKhoHangModelSerializer
from django_ledger.api.serializers.unit import EntityUnitModelSerializer
from django_ledger.models import EntityUnitModel


class ThongTinVatTuTheoDonViSerializer(serializers.ModelSerializer):
    """
    Serializer for ThongTinVatTuTheoDonVi model.
    """
    # Read-only fields for related objects
    ma_vat_tu_data = serializers.SerializerMethodField(read_only=True)
    ma_kho_data = serializers.SerializerMethodField(read_only=True)
    ma_vi_tri_data = serializers.SerializerMethodField(read_only=True)
    unit_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ThongTinVatTuTheoDonViModel
        fields = [
            'uuid',
            'entity_model',
            'ma_vat_tu',
            'ma_vat_tu_data',
            'ma_kho',
            'ma_kho_data',
            'ma_vi_tri',
            'ma_vi_tri_data',
            'unit_id',
            'unit_data',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'entity_model', 'created', 'updated']

    def get_ma_vat_tu_data(self, obj):
        """
        Get basic information about the material
        """
        if not obj.ma_vat_tu:
            return None

        return {
            'uuid': obj.ma_vat_tu.uuid,
            'ma_vt': obj.ma_vat_tu.ma_vt,
            'ten_vt': obj.ma_vat_tu.ten_vt
        }

    def get_ma_kho_data(self, obj):
        """
        Get basic information about the warehouse
        """
        if not obj.ma_kho:
            return None

        return {
            'uuid': obj.ma_kho.uuid,
            'ma_kho': obj.ma_kho.ma_kho,
            'ten_kho': obj.ma_kho.ten_kho
        }

    def get_ma_vi_tri_data(self, obj):
        """
        Get basic information about the warehouse location
        """
        if not obj.ma_vi_tri:
            return None

        return {
            'uuid': obj.ma_vi_tri.uuid,
            'ma_vi_tri': obj.ma_vi_tri.ma_vi_tri,
            'ten_vi_tri': obj.ma_vi_tri.ten_vi_tri
        }

    def get_unit_data(self, obj):
        """
        Get basic information about the unit
        """
        if not obj.unit_id:
            return None

        # Use the EntityUnitModelSerializer to serialize the unit data
        return EntityUnitModelSerializer(obj.unit_id).data
