"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

KhaiBaoTamDungKhauHaoTSCD (Fixed Asset Depreciation Suspension Declaration) router implementation.
"""

from django.urls import path

from django_ledger.api.views.tai_san.dung_khau_hao_tscd.khai_bao_tam_dung_khau_hao_tscd import KhaiBaoTamDungKhauHaoTSCDViewSet

urlpatterns = [
    # KhaiBaoTamDungKhauHaoTSCD routes
    path('', KhaiBaoTamDungKhauHaoTSCDViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='khai-bao-tam-dung-khau-hao-tscd-list'),

    path('<uuid:pk>/', KhaiBaoTamDungKhauHaoTSCDViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='khai-bao-tam-dung-khau-hao-tscd-detail'),
]
