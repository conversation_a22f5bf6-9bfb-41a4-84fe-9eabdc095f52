"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietCapNhatNganSach model.
"""
from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models import ChiTietCapNhatNganSachModel
from django_ledger.api.serializers.unit import EntityUnitModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.api.serializers.ngan_sach.chi_tieu_ngan_sach import ChiTieuNganSachModelSerializer


class ChiTietCapNhatNganSachSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietCapNhatNganSach model.
    """
    # Read-only fields for related objects
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_so_data = serializers.SerializerMethodField(read_only=True)
    bao_cao_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietCapNhatNganSachModel
        fields = [
            'uuid',
            'bao_cao',
            'bao_cao_data',
            'id_maubc',
            'nam',
            'unit_id',
            'unit_id_data',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_so',
            'ma_so_data',
            'ky',
            'so_tien_nt',
            'so_tien',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'ngan_sach_data',
            'unit_id_data',
            'ma_bp_data',
            'ma_vv_data',
            'ma_so_data',
            'created',
            'updated'
        ]
        extra_kwargs = {
            'so_tien_nt': {'required': True},
            'so_tien': {'required': True},
            'ky': {'required': True},
            'ma_bp': {'required': False, 'allow_null': True},
            'ma_vv': {'required': False, 'allow_null': True},
            'ma_so': {'required': False, 'allow_null': True},
            'unit_id': {'required': False, 'allow_null': True}
        }

    def get_bao_cao_data(self, obj):
        """
        Get budget update data.
        """
        if obj.bao_cao:
            return {
                'uuid': obj.bao_cao.uuid,
                'id_maubc': obj.bao_cao.id_maubc,
                'nam': obj.bao_cao.nam
            }
        return None

    def get_unit_id_data(self, obj):
        """
        Get unit data.
        """
        if obj.unit_id:
            return EntityUnitModelSerializer(obj.unit_id).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):
        """
        Get project data.
        """
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_so_data(self, obj):
        """
        Get budget item data.
        """
        if obj.ma_so:
            return ChiTieuNganSachModelSerializer(obj.ma_so).data
        return None

    def validate(self, attrs):
        """
        Validate the serializer data.
        """
        # Check if this is an update or create operation
        is_update = self.instance is not None

        # For create operation, validate required fields
        if not is_update:
            required_fields = ['id_maubc', 'nam', 'ky', 'so_tien_nt', 'so_tien']
            for field in required_fields:
                if field not in attrs:
                    raise serializers.ValidationError({
                        field: _('This field is required.')
                    })

        # Set default values for optional fields if not provided
        attrs.setdefault('ma_bp', None)
        attrs.setdefault('ma_vv', None)
        attrs.setdefault('ma_so', None)
        attrs.setdefault('unit_id', None)

        # Validate ky is between 1 and 12
        if 'ky' in attrs and (attrs['ky'] < 1 or attrs['ky'] > 12):
            raise serializers.ValidationError({
                'ky': _('Ky must be between 1 and 12.')
            })


        # Ensure amounts are non-negative
        if attrs.get('so_tien_nt', 0) < 0:
            raise serializers.ValidationError({
                'so_tien_nt': _('Amount must be non-negative.')
            })

        if attrs.get('so_tien', 0) < 0:
            raise serializers.ValidationError({
                'so_tien': _('Amount must be non-negative.')
            })

        return attrs
