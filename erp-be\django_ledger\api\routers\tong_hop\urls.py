"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Tong Hop (General Accounting) module.
"""

from django.urls import include, path

# URL patterns for the module
urlpatterns = [
    # Include hach_toan URLs
    path("hach-toan/", include("django_ledger.api.routers.tong_hop.hach_toan.urls")),
    # Include but_toan_cuoi_ky URLs
    path('but-toan-cuoi-ky/', include('django_ledger.api.routers.tong_hop.but_toan_cuoi_ky.urls')),

    # Include bao_cao_tai_chinh URLs
    path('bao-cao-tai-chinh/', include('django_ledger.api.routers.tong_hop.bao_cao_tai_chinh.urls')),

    path(
        "but-toan-cuoi-ky/",
        include("django_ledger.api.routers.tong_hop.but_toan_cuoi_ky.urls"),
    ),
    # Include tra_cuu URLs
    path("tra-cuu/", include("django_ledger.api.routers.tong_hop.tra_cuu.urls")),
    # Add other tong_hop-related URLs here
]
