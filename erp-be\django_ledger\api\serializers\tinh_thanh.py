from rest_framework import serializers
from django_ledger.models.tinh_thanh import TinhThanhModel


class TinhThanhModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the TinhThanhModel (Province/City) model.

    This serializer handles the conversion between TinhThanhModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - ma_tinh: Province/City code
    - ten_tinh: Primary name of the province/city
    - ten_tinh2: Secondary/alternative name (optional)
    - status: Status indicator ('0'=inactive, '1'=active)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    """

    class Meta:
        model = TinhThanhModel
        fields = ['uuid', 'entity_model', 'ma_tinh', 'ten_tinh', 'ten_tinh2', 'status',
                  'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_tinh": "HN",
                "ten_tinh": "Hà Nội",
                "ten_tinh2": "Hanoi",
                "status": "1",
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z"
            }
        }
