"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for PhieuYeuCauKiemKe (Inventory Check Request) module.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.ton_kho.kiem_ke.phieu_yeu_cau_kiem_ke import PhieuYeuCauKiemKeViewSet

# Main router for PhieuYeuCauKiemKe
router = DefaultRouter()
router.register('', PhieuYeuCauKiemKeViewSet, basename='phieu-yeu-cau-kiem-ke')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
