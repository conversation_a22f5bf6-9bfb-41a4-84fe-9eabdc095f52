"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Nested serializer for ThongTinThanhToanHoaDonDichVu model.
"""

from rest_framework import serializers

from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_dich_vu import ThongTinThanhToanHoaDonDichVuModel
from django_ledger.models.danh_muc.ban_hang.hinh_thuc_thanh_toan.hinh_thuc_thanh_toan import HinhThucThanhToanModel
from django_ledger.models.bank_account import BankAccountModel
from django_ledger.models.accounts import AccountModel
from django_ledger.models.chung_tu import ChungTu
from django_ledger.models.quyen_chung_tu import QuyenChungTu


class ThongTinThanhToanHoaDonDichVuNestedSerializer(serializers.ModelSerializer):
    """
    Nested serializer for ThongTinThanhToanHoaDonDichVuModel.
    Used for creating and updating ThongTinThanhToanHoaDonDichVuModel instances as part of a HoaDonDichVuModel.
    """

    # Reference data fields
    ma_httt_data = serializers.SerializerMethodField(read_only=True)
    tknh_data = serializers.SerializerMethodField(read_only=True)
    tk_data = serializers.SerializerMethodField(read_only=True)
    ma_ct_data = serializers.SerializerMethodField(read_only=True)
    ma_nk_data = serializers.SerializerMethodField(read_only=True)
    id_ct_tt_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ThongTinThanhToanHoaDonDichVuModel
        fields = [
            'uuid',
            'line',
            'ma_httt',
            'ma_httt_data',
            'ten_httt',
            'tknh',
            'tknh_data',
            'ten_tknh',
            'tk',
            'tk_data',
            'ten_tk',
            'ma_ct',
            'ma_ct_data',
            'ten_ct',
            'ngay_ct',
            'ma_nk',
            'ma_nk_data',
            'ten_nk',
            't_tt_nt',
            'id_ct_tt',
            'id_ct_tt_data',
            'status'
        ]

    def get_ma_httt_data(self, obj):
        """Return payment method data if available"""
        try:
            httt = HinhThucThanhToanModel.objects.get(uuid=obj.ma_httt.uuid)
            return {
                'uuid': str(httt.uuid),
                'ma_httt': httt.ma_httt,
                'ten_httt': httt.ten_httt
            }
        except (HinhThucThanhToanModel.DoesNotExist, AttributeError):
            return None

    def get_tknh_data(self, obj):
        """Return bank account data if available"""
        try:
            bank_account = BankAccountModel.objects.get(uuid=obj.tknh.uuid)
            return {
                'uuid': str(bank_account.uuid),
                'account_number': bank_account.account_number,
                'account_name': bank_account.name
            }
        except (BankAccountModel.DoesNotExist, AttributeError):
            return None

    def get_tk_data(self, obj):
        """Return account data if available"""
        try:
            account = AccountModel.objects.get(uuid=obj.tk.uuid)
            return {
                'uuid': str(account.uuid),
                'code': account.code,
                'name': account.name
            }
        except (AccountModel.DoesNotExist, AttributeError):
            return None

    def get_ma_ct_data(self, obj):
        """Return document data if available"""
        try:
            chung_tu = ChungTu.objects.get(uuid=obj.ma_ct.uuid)
            return {
                'uuid': str(chung_tu.uuid),
                'ma_ct': chung_tu.ma_ct,
                'ten_ct': chung_tu.ten_ct
            }
        except (ChungTu.DoesNotExist, AttributeError):
            return None

    def get_ma_nk_data(self, obj):
        """Return document number data if available"""
        try:
            quyen = QuyenChungTu.objects.get(uuid=obj.ma_nk.uuid)
            return {
                'uuid': str(quyen.uuid),
                'ma_quyen': quyen.ma_quyen,
                'ten_quyen': quyen.ten_quyen
            }
        except (QuyenChungTu.DoesNotExist, AttributeError):
            return None

    def get_id_ct_tt_data(self, obj):
        """Return payment document data if available"""
        try:
            chung_tu = ChungTu.objects.get(uuid=obj.id_ct_tt.uuid)
            return {
                'uuid': str(chung_tu.uuid),
                'ma_ct': chung_tu.ma_ct,
                'ten_ct': chung_tu.ten_ct
            }
        except (ChungTu.DoesNotExist, AttributeError):
            return None
