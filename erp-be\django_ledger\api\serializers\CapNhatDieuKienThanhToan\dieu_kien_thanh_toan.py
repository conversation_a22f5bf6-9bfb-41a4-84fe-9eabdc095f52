"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Customized by TTMI Office.
"""

from rest_framework import serializers

from django_ledger.api.serializers.erp import CustomerModelSerializer
from django_ledger.models import DieuKienThanhToanModel, ChiTietDieuKienThanhToanModel


# Create a simplified serializer for ChiTietDieuKienThanhToanModel to avoid circular imports
class ChiTietDieuKienThanhToanSimpleSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for the ChiTietDieuKienThanhToanModel to avoid circular imports
    """
    class Meta:
        model = ChiTietDieuKienThanhToanModel
        fields = [
            'uuid',
            'ma_kh',
            'ma_dk',
            'line',
            'ngay_gom_hd1',
            'ngay_gom_hd2',
            'created_by',
            'updated_by',
            'created',
            'updated',
        ]
        read_only_fields = [
            'uuid',
            'created',
            'updated',
        ]


class DieuKienThanhToanModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the DieuKienThanhToanModel
    """
    ma_kh_data = CustomerModelSerializer(source='ma_kh', read_only=True)
    ngay_gom_chi_tiet = serializers.SerializerMethodField(read_only=True)

    def get_ngay_gom_chi_tiet(self, obj):
        """
        Get all related payment condition details (chi_tiet_dieu_kien_thanh_toan) for this payment condition
        """
        chi_tiet_set = obj.chi_tiet_set.all()
        return ChiTietDieuKienThanhToanSimpleSerializer(chi_tiet_set, many=True).data

    class Meta:
        model = DieuKienThanhToanModel
        fields = [
            'uuid',
            'ma_dk',
            'ten_dk',
            'ten_dk2',
            'loai_dk',
            'ngay_gom_hd1',
            'ngay_gom_hd2',
            'ngay_tt_thang',
            'ngay_tt_tuan',
            'tuan_tt_thu',
            'so_ngay',
            'status',
            'created_by',
            'updated_by',
            'created',
            'updated',
            'ma_kh',
            'ma_kh_data',
            'ngay_gom_chi_tiet',
        ]
        read_only_fields = [
            'uuid',
            'created',
            'updated',
        ]


class DieuKienThanhToanModelCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating DieuKienThanhToanModel
    """
    ngay_gom_chi_tiet = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        write_only=True,
        help_text="Array of payment condition details to create along with the payment condition"
    )

    class Meta:
        model = DieuKienThanhToanModel
        fields = [
            'ma_dk',
            'ten_dk',
            'ten_dk2',
            'loai_dk',
            'ngay_gom_hd1',
            'ngay_gom_hd2',
            'ngay_tt_thang',
            'ngay_tt_tuan',
            'tuan_tt_thu',
            'so_ngay',
            'status',
            'ma_kh',
            'ngay_gom_chi_tiet',
        ]
