# ERP Schemas

This document describes the database schemas for Django Ledger's ERP functionality.

## KhoHangModel (Warehouse)

The `KhoHangModel` represents a warehouse or storage location.

| Field | Type | Description |
|-------|------|-------------|
| uuid | UUID | Primary key |
| don_vi | CharField | Unit/division |
| ma_kho | CharField | Warehouse code |
| ten_kho | CharField | Warehouse name |
| ten_khac | CharField | Alternative name |
| vi_tri | BooleanField | Location tracking enabled |
| dai_ly | BooleanField | Is agency |
| dia_chi | CharField | Address |
| dien_thoai | CharField | Phone |
| trang_thai | IntegerField | Status |
| created | DateTimeField | Creation timestamp |
| updated | DateTimeField | Last update timestamp |

## NhanVienModel (Employee)

The `NhanVienModel` represents an employee in the system.

| Field | Type | Description |
|-------|------|-------------|
| uuid | UUID | Primary key |
| ma_nhan_vien | CharField | Employee code |
| ban_hang | BooleanField | Is salesperson |
| mua_hang | BooleanField | Is purchaser |
| cong_no_tam_ung | BooleanField | Has debt/advance |
| ho_va_ten | CharField | Full name |
| chuc_vu | CharField | Position |
| bo_phan | CharField | Department |
| gioi_tinh | CharField | Gender |
| ngay_sinh | DateTimeField | Birth date |
| dia_chi_thuong_tru | CharField | Permanent address |
| dien_thoai | CharField | Phone |
| email | CharField | Email |
| ma_so_thue_tncn | CharField | Personal tax ID |
| trang_thai | IntegerField | Status |
| created | DateTimeField | Creation timestamp |
| updated | DateTimeField | Last update timestamp |

## NgoaiTeModel (Currency)

The `NgoaiTeModel` represents a currency in the system.

| Field | Type | Description |
|-------|------|-------------|
| uuid | UUID | Primary key |
| ngoai_te | CharField | Currency code |
| ten_ngoai_te | CharField | Currency name |
| ten_khac | CharField | Alternative name |
| tk_phat_sinh_cl_no | ForeignKey | Debit exchange diff account |
| tk_phat_sinh_cl_co | ForeignKey | Credit exchange diff account |
| stt | IntegerField | Order/priority |
| doc_le | IntegerField | Decimal display format |
| cach_doc | CharField | Reading convention |
| trang_thai | IntegerField | Status |
| created | DateTimeField | Creation timestamp |
| updated | DateTimeField | Last update timestamp |

## HanThanhToanModel (Payment Terms)

The `HanThanhToanModel` represents payment terms.

| Field | Type | Description |
|-------|------|-------------|
| uuid | UUID | Primary key |
| ma_thanh_toan | CharField | Payment term code |
| ten_thanh_toan | CharField | Payment term name |
| ten_khac | CharField | Alternative name |
| trang_thai | IntegerField | Status |
| created | DateTimeField | Creation timestamp |
| updated | DateTimeField | Last update timestamp |

## PhuongThucThanhToanModel (Payment Method)

The `PhuongThucThanhToanModel` represents payment methods.

| Field | Type | Description |
|-------|------|-------------|
| uuid | UUID | Primary key |
| ma_phuong_thuc | CharField | Method code |
| ten_phuong_thuc | CharField | Method name |
| ma_ph_thuc_hddt | CharField | E-invoice method code |
| trang_thai | IntegerField | Status |
| created | DateTimeField | Creation timestamp |
| updated | DateTimeField | Last update timestamp |

## LoaiVatTuModel (Product Type)

The `LoaiVatTuModel` represents product/material types.

| Field | Type | Description |
|-------|------|-------------|
| uuid | UUID | Primary key |
| ma_loai_vat_tu | CharField | Type code |
| ten_loai_vat_tu | CharField | Type name |
| ten_khac | CharField | Alternative name |
| trang_thai | IntegerField | Status |
| tk_kho | ForeignKey | Inventory account |
| tk_doanh_thu | ForeignKey | Revenue account |
| tk_gia_von | ForeignKey | Cost of goods account |
| created | DateTimeField | Creation timestamp |
| updated | DateTimeField | Last update timestamp |

## ThueSuatThueGTGTModel (VAT Tax Rate)

The `ThueSuatThueGTGTModel` represents VAT tax rates.

| Field | Type | Description |
|-------|------|-------------|
| uuid | UUID | Primary key |
| ma_thue | CharField | Tax code |
| ten_thue | CharField | Tax name |
| ten_khac | CharField | Alternative name |
| thue_suat | DecimalField | Tax rate |
| thue_suat_hddt | DecimalField | E-invoice tax rate |
| nhom_thue | ForeignKey | Tax group |
| tk_thue_dau_ra | ForeignKey | Output tax account |
| tk_thue_dau_vao | ForeignKey | Input tax account |
| trang_thai | IntegerField | Status |
| created | DateTimeField | Creation timestamp |
| updated | DateTimeField | Last update timestamp |

## VatTuSanPhamDonViTinhModel (Product-Unit Relation)

The `VatTuSanPhamDonViTinhModel` represents the relationship between products and their units of measure.

| Field | Type | Description |
|-------|------|-------------|
| uuid | UUID | Primary key |
| vat_tu_san_pham_id | ForeignKey | Product reference |
| don_vi_tinh_id | ForeignKey | Unit of measure reference |
| ten_dvt | CharField | Unit name |
| he_so | DecimalField | Conversion factor |
| created | DateTimeField | Creation timestamp |
| updated | DateTimeField | Last update timestamp |

## VatTuSanPhamHangHoaModel (Product Variant)

The `VatTuSanPhamHangHoaModel` represents product variants.

| Field | Type | Description |
|-------|------|-------------|
| uuid | UUID | Primary key |
| vat_tu_san_pham_id | ForeignKey | Product reference |
| ma_hang_hoa | CharField | Variant code |
| created | DateTimeField | Creation timestamp |
| updated | DateTimeField | Last update timestamp |

## GiaMuaModel (Purchase Price)

The `GiaMuaModel` represents purchase prices for products.

| Field | Type | Description |
|-------|------|-------------|
| uuid | UUID | Primary key |
| ma_vat_tu | ForeignKey | Product reference |
| don_vi_tinh | ForeignKey | Unit of measure |
| ngay_hieu_luc | DateTimeField | Effective date |
| nha_cung_cap | ForeignKey | Vendor reference |
| ngoai_te | ForeignKey | Currency reference |
| so_luong_tu | IntegerField | Minimum quantity |
| gia_mua | DecimalField | Purchase price |
| trang_thai | IntegerField | Status |
| created | DateTimeField | Creation timestamp |
| updated | DateTimeField | Last update timestamp |

## RangBuocNhapModel (Input Constraint)

The `RangBuocNhapModel` represents input constraints for accounts.

| Field | Type | Description |
|-------|------|-------------|
| uuid | UUID | Primary key |
| tai_khoan_id | ForeignKey | Account reference |
| ten_doi_tuong | CharField | Object name |
| bat_buoc_nhap | BooleanField | Required input |
| created | DateTimeField | Creation timestamp |
| updated | DateTimeField | Last update timestamp |

## KheUocModel (Loan/Contract)

The `KheUocModel` represents loan agreements or contracts.

| Field | Type | Description |
|-------|------|-------------|
| uuid | UUID | Primary key |
| ma_khe_uoc | CharField | Contract code |
| ten_khe_uoc | CharField | Contract name |
| ten_khac | CharField | Alternative name |
| loai_khe_uoc | IntegerField | Contract type |
| ngay | DateTimeField | Date |
| ngay_vay | DateTimeField | Loan date |
| ngay_dao_han | DateTimeField | Maturity date |
| tien_vay_nt | DecimalField | Loan amount (foreign currency) |
| tien_vay | DecimalField | Loan amount |
| hop_dong | CharField | Contract reference |
| khach_hang | ForeignKey | Customer reference |
| tai_khoan | ForeignKey | Account reference |
| trang_thai | IntegerField | Status |
| created | DateTimeField | Creation timestamp |
| updated | DateTimeField | Last update timestamp |

## KheUocThongTinLaiSuatModel (Loan Interest Rate)

The `KheUocThongTinLaiSuatModel` represents interest rates for loans.

| Field | Type | Description |
|-------|------|-------------|
| uuid | UUID | Primary key |
| khe_uoc_id | ForeignKey | Loan reference |
| lai_suat | DecimalField | Interest rate |
| hieu_luc_tu | DateTimeField | Effective date |
| ghi_chu | CharField | Notes |
| created | DateTimeField | Creation timestamp |
| updated | DateTimeField | Last update timestamp |

## KheUocChiTietThanhToanModel (Loan Payment Detail)

The `KheUocChiTietThanhToanModel` represents payment details for loans.

| Field | Type | Description |
|-------|------|-------------|
| uuid | UUID | Primary key |
| khe_uoc_id | ForeignKey | Loan reference |
| ngay_thanh_toan | DateTimeField | Payment date |
| tien_thanh_toan | DecimalField | Payment amount |
| tien_thanh_toan_nt | DecimalField | Payment amount (foreign currency) |
| ghi_chu | CharField | Notes |
| created | DateTimeField | Creation timestamp |
| updated | DateTimeField | Last update timestamp |

## NhomPhiModel (Fee Group)

The `NhomPhiModel` represents fee groups.

| Field | Type | Description |
|-------|------|-------------|
| uuid | UUID | Primary key |
| ma_nhom | CharField | Group code |
| ten_phan_nhom | CharField | Group name |
| ten_2 | CharField | Secondary name |
| trang_thai | IntegerField | Status |
| loai_nhom | IntegerField | Group type |
| created | DateTimeField | Creation timestamp |
| updated | DateTimeField | Last update timestamp |

## PhiModel (Fee)

The `PhiModel` represents fees.

| Field | Type | Description |
|-------|------|-------------|
| uuid | UUID | Primary key |
| ma_phi | CharField | Fee code |
| ten_phi | CharField | Fee name |
| ten_khac | CharField | Alternative name |
| nhom_phi_1 | ForeignKey | Primary fee group |
| nhom_phi_2 | ForeignKey | Secondary fee group |
| nhom_phi_3 | ForeignKey | Tertiary fee group |
| bo_phan | CharField | Department |
| trang_thai | IntegerField | Status |
| created | DateTimeField | Creation timestamp |
| updated | DateTimeField | Last update timestamp |

## Enhanced Existing Models

### CustomerModel Extensions

| Additional Field | Type | Description |
|------------------|------|-------------|
| ma_khach_hang | CharField | Customer code |
| khach_hang | BooleanField | Is customer |
| nha_cung_cap | BooleanField | Is vendor |
| loai_khach_hang | IntegerField | Customer type |
| ten_khach_hang | CharField | Customer name |
| ten_khac | CharField | Alternative name |
| dia_chi | CharField | Address |
| ma_so_thue | CharField | Tax ID |
| nguoi_lien_he | CharField | Contact person |
| nhan_vien_ban_hang | ForeignKey | Sales rep |
| tai_khoan_ngam_dinh | ForeignKey | Default account |
| ma_th_toan_cong_no | ForeignKey | Payment terms |
| ph_th_th_toan | ForeignKey | Payment method |
| gioi_han_tien_no | IntegerField | Credit limit |
| khu_vuc | CharField | Region |
| dien_thoai | CharField | Phone |
| ngay_sinh | DateTimeField | Birth date |
| so_tai_khoan | CharField | Bank account |
| ten_ngan_hang | CharField | Bank name |

### VendorModel Extensions

| Additional Field | Type | Description |
|------------------|------|-------------|
| ma_nha_cung_cap | CharField | Vendor code |
| khach_hang | BooleanField | Is customer |
| nha_cung_cap | BooleanField | Is vendor |
| loai_khach_hang | IntegerField | Customer type |
| ten_nha_cung_cap | CharField | Vendor name |
| ten_khac | CharField | Alternative name |
| dia_chi | CharField | Address |
| ma_so_thue | CharField | Tax ID |
| nguoi_lien_he | CharField | Contact person |
| nhan_vien_ban_hang | ForeignKey | Sales rep |
| tai_khoan_ngam_dinh | ForeignKey | Default account |
| ma_th_toan_cong_no | ForeignKey | Payment terms |
| ph_th_th_toan | ForeignKey | Payment method |
| gioi_han_tien_no | IntegerField | Credit limit |
| khu_vuc | CharField | Region |
| dien_thoai | CharField | Phone |
| ngay_sinh | DateTimeField | Birth date |
| so_tai_khoan | CharField | Bank account |
| ten_ngan_hang | CharField | Bank name |

### ItemModel Extensions

| Additional Field | Type | Description |
|------------------|------|-------------|
| ma_san_pham | CharField | Product code |
| ten_san_pham | CharField | Product name |
| ten_khac | CharField | Alternative name |
| tao_nhap_thanh_phan | BooleanField | Create component input |
| dvt_nhap_lieu | ForeignKey | Input unit |
| theo_doi_ton_kho | BooleanField | Track inventory |
| theo_doi_lo | BooleanField | Track lots |
| cach_tinh_gia_ton_kho | IntegerField | Inventory costing method |
| loai_vat_tu | ForeignKey | Product type |
| ma_kho_mac_dinh | ForeignKey | Default warehouse |
| ma_thue_mac_dinh | ForeignKey | Default tax |
| tk_kho | ForeignKey | Inventory account |
| tk_doanh_thu | ForeignKey | Revenue account |
| tk_gia_von | ForeignKey | Cost of goods account |
| the_tich | DecimalField | Volume |
| khoi_luong | DecimalField | Weight |
