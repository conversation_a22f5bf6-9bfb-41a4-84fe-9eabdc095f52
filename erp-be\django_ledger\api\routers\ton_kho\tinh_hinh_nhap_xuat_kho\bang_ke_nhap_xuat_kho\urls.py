"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for Bang Ke Nhap <PERSON> (Inventory In/Out Report) API endpoints.
"""

from django.urls import path
from django_ledger.api.views.ton_kho.tinh_hinh_nhap_xuat_kho.bang_ke_nhap_xuat_kho import BangKeNhapXuatKhoViewSet

# URL patterns - Single endpoint for inventory report with filters as POST body data
urlpatterns = [
    # Inventory In/Out Report endpoint - returns report directly with filter POST body data
    path("", BangKeNhapXuatKhoViewSet.as_view({"post": "get_report"}), name="bang-ke-nhap-xuat-kho-report"),
]
