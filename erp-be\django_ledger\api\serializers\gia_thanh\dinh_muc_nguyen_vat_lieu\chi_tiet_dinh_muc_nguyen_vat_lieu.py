"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietDinhMucNguyenVatLieu model.
"""
from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models import ChiTietDinhMucNguyenVatLieuModel, VatTuModel, DonViTinhModel, KhoHangModel
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.warehouse import KhoHangModelSerializer


class ChiTietDinhMucNguyenVatLieuSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietDinhMucNguyenVatLieu model.
    """
    # Read-only fields for related objects
    product_unit_data = serializers.SerializerMethodField(read_only=True)
    ma_vt_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)
    ma_kho_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietDinhMucNguyenVatLieuModel
        fields = [
            'uuid',
            'product_unit',
            'product_unit_data',
            'id',
            'line',
            'ma_vt',
            'ma_vt_data',
            'dvt',
            'dvt_data',
            'ten_dvt',
            'he_so',
            'ma_kho',
            'ma_kho_data',
            'ten_kho',
            'so_luong',
            'tl_hh',
            'ngay_ct1',
            'ngay_ct2',
            'tg_th',
            'tl_pb',
            'dien_giai',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'product_unit',
            'product_unit_data',
            'ma_vt_data',
            'dvt_data',
            'ma_kho_data',
            'created',
            'updated'
        ]

    def get_product_unit_data(self, obj):
        """
        Get parent material quota data.
        """
        if obj.product_unit:
            return {
                'uuid': str(obj.product_unit.uuid),
                'id': obj.product_unit.id,
                'ma_sp': str(obj.product_unit.ma_sp.uuid) if obj.product_unit.ma_sp else None,
                'ma_sp_name': str(obj.product_unit.ma_sp) if obj.product_unit.ma_sp else None
            }
        return None

    def get_ma_vt_data(self, obj):
        """
        Get material data.
        """
        if obj.ma_vt:
            return VatTuSerializer(obj.ma_vt).data
        return None

    def get_dvt_data(self, obj):
        """
        Get unit of measure data.
        """
        if obj.dvt:
            return DonViTinhSerializer(obj.dvt).data
        return None

    def get_ma_kho_data(self, obj):
        """
        Get warehouse data.
        """
        if obj.ma_kho:
            return KhoHangModelSerializer(obj.ma_kho).data
        return None

    def validate(self, attrs):
        """
        Validate the serializer data.
        """
        # Check if this is an update or create operation
        is_update = self.instance is not None

        # Removed validation for 'id' field as it's now optional

        # Set default values if not provided
        if 'so_luong' not in attrs:
            attrs['so_luong'] = 0.0

        if 'tl_hh' not in attrs:
            attrs['tl_hh'] = 0.0

        if 'tg_th' not in attrs:
            attrs['tg_th'] = 0.0

        if 'tl_pb' not in attrs:
            attrs['tl_pb'] = 100.0

        return attrs


