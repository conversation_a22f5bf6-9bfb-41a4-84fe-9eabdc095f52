"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietKheUocLaiSuat (Loan Interest Rate Detail) model.
"""

from rest_framework import serializers

from django_ledger.models.danh_muc.hop_dong_khe_uoc.khe_uoc.chi_tiet_khe_uoc_lai_suat import ChiTietKheUocLaiSuatModel
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import KheUocModelSerializer
from django_ledger.services.danh_muc.hop_dong_khe_uoc.khe_uoc.chi_tiet_khe_uoc_lai_suat import ChiTietKheUocLaiSuatService


class ChiTietKheUocLaiSuatModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the ChiTietKheUocLaiSuatModel
    """
    ma_ku_data = KheUocModelSerializer(source='ma_ku', read_only=True)
    
    class Meta:
        model = ChiTietKheUocLaiSuatModel
        fields = [
            'uuid', 'ma_ku', 'ma_ku_data',
            'line', 'ls', 'ngay_hl', 'ghi_chu',
            'created', 'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated']
    
    def create(self, validated_data):
        """
        Create a new loan interest rate detail
        """
        entity_slug = self.context.get('entity_slug')
        user_model = self.context.get('request').user
        
        service = ChiTietKheUocLaiSuatService(
            entity_slug=entity_slug,
            user_model=user_model
        )
        
        return service.create(validated_data)
    
    def update(self, instance, validated_data):
        """
        Update an existing loan interest rate detail
        """
        entity_slug = self.context.get('entity_slug')
        user_model = self.context.get('request').user
        
        service = ChiTietKheUocLaiSuatService(
            entity_slug=entity_slug,
            user_model=user_model
        )
        
        return service.update(instance.uuid, validated_data)
