"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the URL patterns for the PhieuXuatTraLaiNhaCungCap API.
"""

from django.urls import include, path
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.mua_hang.hoa_don_dieu_chinh_tra_lai.phieu_xuat_tra_lai_nha_cung_cap import (
    ChiTietPhieuXuatTraLaiNhaCungCapViewSet,
    PhieuXuatTraLaiNhaCungCapViewSet,
)

# Main router for PhieuXuatTraLaiNhaCungCap
router = DefaultRouter()
router.register(
    "", PhieuXuatTraLaiNhaCungCapViewSet, basename="phieu-xuat-tra-lai-nha-cung-cap"
)

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path("", include(router.urls)),
    # Nested routes for phieu-xuat-tra-lai-nha-cung-cap
    path(
        "<uuid:phieu_xuat_uuid>/",
        include(
            [
                # Chi tiet phieu xuat tra lai nha cung cap routes
                path(
                    "chi-tiet/",
                    ChiTietPhieuXuatTraLaiNhaCungCapViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="chi-tiet-phieu-xuat-tra-lai-nha-cung-cap-list",
                ),
                path(
                    "chi-tiet/<uuid:uuid>/",
                    ChiTietPhieuXuatTraLaiNhaCungCapViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="chi-tiet-phieu-xuat-tra-lai-nha-cung-cap-detail",
                ),
            ]
        ),
    ),
]
