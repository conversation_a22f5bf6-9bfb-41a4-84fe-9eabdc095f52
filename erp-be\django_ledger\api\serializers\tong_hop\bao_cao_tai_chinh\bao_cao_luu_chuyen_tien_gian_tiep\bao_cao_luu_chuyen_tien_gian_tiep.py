"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Bao Cao Lu<PERSON> Tien <PERSON> Tiep (Indirect Cash Flow Statement Report).
Validates all filter parameters from the cURL request and formats response data.
"""

from rest_framework import serializers


class BaoCaoLuuChuyenTienGianTiepRequestSerializer(serializers.Serializer):
    """
    Serializer for Bao Cao <PERSON> Tien Gian Tiep request parameters.
    Validates all filter parameters from the cURL request exactly as specified.

    """

    # Previous period date range (required) - YYYY-MM-DD format
    ngay_ct11 = serializers.DateField(
        required=True,
        help_text="Previous period start date (YYYY-MM-DD format)"
    )
    ngay_ct12 = serializers.DateField(
        required=True,
        help_text="Previous period end date (YYYY-MM-DD format)"
    )

    # Current period date range (required) - YYYY-MM-DD format
    ngay_ct01 = serializers.DateField(
        required=True,
        help_text="Current period start date (YYYY-MM-DD format)"
    )
    ngay_ct02 = serializers.DateField(
        required=True,
        help_text="Current period end date (YYYY-MM-DD format)"
    )

    # User filter (optional)
    nguoi_lap = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=100,
        help_text="User who created the report"
    )

    # Unit filter (optional)
    ma_unit = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Unit/Department code filter"
    )

    # Report template filter (optional)
    id_maubc = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        default="BCLTTGT01",
        help_text="Report template ID"
    )

    # Report format filter (optional)
    mau_bc = serializers.IntegerField(
        required=False,
        default=20,
        help_text="Report format type"
    )

    def validate_ngay_ct01(self, value):
        """Validate current period start date."""
        # DateField automatically handles date validation with input_formats
        return value

    def validate_ngay_ct02(self, value):
        """Validate current period end date."""
        # DateField automatically handles date validation with input_formats
        return value

    def validate_ngay_ct11(self, value):
        """Validate previous period start date."""
        # DateField automatically handles date validation with input_formats
        return value

    def validate_ngay_ct12(self, value):
        """Validate previous period end date."""
        # DateField automatically handles date validation with input_formats
        return value

    def validate(self, data):
        """Cross-field validation for date ranges."""
        # Get date objects (already parsed by DateField)
        ngay_ct01 = data.get('ngay_ct01')
        ngay_ct02 = data.get('ngay_ct02')
        ngay_ct11 = data.get('ngay_ct11')
        ngay_ct12 = data.get('ngay_ct12')

        # Validate current period date range
        if ngay_ct01 and ngay_ct02 and ngay_ct01 > ngay_ct02:
            raise serializers.ValidationError("Current period start date must be before end date")

        # Validate previous period date range
        if ngay_ct11 and ngay_ct12 and ngay_ct11 > ngay_ct12:
            raise serializers.ValidationError("Previous period start date must be before end date")

        return data


class BaoCaoLuuChuyenTienGianTiepResponseSerializer(serializers.Serializer):
    """
    Serializer for Bao Cao Luu Chuyen Tien Gian Tiep response data.
    Formats all response fields exactly as specified in the user requirements.

    Required response fields:
    ["stt_in", "ma_so", "chi_tieu", "thuyet_minh", "xchi_tieu", "xchi_tieu2",
     "ky_nay", "ky_truoc", "tk", "tk_du", "dau_cuoi"]
    """

    stt_in = serializers.IntegerField(
        help_text="Sequential number for display order"
    )
    ma_so = serializers.CharField(
        max_length=50,
        allow_blank=True,
        help_text="Financial indicator code"
    )
    chi_tieu = serializers.CharField(
        max_length=500,
        help_text="Financial indicator description"
    )
    thuyet_minh = serializers.CharField(
        max_length=100,
        allow_blank=True,
        help_text="Explanatory notes reference"
    )
    xchi_tieu = serializers.CharField(
        max_length=500,
        allow_blank=True,
        help_text="Extended indicator description"
    )
    xchi_tieu2 = serializers.CharField(
        max_length=500,
        allow_blank=True,
        help_text="Additional extended indicator description"
    )
    ky_nay = serializers.CharField(
        max_length=50,
        help_text="Current period amount"
    )
    ky_truoc = serializers.CharField(
        max_length=50,
        help_text="Previous period amount"
    )
    tk = serializers.CharField(
        max_length=100,
        allow_blank=True,
        help_text="Account codes used for calculation"
    )
    tk_du = serializers.CharField(
        max_length=10,
        allow_blank=True,
        help_text="Account balance type (C/D)"
    )
    dau_cuoi = serializers.CharField(
        max_length=10,
        allow_blank=True,
        help_text="Beginning/ending balance indicator"
    )
