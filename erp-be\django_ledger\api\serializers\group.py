from rest_framework import serializers
from django_ledger.models import GroupModel

class GroupModelSimpleSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for GroupModel to be used in nested representations.
    """
    class Meta:
        model = GroupModel
        fields = ['uuid', 'ma_nhom', 'ten_phan_nhom', 'loai_nhom']

class GroupModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = GroupModel
        fields = ['uuid', 'ma_nhom', 'ten_phan_nhom', 'ten2', 'entity_model', 'trang_thai', 'loai_nhom',
                  'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated']

