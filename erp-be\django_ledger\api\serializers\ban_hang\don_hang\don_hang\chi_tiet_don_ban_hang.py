"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Chi Tiet Don Ban Hang (Sales Order Detail) serializer implementation.
"""

from rest_framework import serializers

from django_ledger.models.ban_hang.don_hang.don_hang.chi_tiet_don_ban_hang import ChiTietDonBanHangModel
from django_ledger.models.ban_hang.don_hang.don_hang.don_ban_hang import DonBanHangModel


class ChiTietDonBanHangModelSerializer(serializers.ModelSerializer):
    """
    Chi Tiet Don Ban Hang (Sales Order Detail) serializer.
    """
    ref_data = serializers.SerializerMethodField()

    class Meta:
        model = ChiTietDonBanHangModel
        fields = [
            'uuid',
            'don_hang',
            'line',
            'ma_vt',
            'ten_vt0',
            'dvt',
            'ten_dvt0',
            'ten_dvt',
            'ma_kho',
            'ten_kho',
            'ma_lo',
            'ten_lo',
            'lo_yn',
            'ma_vi_tri',
            'ten_vi_tri',
            'vi_tri_yn',
            'he_so',
            'qc_yn',
            'so_luong',
            'ct_km',
            'ma_loai_gb',
            'gia_nt1',
            'gia_nt2',
            'tien_nt2',
            'ngay_giao',
            'tl_ck',
            'ck_nt',
            'ma_thue',
            'thue_suat',
            'thue_nt',
            'ma_bp',
            'ten_bp',
            'ma_vv',
            'ten_vv',
            'ma_hd',
            'ten_hd',
            'ma_dtt',
            'ten_dtt',
            'ma_ku',
            'ten_ku',
            'ma_phi',
            'ten_phi',
            'ma_sp',
            'ten_sp',
            'ma_lsx',
            'ma_cp0',
            'ten_cp0',
            'gia1',
            'gia2',
            'tien2',
            'ck',
            'thue',
            'sl_don_hang',
            'sl_hd',
            'sl_px',
            'id_hd',
            'line_hd',
            'id_bg',
            'line_bg',
            'ref_data',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'ref_data', 'created', 'updated']

    def get_ref_data(self, instance):
        """
        Get referenced data for the instance.
        """
        data = {}

        # Add referenced data for ma_vt (material)
        if instance.ma_vt:
            try:
                data['ma_vt'] = {
                    'uuid': str(instance.ma_vt.uuid),
                    'ma_vt': instance.ma_vt.ma_vt,
                    'ten_vt': instance.ma_vt.ten_vt,
                    'dvt': instance.ma_vt.dvt.ma_dvt if instance.ma_vt.dvt else None
                }
            except Exception as e:
                pass

        # Add referenced data for dvt (unit)
        if instance.dvt:
            try:
                data['dvt'] = {
                    'uuid': str(instance.dvt.uuid),
                    'ma_dvt': instance.dvt.ma_dvt,
                    'ten_dvt': instance.dvt.ten_dvt
                }
            except Exception as e:
                pass

        # Add referenced data for ma_kho (warehouse)
        if instance.ma_kho:
            try:
                data['ma_kho'] = {
                    'uuid': str(instance.ma_kho.uuid),
                    'ma_kho': instance.ma_kho.ma_kho,
                    'ten_kho': instance.ma_kho.ten_kho
                }
            except Exception as e:
                pass

        # Add referenced data for ma_lo (batch)
        if instance.ma_lo:
            try:
                data['ma_lo'] = {
                    'uuid': str(instance.ma_lo.uuid),
                    'ma_lo': instance.ma_lo.ma_lo,
                    'ten_lo': instance.ma_lo.ten_lo
                }
            except Exception as e:
                pass

        # Add referenced data for ma_vi_tri (position)
        if instance.ma_vi_tri:
            try:
                data['ma_vi_tri'] = {
                    'uuid': str(instance.ma_vi_tri.uuid),
                    'ma_vi_tri': instance.ma_vi_tri.ma_vi_tri,
                    'ten_vi_tri': instance.ma_vi_tri.ten_vi_tri
                }
            except Exception as e:
                pass

        # Add referenced data for ma_bp (department)
        if instance.ma_bp:
            try:
                data['ma_bp'] = {
                    'uuid': str(instance.ma_bp.uuid),
                    'ma_bp': instance.ma_bp.ma_bp,
                    'ten_bp': instance.ma_bp.ten_bp
                }
            except Exception as e:
                pass

        # Add referenced data for ma_vv (case)
        if instance.ma_vv:
            try:
                data['ma_vv'] = {
                    'uuid': str(instance.ma_vv.uuid),
                    'ma_vv': instance.ma_vv.ma_vv,
                    'ten_vv': instance.ma_vv.ten_vv
                }
            except Exception as e:
                pass

        # Add referenced data for ma_hd (contract)
        if instance.ma_hd:
            try:
                data['ma_hd'] = {
                    'uuid': str(instance.ma_hd.uuid),
                    'ma_hd': instance.ma_hd.contract_number,
                    'ten_hd': instance.ma_hd.contract_title
                }
            except Exception as e:
                pass

        # Add referenced data for ma_dtt (payment installment)
        if instance.ma_dtt:
            try:
                data['ma_dtt'] = {
                    'uuid': str(instance.ma_dtt.uuid),
                    'ma_dtt': instance.ma_dtt.ma_dtt,
                    'ten_dtt': instance.ma_dtt.ten_dtt
                }
            except Exception as e:
                pass

        # Add referenced data for ma_ku (loan)
        if instance.ma_ku:
            try:
                data['ma_ku'] = {
                    'uuid': str(instance.ma_ku.uuid),
                    'ma_ku': instance.ma_ku.ma_ku,
                    'ten_ku': instance.ma_ku.ten_ku
                }
            except Exception as e:
                pass

        # Add referenced data for ma_phi (fee)
        if instance.ma_phi:
            try:
                data['ma_phi'] = {
                    'uuid': str(instance.ma_phi.uuid),
                    'ma_phi': instance.ma_phi.ma_phi,
                    'ten_phi': instance.ma_phi.ten_phi
                }
            except Exception as e:
                pass

        # Add referenced data for ma_sp (product)
        if instance.ma_sp:
            try:
                data['ma_sp'] = {
                    'uuid': str(instance.ma_sp.uuid),
                    'ma_sp': instance.ma_sp.ma_vt,
                    'ten_sp': instance.ma_sp.ten_vt
                }
            except Exception as e:
                pass

        # Add referenced data for ma_cp0 (invalid expense)
        if instance.ma_cp0:
            try:
                data['ma_cp0'] = {
                    'uuid': str(instance.ma_cp0.uuid),
                    'ma_cp0': instance.ma_cp0.ma_cp,
                    'ten_cp0': instance.ma_cp0.ten_cp
                }
            except Exception as e:
                pass

        return data


