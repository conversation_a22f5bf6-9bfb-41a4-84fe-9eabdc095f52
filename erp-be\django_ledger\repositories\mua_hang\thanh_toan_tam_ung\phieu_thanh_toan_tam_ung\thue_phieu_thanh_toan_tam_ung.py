"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ThuePhieuThanhToanTamUng (Advance Payment Settlement Voucher Tax) Repository
"""

from django.db.models import QuerySet
from django.utils.translation import gettext_lazy as _

from django_ledger.models.mua_hang.thanh_toan_tam_ung import ThuePhieuThanhToanTamUngModel, PhieuThanhToanTamUngModel
from django_ledger.repositories.base import BaseRepository


class ThuePhieuThanhToanTamUngRepository(BaseRepository):
    """
    A repository class for the ThuePhieuThanhToanTamUngModel.
    """

    def __init__(self):
        self.model_class = ThuePhieuThanhToanTamUngModel

    def get_by_id(self, entity_slug: str, uuid: str, user_model) -> ThuePhieuThanhToanTamUngModel:
        """
        Get a ThuePhieuThanhToanTamUngModel by its UUID.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug to filter by.
        uuid: str
            The UUID of the ThuePhieuThanhToanTamUngModel to retrieve.
        user_model
            The authenticated Django UserModel making the request.

        Returns
        -------
        ThuePhieuThanhToanTamUngModel
            The requested ThuePhieuThanhToanTamUngModel instance.
        """
        return self.model_class.objects.get(uuid__exact=uuid)

    def list(self, entity_slug: str, user_model, phieu_thanh_toan_uuid=None, **kwargs) -> QuerySet:
        """
        Get a QuerySet of ThuePhieuThanhToanTamUngModel instances.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug to filter by.
        user_model
            The authenticated Django UserModel making the request.
        phieu_thanh_toan_uuid: str
            Optional UUID of the PhieuThanhToanTamUngModel to filter by.
        kwargs
            Additional filtering parameters.

        Returns
        -------
        QuerySet
            A QuerySet of ThuePhieuThanhToanTamUngModel instances.
        """
        qs = self.model_class.objects.all()
        if phieu_thanh_toan_uuid:
            qs = qs.filter(phieu_thanh_toan__uuid__exact=phieu_thanh_toan_uuid)
        return qs

    def create(self, entity_slug: str, user_model, phieu_thanh_toan_uuid=None, **kwargs) -> ThuePhieuThanhToanTamUngModel:
        """
        Create a new ThuePhieuThanhToanTamUngModel instance.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug to associate with the new instance.
        user_model
            The authenticated Django UserModel making the request.
        phieu_thanh_toan_uuid: str
            Optional UUID of the PhieuThanhToanTamUngModel to associate with the new instance.
        kwargs
            Additional parameters for the new instance.

        Returns
        -------
        ThuePhieuThanhToanTamUngModel
            The newly created ThuePhieuThanhToanTamUngModel instance.
        """
        if phieu_thanh_toan_uuid:
            phieu_thanh_toan = PhieuThanhToanTamUngModel.objects.get(uuid__exact=phieu_thanh_toan_uuid)
            kwargs['phieu_thanh_toan'] = phieu_thanh_toan
        
        # Convert UUIDs to model instances
        self.convert_uuids_to_model_instances(kwargs)
        
        instance = self.model_class(**kwargs)
        instance.save()
        return instance

    def update(self, entity_slug: str, user_model, instance: ThuePhieuThanhToanTamUngModel, **kwargs) -> ThuePhieuThanhToanTamUngModel:
        """
        Update an existing ThuePhieuThanhToanTamUngModel instance.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug to filter by.
        user_model
            The authenticated Django UserModel making the request.
        instance: ThuePhieuThanhToanTamUngModel
            The ThuePhieuThanhToanTamUngModel instance to update.
        kwargs
            Additional parameters to update.

        Returns
        -------
        ThuePhieuThanhToanTamUngModel
            The updated ThuePhieuThanhToanTamUngModel instance.
        """
        # Convert UUIDs to model instances
        self.convert_uuids_to_model_instances(kwargs)
        
        for key, value in kwargs.items():
            setattr(instance, key, value)
        instance.save()
        return instance

    def delete(self, entity_slug: str, user_model, instance: ThuePhieuThanhToanTamUngModel) -> bool:
        """
        Delete a ThuePhieuThanhToanTamUngModel instance.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug to filter by.
        user_model
            The authenticated Django UserModel making the request.
        instance: ThuePhieuThanhToanTamUngModel
            The ThuePhieuThanhToanTamUngModel instance to delete.

        Returns
        -------
        bool
            True if the instance was deleted, False otherwise.
        """
        instance.delete()
        return True

    def convert_uuids_to_model_instances(self, data: dict) -> None:
        """
        Convert UUID strings to model instances in the data dictionary.

        Parameters
        ----------
        data: dict
            The data dictionary to process.
        """
        from django_ledger.models import (
            AccountModel, CustomerModel, BoPhanModel, VuViecModel, 
            ContractModel, DotThanhToanModel, KheUocModel, PhiModel, 
            VatTuModel, ChiPhiKhongHopLeModel, PhieuThanhToanTamUngModel
        )
        
        if 'phieu_thanh_toan' in data and isinstance(data['phieu_thanh_toan'], str):
            try:
                data['phieu_thanh_toan'] = PhieuThanhToanTamUngModel.objects.get(uuid__exact=data['phieu_thanh_toan'])
            except PhieuThanhToanTamUngModel.DoesNotExist:
                pass
                
        if 'ma_kh' in data and isinstance(data['ma_kh'], str):
            try:
                data['ma_kh'] = CustomerModel.objects.get(uuid__exact=data['ma_kh'])
            except CustomerModel.DoesNotExist:
                pass
                
        if 'tk_thue_no' in data and isinstance(data['tk_thue_no'], str):
            try:
                data['tk_thue_no'] = AccountModel.objects.get(uuid__exact=data['tk_thue_no'])
            except AccountModel.DoesNotExist:
                pass
                
        if 'tk_du' in data and isinstance(data['tk_du'], str):
            try:
                data['tk_du'] = AccountModel.objects.get(uuid__exact=data['tk_du'])
            except AccountModel.DoesNotExist:
                pass
                
        if 'ma_kh9' in data and isinstance(data['ma_kh9'], str):
            try:
                data['ma_kh9'] = CustomerModel.objects.get(uuid__exact=data['ma_kh9'])
            except CustomerModel.DoesNotExist:
                pass
                
        if 'ma_bp' in data and isinstance(data['ma_bp'], str):
            try:
                data['ma_bp'] = BoPhanModel.objects.get(uuid__exact=data['ma_bp'])
            except BoPhanModel.DoesNotExist:
                pass
                
        if 'ma_vv' in data and isinstance(data['ma_vv'], str):
            try:
                data['ma_vv'] = VuViecModel.objects.get(uuid__exact=data['ma_vv'])
            except VuViecModel.DoesNotExist:
                pass
                
        if 'ma_hd' in data and isinstance(data['ma_hd'], str):
            try:
                data['ma_hd'] = ContractModel.objects.get(uuid__exact=data['ma_hd'])
            except ContractModel.DoesNotExist:
                pass
                
        if 'ma_dtt' in data and isinstance(data['ma_dtt'], str):
            try:
                data['ma_dtt'] = DotThanhToanModel.objects.get(uuid__exact=data['ma_dtt'])
            except DotThanhToanModel.DoesNotExist:
                pass
                
        if 'ma_ku' in data and isinstance(data['ma_ku'], str):
            try:
                data['ma_ku'] = KheUocModel.objects.get(uuid__exact=data['ma_ku'])
            except KheUocModel.DoesNotExist:
                pass
                
        if 'ma_phi' in data and isinstance(data['ma_phi'], str):
            try:
                data['ma_phi'] = PhiModel.objects.get(uuid__exact=data['ma_phi'])
            except PhiModel.DoesNotExist:
                pass
                
        if 'ma_sp' in data and isinstance(data['ma_sp'], str):
            try:
                data['ma_sp'] = VatTuModel.objects.get(uuid__exact=data['ma_sp'])
            except VatTuModel.DoesNotExist:
                pass
                
        if 'ma_cp0' in data and isinstance(data['ma_cp0'], str):
            try:
                data['ma_cp0'] = ChiPhiKhongHopLeModel.objects.get(uuid__exact=data['ma_cp0'])
            except ChiPhiKhongHopLeModel.DoesNotExist:
                pass
