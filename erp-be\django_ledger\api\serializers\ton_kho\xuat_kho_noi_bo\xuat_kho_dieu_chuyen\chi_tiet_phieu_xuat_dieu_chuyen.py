"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiTietPhieuXuatDieuChuyen (Warehouse Transfer Detail) serializer implementation.
"""

from rest_framework import serializers

from django_ledger.models.ton_kho.xuat_kho_noi_bo.xuat_kho_dieu_chuyen import ChiTietPhieuXuatDieuChuyenModel

# Import serializers for foreign key fields
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.warehouse import KhoHangModelSerializer
from django_ledger.api.serializers.vi_tri_kho_hang import ViTriKhoHangModelSerializer as ViTriModelSerializer
from django_ledger.api.serializers.lo import LoModelSerializer
from django_ledger.api.serializers.accounts import AccountModelSerializer
from django_ledger.api.serializers.nhap_xuat import NhapXuatModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.tien_do_thanh_toan import DotThanhToanModelSerializer
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import KheUocModelSerializer
from django_ledger.api.serializers.danh_muc import PhiSerializer, ChiPhiKhongHopLeSerializer


class ChiTietPhieuXuatDieuChuyenModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the ChiTietPhieuXuatDieuChuyenModel.

    This serializer handles the conversion between ChiTietPhieuXuatDieuChuyenModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields
    - Adds additional fields with "_data" suffix that contain the complete nested object data
    - When deserializing, accepts UUIDs for reference fields
    - All fields except phieu_xuat, ma_vt, ma_kho, ma_khon, so_luong, gia_nt, gia, tien_nt, tien are optional
    """
    # Add nested serializers for foreign key fields
    phieu_xuat_data = serializers.SerializerMethodField(read_only=True)
    ma_vt_data = VatTuSerializer(source='ma_vt', read_only=True)
    dvt_data = DonViTinhSerializer(source='dvt', read_only=True)
    ma_kho_data = KhoHangModelSerializer(source='ma_kho', read_only=True)
    ma_khon_data = KhoHangModelSerializer(source='ma_khon', read_only=True)
    ma_vi_tri_data = ViTriModelSerializer(source='ma_vi_tri', read_only=True)
    ma_vi_trin_data = ViTriModelSerializer(source='ma_vi_trin', read_only=True)
    ma_lo_data = LoModelSerializer(source='ma_lo', read_only=True)
    tk_vt_data = AccountModelSerializer(source='tk_vt', read_only=True)
    ma_nx_data = NhapXuatModelSerializer(source='ma_nx', read_only=True)
    tk_du_data = AccountModelSerializer(source='tk_du', read_only=True)
    ma_bp_data = BoPhanModelSerializer(source='ma_bp', read_only=True)
    ma_vv_data = VuViecModelSerializer(source='ma_vv', read_only=True)
    ma_hd_data = ContractModelSerializer(source='ma_hd', read_only=True)
    ma_dtt_data = DotThanhToanModelSerializer(source='ma_dtt', read_only=True)
    ma_ku_data = KheUocModelSerializer(source='ma_ku', read_only=True)
    ma_phi_data = PhiSerializer(source='ma_phi', read_only=True)
    ma_sp_data = VatTuSerializer(source='ma_sp', read_only=True)
    ma_cp0_data = ChiPhiKhongHopLeSerializer(source='ma_cp0', read_only=True)

    class Meta:
        model = ChiTietPhieuXuatDieuChuyenModel
        fields = [
            'uuid',
            'phieu_xuat',
            'phieu_xuat_data',
            'line',
            'ma_vt',
            'ma_vt_data',
            'dvt',
            'dvt_data',
            'ten_dvt',
            'ma_kho',
            'ma_kho_data',
            'ten_kho',
            'ma_khon',
            'ma_khon_data',
            'ten_khon',
            'ma_vi_tri',
            'ma_vi_tri_data',
            'ten_vi_tri',
            'ma_vi_trin',
            'ma_vi_trin_data',
            'ten_vi_trin',
            'vi_tri_yn',
            'vi_trin_yn',
            'ma_lo',
            'ma_lo_data',
            'ten_lo',
            'lo_yn',
            'he_so',
            'qc_yn',
            'so_luong',
            'px_dd',
            'gia_nt',
            'tien_nt',
            'tk_vt',
            'tk_vt_data',
            'ma_nx',
            'ma_nx_data',
            'tk_du',
            'tk_du_data',
            'ghi_chu',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_sp_data',
            'ma_lsx',
            'ma_cp0',
            'ma_cp0_data',
            'gia',
            'tien',
            'id_pn',
            'line_pn',
            'id_nhap',
            'line_nhap',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'phieu_xuat_data',
            'ma_vt_data',
            'dvt_data',
            'ma_kho_data',
            'ma_khon_data',
            'ma_vi_tri_data',
            'ma_vi_trin_data',
            'ma_lo_data',
            'tk_vt_data',
            'ma_nx_data',
            'tk_du_data',
            'ma_bp_data',
            'ma_vv_data',
            'ma_hd_data',
            'ma_dtt_data',
            'ma_ku_data',
            'ma_phi_data',
            'ma_sp_data',
            'ma_cp0_data',
            'created',
            'updated'
        ]

    def get_phieu_xuat_data(self, obj):
        """
        Get the phieu_xuat_data for the ChiTietPhieuXuatDieuChuyen.
        This is a method field that returns a minimal representation of the PhieuXuatDieuChuyen.

        Parameters
        ----------
        obj : ChiTietPhieuXuatDieuChuyenModel
            The ChiTietPhieuXuatDieuChuyenModel instance

        Returns
        -------
        dict
            Minimal representation of the PhieuXuatDieuChuyen
        """
        return {
            'uuid': str(obj.phieu_xuat.uuid),
            'so_ct': str(obj.phieu_xuat.so_ct),
            'dien_giai': obj.phieu_xuat.dien_giai
        }

    def create(self, validated_data):
        """
        Create a new ChiTietPhieuXuatDieuChuyen instance.
        Sets default values for optional fields if they are not provided.

        Parameters
        ----------
        validated_data : dict
            The validated data for creating the ChiTietPhieuXuatDieuChuyen

        Returns
        -------
        ChiTietPhieuXuatDieuChuyenModel
            The created ChiTietPhieuXuatDieuChuyen instance
        """
        # Set default values for optional fields if they are not provided
        if 'vi_tri_yn' not in validated_data:
            validated_data['vi_tri_yn'] = 0
        if 'vi_trin_yn' not in validated_data:
            validated_data['vi_trin_yn'] = 0
        if 'lo_yn' not in validated_data:
            validated_data['lo_yn'] = 0
        if 'he_so' not in validated_data:
            validated_data['he_so'] = 1
        if 'qc_yn' not in validated_data:
            validated_data['qc_yn'] = 0
        if 'px_dd' not in validated_data:
            validated_data['px_dd'] = 0
        if 'id_pn' not in validated_data:
            validated_data['id_pn'] = 0
        if 'line_pn' not in validated_data:
            validated_data['line_pn'] = 0
        if 'id_nhap' not in validated_data:
            validated_data['id_nhap'] = 0
        if 'line_nhap' not in validated_data:
            validated_data['line_nhap'] = 0

        return super().create(validated_data)
