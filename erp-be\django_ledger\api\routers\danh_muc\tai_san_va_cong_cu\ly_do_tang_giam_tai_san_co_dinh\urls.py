"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Ly Do Tang Giam Tai San Co Dinh (Fixed Asset Change Reason) module.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.danh_muc.tai_san_va_cong_cu.ly_do_tang_giam_tai_san_co_dinh import LyDoTangGiamTaiSanCoDinhViewSet

# Main router for LyDoTangGiamTaiSanCoDinh
router = DefaultRouter()
router.register('', LyDoTangGiamTaiSanCoDinhViewSet, basename='ly-do-tang-giam-tai-san-co-dinh')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
