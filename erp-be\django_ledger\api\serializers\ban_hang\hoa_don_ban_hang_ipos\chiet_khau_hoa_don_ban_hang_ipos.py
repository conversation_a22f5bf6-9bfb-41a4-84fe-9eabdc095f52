"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChietKhauHoaDonBanHangIPosModel.
"""
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from django_ledger.models import ChietKhauHoaDonBanHangIPosModel
from django_ledger.api.serializers.vat_tu import VatTuSerializer


class ChietKhauHoaDonBanHangIPosSerializer(serializers.ModelSerializer):
    """
    Serializer for ChietKhauHoaDonBanHangIPosModel.
    """
    # Read-only fields for related objects
    hoa_don_data = serializers.SerializerMethodField(read_only=True)
    ma_vt_tang_data = serializers.SerializerMethodField(read_only=True)
    ma_kho_tang_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChietKhauHoaDonBanHangIPosModel
        fields = [
            'uuid',
            'hoa_don',
            'hoa_don_data',
            # Basic information
            'id',
            'line',
            # Discount information
            'ma_ck',
            'ten_ck',
            'ma_lck',
            'id_ck',
            'line_ck',
            'tl_ck',
            't_tien_ck_tl',
            't_tien_ck',
            'tien_ck',
            'dvt_ck',
            # Gift information
            'tang_yn',
            'ma_vt_tang',
            'ma_vt_tang_data',
            'dvt_tang',
            'ten_dvt_tang',
            'ma_kho_tang',
            'ma_kho_tang_data',
            'ten_kho_tang',
            'sl_tang',
            'dt_tang',
            'dt_tang_val',
            # Other
            'tien_hang_yn',
            'xtype',
            'xkey',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'hoa_don_data',
            'ma_vt_tang_data',
            'ma_kho_tang_data',
            'created',
            'updated'
        ]

    def get_hoa_don_data(self, obj):
        """Get invoice data."""
        if obj.hoa_don:
            return {
                'uuid': str(obj.hoa_don.uuid),
                'so_ct': obj.hoa_don.so_ct,
                'ten_kh_thue': obj.hoa_don.ten_kh_thue
            }
        return None

    def get_ma_vt_tang_data(self, obj):
        """Get gift material data."""
        if obj.ma_vt_tang:
            return VatTuSerializer(obj.ma_vt_tang).data
        return None

    def get_ma_kho_tang_data(self, obj):
        """Get gift warehouse data."""
        if obj.ma_kho_tang:
            return {
                'uuid': str(obj.ma_kho_tang.uuid),
                'ma_kho': obj.ma_kho_tang.ma_kho,
                'ten_kho': getattr(obj.ma_kho_tang, 'ten_kho', None)
            }
        return None
