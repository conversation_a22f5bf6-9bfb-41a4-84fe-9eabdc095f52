"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for KenhBanHang (Sales Channel) model
"""

from rest_framework import serializers

from django_ledger.models.danh_muc.ban_hang.kenh_ban_hang import KenhBanHangModel
from django_ledger.models import DanhMucNguonDonModel, PhuongThucThanhToanModel, HinhThucThanhToanModel
from django_ledger.api.serializers.danh_muc_nguon_don import DanhMucNguonDonModelSerializer
from django_ledger.api.serializers.finance import PhuongThucThanhToanModelSerializer
from django_ledger.api.serializers.danh_muc.ban_hang.hinh_thuc_thanh_toan.hinh_thuc_thanh_toan import HinhThucThanhToanModelSerializer


class KenhBanHangModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the KenhBanHangModel (Sales Channel) model.

    This serializer handles the conversion between KenhBanHangModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - entity_model: Reference to the entity model (read-only)
    - ma_kbh: Sales channel code
    - ten_kbh: Name of the sales channel
    - ma_nguondon: Reference to the order source model
    - ten_nguondon: Name of the order source
    - ma_pttt: Reference to the payment method model
    - ma_httt: Payment form code
    - ma_cuahang: Store code
    - tl_hoahong: Commission rate
    - loai_kenh: Channel type
    - status: Status indicator (0=inactive, 1=active)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    """
    ma_nguondon_data = serializers.SerializerMethodField(read_only=True)
    ma_pttt_data = serializers.SerializerMethodField(read_only=True)
    ma_httt_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = KenhBanHangModel
        fields = [
            'uuid', 'entity_model', 'ma_kbh', 'ten_kbh',
            'ma_nguondon', 'ma_nguondon_data', 'ten_nguondon',
            'ma_pttt', 'ma_pttt_data', 'ma_httt', 'ma_httt_data', 'ma_cuahang',
            'tl_hoahong', 'loai_kenh', 'status',
            'created', 'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated', 'entity_model']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_kbh": "SHOPEE",
                "ten_kbh": "Shopee Official Store",
                "ma_nguondon": "123e4567-e89b-12d3-a456-************",
                "ten_nguondon": "Shopee",
                "ma_pttt": "123e4567-e89b-12d3-a456-************",
                "ma_httt": "123e4567-e89b-12d3-a456-************",
                "ma_cuahang": "STORE01",
                "tl_hoahong": 5.00,
                "loai_kenh": 1,
                "status": 1,
                "created": "2023-01-01T00:00:00Z",
                "updated": "2023-01-02T00:00:00Z"
            }
        }

    def get_ma_nguondon_data(self, obj):
        """
        Get detailed information about the order source
        """
        if obj.ma_nguondon:
            return {
                'uuid': obj.ma_nguondon.uuid,
                'ma_nguondon': obj.ma_nguondon.ma_nguondon,
                'ten_nguondon': obj.ma_nguondon.ten_nguondon
            }
        return None

    def get_ma_pttt_data(self, obj):
        """
        Get detailed information about the payment method
        """
        if obj.ma_pttt:
            return {
                'uuid': obj.ma_pttt.uuid,
                'ma_pttt': obj.ma_pttt.ma_pttt,
                'ten_pttt': obj.ma_pttt.ten_pttt
            }
        return None

    def get_ma_httt_data(self, obj):
        """
        Get detailed information about the payment form
        """
        if obj.ma_httt:
            return {
                'uuid': obj.ma_httt.uuid,
                'ma_httt': obj.ma_httt.ma_httt,
                'ten_httt': obj.ma_httt.ten_httt
            }
        return None

    def validate(self, data):
        """
        Validate the data before saving
        """
        # Ensure ma_kbh is uppercase
        if 'ma_kbh' in data:
            data['ma_kbh'] = data['ma_kbh'].upper()

        return data

    def create(self, validated_data):
        """
        Create a new KenhBanHangModel instance
        """
        # The service layer will handle the creation
        return KenhBanHangModel.objects.create(**validated_data)

    def update(self, instance, validated_data):
        """
        Update an existing KenhBanHangModel instance
        """
        # Update fields
        for field, value in validated_data.items():
            setattr(instance, field, value)

        # Auto-populate ten_nguondon if ma_nguondon is provided
        if 'ma_nguondon' in validated_data and validated_data['ma_nguondon']:
            instance.ten_nguondon = validated_data['ma_nguondon'].ten_nguondon

        instance.save()
        return instance
