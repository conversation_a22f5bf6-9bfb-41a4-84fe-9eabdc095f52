"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL configuration for KhaiBaoNhomNguoiSuDung API endpoints.
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.he_thong.tai_khoan.khai_bao_nhom_nguoi_su_dung import (
    KhaiBaoNhomNguoiSuDungViewSet,
    ChiTietKhaiBaoNhomNguoiSuDungViewSet
)

# Main router for KhaiBaoNhomNguoiSuDung
router = DefaultRouter()
router.register('', KhaiBaoNhomNguoiSuDungViewSet, basename='khai-bao-nhom-nguoi-su-dung')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),

    # Nested routes for khai-bao-nhom-nguoi-su-dung
    path('<uuid:khai_bao_uuid>/', include([
        # Chi tiet khai bao nhom nguoi su dung routes
        path('chi-tiet/', ChiTietKhaiBaoNhomNguoiSuDungViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-tiet-khai-bao-nhom-nguoi-su-dung-list'),

        path('chi-tiet/<uuid:uuid>/', ChiTietKhaiBaoNhomNguoiSuDungViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-tiet-khai-bao-nhom-nguoi-su-dung-detail'),
    ])),
]
