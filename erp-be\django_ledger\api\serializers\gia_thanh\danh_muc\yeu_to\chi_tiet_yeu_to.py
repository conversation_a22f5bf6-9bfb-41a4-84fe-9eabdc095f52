"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for YeuToChiTiet model.
"""
from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models import YeuToChiTietModel, EntityUnitModel, BoPhanModel
from django_ledger.api.serializers.unit import EntityUnitModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
# Removed import to break circular dependency


class YeuToChiTietModelSerializer(serializers.ModelSerializer):
    """
    Serializer for YeuToChiTiet model.
    """
    # Read-only fields for related objects
    ma_yt_data = serializers.SerializerMethodField(read_only=True)
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = YeuToChiTietModel
        fields = [
            'uuid',
            'ma_yt',
            'ma_yt_data',
            'unit_id',
            'unit_id_data',
            'line',
            'ma_bp',
            'ma_bp_data',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'ma_yt_data',
            'unit_id_data',
            'ma_bp_data',
            'created',
            'updated'
        ]

    def get_ma_yt_data(self, obj):
        """
        Get factor data.
        """
        if obj.ma_yt:
            # Return minimal data to avoid circular imports
            return {
                'uuid': obj.ma_yt.uuid,
                'ma_yt': obj.ma_yt.ma_yt,
                'ten_yt': obj.ma_yt.ten_yt
            }
        return None

    def get_unit_id_data(self, obj):
        """
        Get unit data.
        """
        if obj.unit_id:
            return EntityUnitModelSerializer(obj.unit_id).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None
