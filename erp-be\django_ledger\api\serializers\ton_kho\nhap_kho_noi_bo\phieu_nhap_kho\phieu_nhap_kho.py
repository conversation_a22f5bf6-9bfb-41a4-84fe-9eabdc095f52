"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the PhieuNhapKhoSerializer, which handles serialization
for the PhieuNhapKhoModel.
"""

from rest_framework import serializers

from django_ledger.models.ton_kho.nhap_kho_noi_bo.phieu_nhap_kho import PhieuNhapKhoModel
from django_ledger.api.serializers.ton_kho.nhap_kho_noi_bo.phieu_nhap_kho.chi_tiet_phieu_nhap_kho import ChiTietPhieuNhapKhoSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.quyen_chung_tu import QuyenChungTuListSerializer
from django_ledger.api.serializers.chung_tu import ChungTuSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer 


class PhieuNhapKhoSerializer(serializers.ModelSerializer):
    """
    Serializer for PhieuNhapKhoModel.
    """

    chi_tiet = ChiTietPhieuNhapKhoSerializer(many=True, required=False)

    # Reference data fields
    ma_kh_data = serializers.SerializerMethodField()
    ma_nk_data = serializers.SerializerMethodField()
    so_ct_data = serializers.SerializerMethodField()
    ma_nt_data = serializers.SerializerMethodField()

    class Meta:
        model = PhieuNhapKhoModel
        fields = [
            'uuid', 'entity_model', 'action', 'question_ids', 'ma_ngv', 'ma_gd', 'ma_kh', 'dien_giai',
            'unit_id', 'id_progress', 'xprogress', 'i_so_ct', 'ma_nk', 'so_ct', 'ngay_ct', 'ngay_lct',
            'xdatetime2', 'ma_nt', 'ty_gia', 'status', 'transfer_yn', 't_so_luong', 't_tien_nt',
            't_tien', 'xfile', 'created', 'updated', 'chi_tiet',
            'ma_kh_data', 'ma_nk_data', 'so_ct_data', 'ma_nt_data'
        ]
        read_only_fields = ['uuid', 'entity_model', 'created', 'updated']

    def get_ma_kh_data(self, obj):
        """
        Returns the customer data for the ma_kh field.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_ma_nk_data(self, obj):
        """
        Returns the document permission data for the ma_nk field.
        """
        if obj.ma_nk:
            return QuyenChungTuListSerializer(obj.ma_nk).data
        return None

    def get_so_ct_data(self, obj):
        """
        Returns the document data for the so_ct field.
        """
        if obj.so_ct:
            return ChungTuSerializer(obj.so_ct).data
        return None

    def get_ma_nt_data(self, obj):
        """
        Returns the currency data for the ma_nt field.
        """
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None
