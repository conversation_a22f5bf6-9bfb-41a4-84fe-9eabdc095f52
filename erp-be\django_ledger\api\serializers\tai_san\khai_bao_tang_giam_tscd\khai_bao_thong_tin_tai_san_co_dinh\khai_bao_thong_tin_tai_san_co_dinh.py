"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for KhaiBaoThongTinTaiSanCoDinh (Fixed Asset Information Declaration) model
"""

from rest_framework import serializers

from django_ledger.models import KhaiBaoThongTinTaiSanCoDinhModel
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer 
from django_ledger.api.serializers.group import GroupModelSerializer
from django_ledger.api.serializers.chung_tu import ChungTuSerializer
from django_ledger.api.serializers.tai_san.khai_bao_tang_giam_tscd.khai_bao_thong_tin_tai_san_co_dinh.chi_tiet_doi_tuong_hach_toan_tscd import ChiTietDoiTuongHachToanTSCDSerializer
from django_ledger.api.serializers.tai_san.khai_bao_tang_giam_tscd.khai_bao_thong_tin_tai_san_co_dinh.chi_tiet_phu_tung_kem_theo_tscd import ChiTietPhuTungKemTheoTSCDSerializer
from django_ledger.api.serializers.unit import  EntityUnitModelSerializer



class KhaiBaoThongTinTaiSanCoDinhSerializer(serializers.ModelSerializer):
    """
    Serializer for KhaiBaoThongTinTaiSanCoDinhModel
    """
    ma_nt_data = serializers.SerializerMethodField(read_only=True)
    tk_ts_data = serializers.SerializerMethodField(read_only=True)
    tk_kh_data = serializers.SerializerMethodField(read_only=True)
    tk_cp_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    so_ct_data = serializers.SerializerMethodField(read_only=True)
    nh_ts1_data = serializers.SerializerMethodField(read_only=True)
    nh_ts2_data = serializers.SerializerMethodField(read_only=True)
    nh_ts3_data = serializers.SerializerMethodField(read_only=True)
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    chi_tiet_doi_tuong_hach_toan_data = serializers.SerializerMethodField(read_only=True)
    chi_tiet_phu_tung_kem_theo_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = KhaiBaoThongTinTaiSanCoDinhModel
        fields = [
            'uuid',
            'ma_ts',
            'ten_ts',
            'ma_lts',
            'ngay_mua',
            'ngay_kh0',
            'so_ky_kh',
            'ngay_kh_kt',
            'so_luong',
            'dvt',
            'unit_id',
            'unit_id_data',
            'ma_nt',
            'ma_nt_data',
            'ty_gia',
            'tk_ts',
            'tk_ts_data',
            'tk_kh',
            'tk_kh_data',
            'tk_cp',
            'tk_cp_data',
            'ma_bp',
            'ma_bp_data',
            'kieu_kh',
            'ty_le_kh',
            'nguyen_gia_nt',
            'nguyen_gia',
            'gt_da_kh_nt',
            'gt_da_kh',
            'gt_cl_nt',
            'gt_cl',
            'gt_kh_ky_nt',
            'gt_kh_ky',
            'status',
            'so_hieu_ts',
            'so_ct',
            'so_ct_data',
            'ngay_ct',
            'ma_barcode',
            'nh_ts1',
            'nh_ts1_data',
            'nh_ts2',
            'nh_ts2_data',
            'nh_ts3',
            'nh_ts3_data',
            'ten_ts2',
            'nuoc_sx',
            'nam_sx',
            'ghi_chu',
            'chi_tiet_doi_tuong_hach_toan_data',
            'chi_tiet_phu_tung_kem_theo_data',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated']

    def get_unit_id_data(self, instance):
        """
        Get unit data
        """
        if instance.unit_id:
            return EntityUnitModelSerializer(instance.unit_id).data
        return None

    def get_ma_nt_data(self, instance):
        """
        Get currency data
        """
        if instance.ma_nt:
            return NgoaiTeSerializer(instance.ma_nt).data
        return None

    def get_tk_ts_data(self, instance):
        """
        Get asset account data
        """
        if instance.tk_ts:
            return AccountModelSerializer(instance.tk_ts).data
        return None

    def get_tk_kh_data(self, instance):
        """
        Get depreciation account data
        """
        if instance.tk_kh:
            return AccountModelSerializer(instance.tk_kh).data
        return None

    def get_tk_cp_data(self, instance):
        """
        Get expense account data
        """
        if instance.tk_cp:
            return AccountModelSerializer(instance.tk_cp).data
        return None

    def get_ma_bp_data(self, instance):
        """
        Get department data
        """
        if instance.ma_bp:
            return BoPhanModelSerializer(instance.ma_bp).data
        return None

    def get_so_ct_data(self, instance):
        """
        Get document data
        """
        if instance.so_ct:
            return ChungTuSerializer(instance.so_ct).data
        return None

    def get_nh_ts1_data(self, instance):
        """
        Get asset group 1 data
        """
        if instance.nh_ts1:
            return GroupModelSerializer(instance.nh_ts1).data
        return None

    def get_nh_ts2_data(self, instance):
        """
        Get asset group 2 data
        """
        if instance.nh_ts2:
            return GroupModelSerializer(instance.nh_ts2).data
        return None

    def get_nh_ts3_data(self, instance):
        """
        Get asset group 3 data
        """
        if instance.nh_ts3:
            return GroupModelSerializer(instance.nh_ts3).data
        return None

    def get_chi_tiet_doi_tuong_hach_toan_data(self, instance):
        """
        Get accounting object details data
        """
        queryset = instance.chi_tiet_doi_tuong_hach_toan.all()
        return ChiTietDoiTuongHachToanTSCDSerializer(queryset, many=True).data

    def get_chi_tiet_phu_tung_kem_theo_data(self, instance):
        """
        Get accessory details data
        """
        queryset = instance.chi_tiet_phu_tung_kem_theo.all()
        return ChiTietPhuTungKemTheoTSCDSerializer(queryset, many=True).data
