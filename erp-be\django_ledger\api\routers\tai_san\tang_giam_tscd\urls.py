"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Tang Giam TSCD (Fixed Asset Increase/Decrease) module.
"""

from django.urls import include, path

# URL patterns for the module
urlpatterns = [
    # Include bao_cao_chi_tiet_tang_giam_tscd URLs
    path(
        "bao-cao-chi-tiet-tang-giam-tscd/",
        include(
            "django_ledger.api.routers.tai_san.tang_giam_tscd.bao_cao_chi_tiet_tang_giam_tscd.urls"
        ),
    ),
    # Include bang_ke_tscd_het_khau_hao_con_su_dung URLs
    path(
        "bang-ke-tscd-het-khau-hao-con-su-dung/",
        include(
            "django_ledger.api.routers.tai_san.tang_giam_tscd.bang_ke_tscd_het_khau_hao_con_su_dung.urls"
        ),
    ),
    # Add other tang_giam_tscd-related URLs here
]
