"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietHoaDonBanHangIPosModel.
"""
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from django_ledger.models import ChiTietHoaDonBanHangIPosModel
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.accounts import AccountModelSerializer


class ChiTietHoaDonBanHangIPosSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietHoaDonBanHangIPosModel.
    """
    # Read-only fields for related objects
    hoa_don_data = serializers.SerializerMethodField(read_only=True)
    ma_vt_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)
    ma_kho_data = serializers.SerializerMethodField(read_only=True)
    ma_thue_data = serializers.SerializerMethodField(read_only=True)
    tk_dt_data = serializers.SerializerMethodField(read_only=True)
    tk_gv_data = serializers.SerializerMethodField(read_only=True)
    tk_vt_data = serializers.SerializerMethodField(read_only=True)
    tk_gg_data = serializers.SerializerMethodField(read_only=True)
    tk_ck_data = serializers.SerializerMethodField(read_only=True)
    tk_km_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_co_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_dtt_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_cp0_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietHoaDonBanHangIPosModel
        fields = [
            'uuid',
            'hoa_don',
            'hoa_don_data',
            # Basic information
            'id',
            'line',
            # Material information
            'ma_vt',
            'ma_vt_data',
            'dvt',
            'dvt_data',
            'ten_dvt',
            # Warehouse and lot information
            'ma_kho',
            'ma_kho_data',
            'ten_kho',
            'ma_lo',
            'ten_lo',
            'lo_yn',
            'ma_vi_tri',
            'ten_vi_tri',
            'vi_tri_yn',
            # Quantity and price information
            'so_luong',
            'he_so',
            'gia_nt',
            'gia_nt1',
            'gia_nt2',
            'gia',
            'gia1',
            'gia2',
            'tien_nt',
            'tien_nt2',
            'tien',
            'tien2',
            # Discount and reduction information
            'tl_ck',
            'ck_nt',
            'ck',
            'gg_nt',
            'gg',
            # Tax information
            'ma_thue',
            'ma_thue_data',
            'ten_thue',
            'thue_suat',
            'thue_nt',
            'thue',
            # Account information
            'tk_dt',
            'tk_dt_data',
            'ten_tk_dt',
            'tk_gv',
            'tk_gv_data',
            'ten_tk_gv',
            'tk_vt',
            'tk_vt_data',
            'ten_tk_vt',
            'tk_gg',
            'tk_gg_data',
            'ten_tk_gg',
            'tk_ck',
            'tk_ck_data',
            'ten_tk_ck',
            'tk_km',
            'tk_km_data',
            'ten_tk_km',
            'tk_thue_co',
            'tk_thue_co_data',
            'ten_tk_thue_co',
            # Allocation information
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_sp_data',
            'ma_lsx',
            'ma_cp0',
            'ma_cp0_data',
            # Export and order information
            'px_dd',
            'sl_px',
            'id_px',
            'line_px',
            'id_dh',
            'line_dh',
            # POS information
            'pos_ma_vt',
            'pos_gia2',
            'pos_tien_ck',
            'pos_tien_gg',
            # Other
            'ct_km',
            'ghi_chu',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'hoa_don_data',
            'ma_vt_data',
            'dvt_data',
            'ma_kho_data',
            'ma_thue_data',
            'tk_dt_data',
            'tk_gv_data',
            'tk_vt_data',
            'tk_gg_data',
            'tk_ck_data',
            'tk_km_data',
            'tk_thue_co_data',
            'ma_bp_data',
            'ma_vv_data',
            'ma_hd_data',
            'ma_dtt_data',
            'ma_ku_data',
            'ma_phi_data',
            'ma_sp_data',
            'ma_cp0_data',
            'created',
            'updated'
        ]

    def get_hoa_don_data(self, obj):
        """Get invoice data."""
        if obj.hoa_don:
            return {
                'uuid': str(obj.hoa_don.uuid),
                'so_ct': obj.hoa_don.so_ct,
                'ten_kh_thue': obj.hoa_don.ten_kh_thue
            }
        return None

    def get_ma_vt_data(self, obj):
        """Get material data."""
        if obj.ma_vt:
            return VatTuSerializer(obj.ma_vt).data
        return None

    def get_dvt_data(self, obj):
        """Get unit data."""
        if obj.dvt:
            return {
                'uuid': str(obj.dvt.uuid),
                'ma_dvt': obj.dvt.ma_dvt,
                'ten_dvt': getattr(obj.dvt, 'ten_dvt', None)
            }
        return None

    def get_ma_kho_data(self, obj):
        """Get warehouse data."""
        if obj.ma_kho:
            return {
                'uuid': str(obj.ma_kho.uuid),
                'ma_kho': obj.ma_kho.ma_kho,
                'ten_kho': getattr(obj.ma_kho, 'ten_kho', None)
            }
        return None

    def get_ma_thue_data(self, obj):
        """Get tax data."""
        if obj.ma_thue:
            return {
                'uuid': str(obj.ma_thue.uuid),
                'ma_thue': obj.ma_thue.ma_thue,
                'ten_thue': getattr(obj.ma_thue, 'ten_thue', None),
                'thue_suat': getattr(obj.ma_thue, 'thue_suat', None)
            }
        return None

    def get_tk_dt_data(self, obj):
        """Get revenue account data."""
        if obj.tk_dt:
            return AccountModelSerializer(obj.tk_dt).data
        return None

    def get_tk_gv_data(self, obj):
        """Get cost account data."""
        if obj.tk_gv:
            return AccountModelSerializer(obj.tk_gv).data
        return None

    def get_tk_vt_data(self, obj):
        """Get material account data."""
        if obj.tk_vt:
            return AccountModelSerializer(obj.tk_vt).data
        return None

    def get_tk_gg_data(self, obj):
        """Get reduction account data."""
        if obj.tk_gg:
            return AccountModelSerializer(obj.tk_gg).data
        return None

    def get_tk_ck_data(self, obj):
        """Get discount account data."""
        if obj.tk_ck:
            return AccountModelSerializer(obj.tk_ck).data
        return None

    def get_tk_km_data(self, obj):
        """Get promotion account data."""
        if obj.tk_km:
            return AccountModelSerializer(obj.tk_km).data
        return None

    def get_tk_thue_co_data(self, obj):
        """Get tax credit account data."""
        if obj.tk_thue_co:
            return AccountModelSerializer(obj.tk_thue_co).data
        return None

    def get_ma_bp_data(self, obj):
        """Get department data."""
        if obj.ma_bp:
            return {
                'uuid': str(obj.ma_bp.uuid),
                'ma_bp': obj.ma_bp.ma_bp,
                'ten_bp': getattr(obj.ma_bp, 'ten_bp', None)
            }
        return None

    def get_ma_vv_data(self, obj):
        """Get case data."""
        if obj.ma_vv:
            return {
                'uuid': str(obj.ma_vv.uuid),
                'ma_vv': obj.ma_vv.ma_vv,
                'ten_vv': getattr(obj.ma_vv, 'ten_vv', None)
            }
        return None

    def get_ma_hd_data(self, obj):
        """Get contract data."""
        if obj.ma_hd:
            return {
                'uuid': str(obj.ma_hd.uuid),
                'ma_hd': obj.ma_hd.ma_hd,
                'ten_hd': getattr(obj.ma_hd, 'ten_hd', None)
            }
        return None

    def get_ma_dtt_data(self, obj):
        """Get payment term data."""
        if obj.ma_dtt:
            return {
                'uuid': str(obj.ma_dtt.uuid),
                'ma_dtt': obj.ma_dtt.ma_dtt,
                'ten_dtt': getattr(obj.ma_dtt, 'ten_dtt', None)
            }
        return None

    def get_ma_ku_data(self, obj):
        """Get agreement data."""
        if obj.ma_ku:
            return {
                'uuid': str(obj.ma_ku.uuid),
                'ma_ku': obj.ma_ku.ma_ku,
                'ten_ku': getattr(obj.ma_ku, 'ten_ku', None)
            }
        return None

    def get_ma_phi_data(self, obj):
        """Get fee data."""
        if obj.ma_phi:
            return {
                'uuid': str(obj.ma_phi.uuid),
                'ma_phi': obj.ma_phi.ma_phi,
                'ten_phi': getattr(obj.ma_phi, 'ten_phi', None)
            }
        return None

    def get_ma_sp_data(self, obj):
        """Get product data."""
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_cp0_data(self, obj):
        """Get invalid cost data."""
        if obj.ma_cp0:
            return {
                'uuid': str(obj.ma_cp0.uuid),
                'ma_cp0': obj.ma_cp0.ma_cp0,
                'ten_cp0': getattr(obj.ma_cp0, 'ten_cp0', None)
            }
        return None
