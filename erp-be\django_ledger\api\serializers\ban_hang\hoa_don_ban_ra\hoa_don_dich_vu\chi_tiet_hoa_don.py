"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietHoaDon model.
"""

from rest_framework import serializers

from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_dich_vu import ChiTietHoaDonModel
from django_ledger.models.dich_vu import DichVuModel
from django_ledger.models.accounts import AccountModel
from django_ledger.models.don_vi_tinh import DonViTinhModel
from django_ledger.models.tax import TaxModel
from django_ledger.models.bo_phan import BoPhanModel
from django_ledger.models.vu_viec import VuViecModel
from django_ledger.models.contract import ContractModel
from django_ledger.models.danh_muc import DotThanhToanModel
from django_ledger.models.danh_muc import KheUocModel
from django_ledger.models.danh_muc import PhiModel


class ChiTietHoaDonSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietHoaDonModel.
    """

    class Meta:
        model = ChiTietHoaDonModel
        fields = [
            'uuid',
            'hoa_don',
            'line',
            'ma_dv',
            'tk_dt',
            'dvt',
            'so_luong',
            'gia_nt2',
            'tien_nt2',
            'dien_giai',
            'tl_ck',
            'ck_nt',
            'tk_ck',
            'ten_tk_ck',
            'ma_thue',
            'thue_suat',
            'tk_thue_co',
            'ten_tk_thue_co',
            'thue_nt',
            'gia2',
            'tien2',
            'ck',
            'thue',
            'ma_bp',
            'ma_vv',
            'ma_hd',
            'ma_dtt',
            'ma_ku',
            'ma_phi',
            'ma_sp',
            'ma_lsx',
            'ma_cp0',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated']
        swagger_schema_fields = {
            'title': 'ChiTietHoaDon',
            'description': 'Chi tiết hóa đơn dịch vụ model serializer'
        }


class ChiTietHoaDonNestedSerializer(serializers.ModelSerializer):
    """
    Nested serializer for ChiTietHoaDonModel.
    """

    # Reference data fields
    ma_dv_data = serializers.SerializerMethodField(read_only=True)
    tk_dt_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)
    ma_thue_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_co_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_dtt_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietHoaDonModel
        fields = [
            'uuid',
            'line',
            'ma_dv',
            'ma_dv_data',
            'tk_dt',
            'tk_dt_data',
            'dvt',
            'dvt_data',
            'so_luong',
            'gia_nt2',
            'tien_nt2',
            'dien_giai',
            'tl_ck',
            'ck_nt',
            'tk_ck',
            'ten_tk_ck',
            'ma_thue',
            'ma_thue_data',
            'thue_suat',
            'tk_thue_co',
            'tk_thue_co_data',
            'ten_tk_thue_co',
            'thue_nt',
            'gia2',
            'tien2',
            'ck',
            'thue',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_lsx',
            'ma_cp0',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated']

    def get_ma_dv_data(self, obj):
        """Return service data if available"""
        try:
            dich_vu = DichVuModel.objects.get(ma_dv=obj.ma_dv)
            return {
                'uuid': str(dich_vu.uuid),
                'ma_dv': dich_vu.ma_dv,
                'ten_dv': dich_vu.ten_dv
            }
        except DichVuModel.DoesNotExist:
            return None

    def get_tk_dt_data(self, obj):
        """Return revenue account data if available"""
        try:
            account = AccountModel.objects.get(code=obj.tk_dt)
            return {
                'uuid': str(account.uuid),
                'code': account.code,
                'name': account.name
            }
        except AccountModel.DoesNotExist:
            return None

    def get_dvt_data(self, obj):
        """Return unit data if available"""
        try:
            dvt = DonViTinhModel.objects.get(ma_dvt=obj.dvt)
            return {
                'uuid': str(dvt.uuid),
                'ma_dvt': dvt.ma_dvt,
                'ten_dvt': dvt.ten_dvt
            }
        except DonViTinhModel.DoesNotExist:
            return None

    def get_ma_thue_data(self, obj):
        """Return tax data if available"""
        try:
            tax = TaxModel.objects.get(ma_thue=obj.ma_thue)
            return {
                'uuid': str(tax.uuid),
                'ma_thue': tax.ma_thue,
                'ten_thue': tax.ten_thue
            }
        except TaxModel.DoesNotExist:
            return None

    def get_tk_thue_co_data(self, obj):
        """Return tax account data if available"""
        try:
            account = AccountModel.objects.get(code=obj.tk_thue_co)
            return {
                'uuid': str(account.uuid),
                'code': account.code,
                'name': account.name
            }
        except AccountModel.DoesNotExist:
            return None

    def get_ma_bp_data(self, obj):
        """Return department data if available"""
        try:
            bo_phan = BoPhanModel.objects.get(ma_bp=obj.ma_bp)
            return {
                'uuid': str(bo_phan.uuid),
                'ma_bp': bo_phan.ma_bp,
                'ten_bp': bo_phan.ten_bp
            }
        except BoPhanModel.DoesNotExist:
            return None

    def get_ma_vv_data(self, obj):
        """Return case data if available"""
        try:
            vu_viec = VuViecModel.objects.get(ma_vv=obj.ma_vv)
            return {
                'uuid': str(vu_viec.uuid),
                'ma_vv': vu_viec.ma_vv,
                'ten_vv': vu_viec.ten_vv
            }
        except VuViecModel.DoesNotExist:
            return None

    def get_ma_hd_data(self, obj):
        """Return contract data if available"""
        try:
            contract = ContractModel.objects.get(ma_hd=obj.ma_hd)
            return {
                'uuid': str(contract.uuid),
                'ma_hd': contract.ma_hd,
                'ten_hd': contract.ten_hd
            }
        except ContractModel.DoesNotExist:
            return None

    def get_ma_dtt_data(self, obj):
        """Return payment period data if available"""
        try:
            dot_thanh_toan = DotThanhToanModel.objects.get(ma_dtt=obj.ma_dtt)
            return {
                'uuid': str(dot_thanh_toan.uuid),
                'ma_dtt': dot_thanh_toan.ma_dtt,
                'ten_dtt': dot_thanh_toan.ten_dtt
            }
        except DotThanhToanModel.DoesNotExist:
            return None

    def get_ma_ku_data(self, obj):
        """Return agreement data if available"""
        try:
            khe_uoc = KheUocModel.objects.get(ma_ku=obj.ma_ku)
            return {
                'uuid': str(khe_uoc.uuid),
                'ma_ku': khe_uoc.ma_ku,
                'ten_ku': khe_uoc.ten_ku
            }
        except KheUocModel.DoesNotExist:
            return None

    def get_ma_phi_data(self, obj):
        """Return fee data if available"""
        try:
            phi = PhiModel.objects.get(ma_phi=obj.ma_phi)
            return {
                'uuid': str(phi.uuid),
                'ma_phi': phi.ma_phi,
                'ten_phi': phi.ten_phi
            }
        except PhiModel.DoesNotExist:
            return None
