"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuXuatKho (Warehouse Export) router implementation.
"""

from django.urls import path

from django_ledger.api.views.ton_kho.xuat_kho_noi_bo.phieu_xuat_kho import PhieuXuatKhoViewSet, ChiTietPhieuXuatKhoViewSet

urlpatterns = [
    # PhieuXuatKho routes
    path('', PhieuXuatKhoViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='phieu-xuat-kho-list'),

    path('<uuid:pk>/', PhieuXuatKhoViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='phieu-xuat-kho-detail'),

    # Nested routes for ChiTietPhieuXuatKho
    path('<uuid:phieu_xuat_kho_uuid>/chi-tiet/', ChiTietPhieuXuatKhoViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='chi-tiet-phieu-xuat-kho-list'),

    path('<uuid:phieu_xuat_kho_uuid>/chi-tiet/<uuid:uuid>/', ChiTietPhieuXuatKhoViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='chi-tiet-phieu-xuat-kho-detail'),
]
