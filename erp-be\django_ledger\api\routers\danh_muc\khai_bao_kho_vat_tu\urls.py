"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Khai Bao Kho Vat Tu (Inventory Declaration) module.
"""

from django.urls import path, include

# URL patterns for the module
urlpatterns = [
    # Include quy_doi_don_vi_tinh_chi_tiet URLs
    path('quy-doi-don-vi-tinh-chi-tiet/', include('django_ledger.api.routers.danh_muc.khai_bao_kho_vat_tu.quy_doi_don_vi_tinh_chi_tiet.urls')),
    
    # Include thong_tin_vat_tu_theo_don_vi URLs
    path('thong-tin-vat-tu-theo-don-vi/', include('django_ledger.api.routers.danh_muc.khai_bao_kho_vat_tu.thong_tin_vat_tu_theo_don_vi.urls')),
    
    # Include khai_bao_ma_hang_ipos URLs
    path('khai-bao-ma-hang-ipos/', include('django_ledger.api.routers.danh_muc.khai_bao_kho_vat_tu.khai_bao_ma_hang_ipos.urls')),
    
    # Include quy_doi_don_vi_tinh_chung URLs
    path('quy-doi-don-vi-tinh-chung/', include('django_ledger.api.routers.danh_muc.khai_bao_kho_vat_tu.quy_doi_don_vi_tinh_chung.urls')),
    
    # Add other khai_bao_kho_vat_tu-related URLs here
]
