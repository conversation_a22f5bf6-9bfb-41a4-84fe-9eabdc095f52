"""
Django <PERSON>ger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Bang Ke Hoa Don Chung Tu Hang Hoa Dich Vu Ban Ra (Tax Invoice Report) API.
"""

from rest_framework import serializers
from decimal import Decimal
from datetime import date


class BangKeHoaDonChungTuHangHoaDichVuBanRaRequestSerializer(serializers.Serializer):
    """
    Serializer for Bang Ke Hoa Don Chung Tu Hang Hoa Dich Vu Ban Ra request parameters.
    Validates all filter parameters from the cURL request.
    """
    
    # Period filters (required)
    ky_tu = serializers.IntegerField(
        required=True,
        min_value=1,
        max_value=12,
        help_text="Period from (month 1-12)"
    )
    ky_den = serializers.IntegerField(
        required=True,
        min_value=1,
        max_value=12,
        help_text="Period to (month 1-12)"
    )
    nam = serializers.IntegerField(
        required=True,
        min_value=2000,
        max_value=2100,
        help_text="Year (YYYY)"
    )
    
    # Tax code filter
    ma_thue = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Tax code filter"
    )
    
    # Tax account filters
    tk_thue = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=255,
        help_text="Tax account codes (comma-separated)"
    )
    tk_du = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Reserve account code"
    )
    
    # Customer filters
    ma_kh = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Customer code filter"
    )
    ma_kh9 = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Customer code 9 filter"
    )
    
    # Warehouse filter
    ma_kho = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Warehouse code filter"
    )
    
    # Classification filter
    phan_loai = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=10,
        help_text="Classification filter"
    )
    
    # Unit filter
    ma_unit = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Unit code filter"
    )
    
    # Report template
    mau_bc = serializers.IntegerField(
        required=False,
        default=20,
        help_text="Report template number"
    )

    def validate(self, data):
        """
        Validate the period range.
        """
        ky_tu = data.get('ky_tu')
        ky_den = data.get('ky_den')
        
        if ky_tu and ky_den and ky_tu > ky_den:
            raise serializers.ValidationError(
                "Period from (ky_tu) must be less than or equal to period to (ky_den)"
            )
        
        return data


class BangKeHoaDonChungTuHangHoaDichVuBanRaResponseSerializer(serializers.Serializer):
    """
    Serializer for Bang Ke Hoa Don Chung Tu Hang Hoa Dich Vu Ban Ra response data.
    Defines all fields that should be returned in the report.
    """
    
    stt = serializers.IntegerField(help_text="Sequential number")
    id = serializers.CharField(max_length=255, help_text="Record ID")
    line = serializers.IntegerField(help_text="Line number")
    so_ct2 = serializers.CharField(max_length=50, help_text="Document number 2")
    ngay_ct = serializers.DateField(help_text="Document date")
    ngay_lct = serializers.DateField(help_text="Document creation date")
    ma_ct = serializers.CharField(max_length=50, help_text="Document code")
    so_ct = serializers.CharField(max_length=50, help_text="Document number")
    ma_nt = serializers.CharField(max_length=10, help_text="Currency code")
    ma_so_thue = serializers.CharField(max_length=50, help_text="Tax identification number")
    ten_vt_thue = serializers.CharField(max_length=255, help_text="Tax item name")
    t_tien2 = serializers.DecimalField(max_digits=15, decimal_places=3, help_text="Total amount 2")
    t_thue = serializers.DecimalField(max_digits=15, decimal_places=3, help_text="Total tax")
    thue_suat = serializers.DecimalField(max_digits=5, decimal_places=3, help_text="Tax rate")
    ma_thue = serializers.CharField(max_length=50, help_text="Tax code")
    nhom_thue = serializers.CharField(max_length=50, help_text="Tax group")
    ten_unit = serializers.CharField(max_length=255, help_text="Unit name")
    ten_unit2 = serializers.CharField(max_length=255, help_text="Unit name 2")
    ma_ct0 = serializers.CharField(max_length=50, help_text="Document code 0")
    ten_kh = serializers.CharField(max_length=255, help_text="Customer name")
    ghi_chu = serializers.CharField(max_length=500, help_text="Notes")
    ten_thue = serializers.CharField(max_length=255, help_text="Tax name")
    ten_thue2 = serializers.CharField(max_length=255, help_text="Tax name 2")
    ten_nh = serializers.CharField(max_length=255, help_text="Bank name")
    ten_nh2 = serializers.CharField(max_length=255, help_text="Bank name 2")
    ma_mau_ct = serializers.CharField(max_length=50, help_text="Document template code")
    stt2 = serializers.IntegerField(help_text="Sequential number 2")
    ten_kh2 = serializers.CharField(max_length=255, help_text="Customer name 2")
    xten_kh = serializers.CharField(max_length=255, help_text="Extended customer name")
    xten_kh2 = serializers.CharField(max_length=255, help_text="Extended customer name 2")
    xid = serializers.CharField(max_length=255, help_text="Extended ID")
