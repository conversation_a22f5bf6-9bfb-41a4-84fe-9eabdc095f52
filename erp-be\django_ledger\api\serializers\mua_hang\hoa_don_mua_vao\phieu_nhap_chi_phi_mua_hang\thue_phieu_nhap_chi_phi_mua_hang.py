"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ThuePhieuNhapChiPhiMuaHangSerializer, which handles serialization
for the ThuePhieuNhapChiPhiMuaHangModel.
"""

from rest_framework import serializers

from django_ledger.models.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import ThuePhieuNhapChiPhiMuaHangModel


class ThuePhieuNhapChiPhiMuaHangSerializer(serializers.ModelSerializer):
    """
    Serializer for ThuePhieuNhapChiPhiMuaHangModel.
    """

    # Reference data fields
    ma_thue_data = serializers.SerializerMethodField()
    ma_kh_data = serializers.SerializerMethodField()
    tk_thue_no_data = serializers.SerializerMethodField()

    class Meta:
        model = ThuePhieuNhapChiPhiMuaHangModel
        fields = [
            'uuid', 'phieu_nhap', 'line', 'so_ct0', 'so_ct2', 'ngay_ct0', 'ma_thue', 'thue_suat',
            'ma_kh', 'ten_kh_thue', 't_tien_nt', 't_tien', 'tk_thue_no', 'ten_tk_thue_no',
            'ma_thue_data', 'ma_kh_data', 'tk_thue_no_data'
        ]
        read_only_fields = ['uuid', 'phieu_nhap']

    def get_ma_thue_data(self, obj):
        """
        Returns the tax data for the ma_thue field.
        """
        if obj.ma_thue:
            return {
                'uuid': obj.ma_thue.uuid,
                'ma_thue': obj.ma_thue.ma_thue,
                'ten_thue': obj.ma_thue.ten_thue
            }
        return None

    def get_ma_kh_data(self, obj):
        """
        Returns the customer data for the ma_kh field.
        """
        if obj.ma_kh:
            return {
                'uuid': obj.ma_kh.uuid,
                'ma_kh': obj.ma_kh.ma_kh,
                'ten_kh': obj.ma_kh.ten_kh
            }
        return None

    def get_tk_thue_no_data(self, obj):
        """
        Returns the account data for the tk_thue_no field.
        """
        if obj.tk_thue_no:
            return {
                'uuid': obj.tk_thue_no.uuid,
                'code': obj.tk_thue_no.code,
                'name': obj.tk_thue_no.name
            }
        return None


