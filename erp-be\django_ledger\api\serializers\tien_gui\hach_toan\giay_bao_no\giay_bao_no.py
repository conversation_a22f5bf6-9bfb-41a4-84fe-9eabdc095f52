"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for BankTransferDocument model.
"""
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from django_ledger.models import BankTransferDocumentModel
from django_ledger.api.serializers.unit import EntityUnitModelSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.bank_account import BankAccountModelSerializer
from django_ledger.api.serializers.ngan_hang import NganHangModelSerializer
from django_ledger.api.serializers.account import AccountModelSerializer


class BankTransferDocumentSerializer(serializers.ModelSerializer):
    """
    Serializer for BankTransferDocument model.
    """
    # Read-only fields for related objects
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    tk_data = serializers.SerializerMethodField(read_only=True)
    tknh_data = serializers.SerializerMethodField(read_only=True)
    ma_ngan_hang_data = serializers.SerializerMethodField(read_only=True)

    # Child data
    chi_tiet_data = serializers.SerializerMethodField(read_only=True)
    thue_data = serializers.SerializerMethodField(read_only=True)
    phi_ngan_hang_data = serializers.SerializerMethodField(read_only=True)

    # Write-only fields for creating/updating child records
    chi_tiet = serializers.ListField(required=False, write_only=True)
    thue = serializers.ListField(required=False, write_only=True)
    phi_ngan_hang = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = BankTransferDocumentModel
        fields = [
            'uuid',
            'entity_model',
            'ma_ngv',
            'dia_chi',
            'ong_ba',
            'dien_giai',
            'tk',
            'tk_data',
            'i_so_ct',
            'ngay_ct',
            'ngay_lct',
            'ty_gia',
            'status',
            'transfer_yn',
            'hd_yn',
            'tg_dd',
            'cltg_yn',
            'ngay_ct0',
            'lenh_ct_yn',
            'ten_kh',
            'loai_lenh',
            'stk_kh',
            'ten_nh',
            'chi_nhanh_nh',
            'tinh_thanh_nh',
            'dien_giai_nh',
            'ma_lenh_nh',
            'tt_lenh_nh',
            'phi_nhan_yn',
            'dien_giai_ct_goc',
            't_tien_nt',
            't_tien',
            't_cp_nt',
            't_cp',
            't_thue_nt',
            't_thue',
            'unit_id',
            'unit_id_data',
            'ma_nt',
            'ma_nt_data',
            'ma_nk',
            'so_ct',
            'ma_tt',
            'ma_kh',
            'ma_kh_data',
            'tknh',
            'tknh_data',
            'so_ct0',
            'so_ct_goc',
            'ma_kh0',
            'ma_ngan_hang',
            'ma_ngan_hang_data',
            'chi_tiet_data',
            'thue_data',
            'phi_ngan_hang_data',
            'chi_tiet',
            'thue',
            'phi_ngan_hang',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'unit_id_data',
            'ma_nt_data',
            'ma_kh_data',
            'tk_data',
            'tknh_data',
            'ma_ngan_hang_data',
            't_tien_nt',
            't_tien',
            't_cp_nt',
            't_cp',
            't_thue_nt',
            't_thue',
            'chi_tiet_data',
            'thue_data',
            'phi_ngan_hang_data',
            'created',
            'updated'
        ]

    def get_unit_id_data(self, obj):
        """
        Get entity unit data.
        """
        if obj.unit_id:
            return EntityUnitModelSerializer(obj.unit_id).data
        return None

    def get_ma_nt_data(self, obj):
        """
        Get currency data.
        """
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_ma_kh_data(self, obj):
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_tk_data(self, obj):
        """
        Get account data.
        """
        if obj.tk:
            return AccountModelSerializer(obj.tk).data
        return None

    def get_tknh_data(self, obj):
        """
        Get bank account data.
        """
        if obj.tknh:
            return BankAccountModelSerializer(obj.tknh).data
        return None

    def get_ma_ngan_hang_data(self, obj):
        """
        Get bank data.
        """
        if obj.ma_ngan_hang:
            return NganHangModelSerializer(obj.ma_ngan_hang).data
        return None

    def get_chi_tiet_data(self, obj):
        """
        Get child details data.
        """
        chi_tiet = obj.bank_transfer_details.all()
        from django_ledger.api.serializers.tien_gui.hach_toan.giay_bao_no.chi_tiet_giay_bao_no import BankTransferDetailSerializer
        return BankTransferDetailSerializer(chi_tiet, many=True).data

    def get_thue_data(self, obj):
        """
        Get tax details data.
        """
        thue = obj.tax_details.all()
        from django_ledger.api.serializers.tien_gui.hach_toan.giay_bao_no.thue_giay_bao_no import TaxDetailSerializer
        return TaxDetailSerializer(thue, many=True).data

    def get_phi_ngan_hang_data(self, obj):
        """
        Get bank fee details data.
        """
        phi_ngan_hang = obj.bank_fee_details.all()
        from django_ledger.api.serializers.tien_gui.hach_toan.giay_bao_no.phi_ngan_hang_giay_bao_no import BankFeeDetailSerializer
        return BankFeeDetailSerializer(phi_ngan_hang, many=True).data

    def validate_status(self, value):
        """
        Validate status field.
        """
        valid_statuses = ['1', '2', '3']  # Define valid status values
        if value not in valid_statuses:
            raise serializers.ValidationError(f"Status must be one of: {', '.join(valid_statuses)}")
        return value

    def validate_ty_gia(self, value):
        """
        Validate exchange rate.
        """
        if value is not None and value <= 0:
            raise serializers.ValidationError("Exchange rate must be greater than 0")
        return value

    def validate_thue_suat(self, value):
        """
        Validate tax rate.
        """
        if value is not None and (value < 0 or value > 100):
            raise serializers.ValidationError("Tax rate must be between 0 and 100")
        return value

    def validate(self, attrs):
        """
        Validate the entire object.
        """
        # Validate date consistency
        if attrs.get('ngay_ct') and attrs.get('ngay_lct'):
            if attrs['ngay_ct'] > attrs['ngay_lct']:
                raise serializers.ValidationError("Document date cannot be later than creation date")

        # Validate amounts
        if attrs.get('t_tien_nt') and attrs.get('t_tien') and attrs.get('ty_gia'):
            expected_vnd = attrs['t_tien_nt'] * attrs['ty_gia']
            if abs(attrs['t_tien'] - expected_vnd) > 1:  # Allow small rounding differences
                raise serializers.ValidationError("VND amount does not match foreign currency amount * exchange rate")

        return attrs



