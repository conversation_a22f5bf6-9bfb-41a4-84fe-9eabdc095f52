# Sơ Đồ Cơ Sở Dữ Liệu Cốt Lõi - <PERSON><PERSON>go Ledger

Tài liệu này mô tả chi tiết cấu trúc cơ sở dữ liệu cho các thực thể cốt lõi trong hệ thống Django Ledger.

## M<PERSON><PERSON>

- [S<PERSON> Đồ Cơ Sở Dữ Liệu Cốt Lõi - Django <PERSON>](#sơ-đồ-cơ-sở-dữ-liệu-cốt-lõi---django-ledger)
  - [<PERSON><PERSON><PERSON>](#mục-lục)
  - [EntityModel](#entitymodel)
  - [EntityManagementModel](#entitymanagementmodel)
  - [EntityUnitModel](#entityunitmodel)
  - [EntityStateModel](#entitystatemodel)

## EntityModel

EntityModel đại diện cho một công ty, tổ chức, pháp nhân, doanh nghiệp hoặc cá nhân hoạt động như một doanh nghiệp.

| Trường | <PERSON><PERSON><PERSON> | <PERSON><PERSON> tả |
|-------|------|-------------|
| uuid | U<PERSON>DField | Khóa chính |
| name | CharField | Tên đơn vị |
| slug | SlugField | Tên thân thiện với URL |
| admin | ForeignKey(UserModel) | Người quản trị |
| default_coa | OneToOneField(ChartOfAccountModel) | Sơ đồ tài khoản mặc định |
| managers | ManyToManyField(UserModel) | Người quản lý đơn vị |
| hidden | BooleanField | Cờ ẩn đơn vị khỏi truy vấn |
| accrual_method | BooleanField | Sử dụng phương pháp kế toán dồn tích hay không (nếu không, sẽ dùng phương pháp tiền mặt) |
| fy_start_month | IntegerField | Tháng bắt đầu năm tài chính (1-12) |
| last_closing_date | DateField | Ngày đóng sổ gần nhất |
| picture | ImageField | Logo/hình ảnh đơn vị |
| meta | JSONField | Siêu dữ liệu bổ sung |

**Mục đích**: EntityModel là đơn vị cơ bản trong hệ thống. Mỗi EntityModel có thể duy trì sổ sách kế toán riêng, sơ đồ tài khoản, và báo cáo tài chính. EntityModel hỗ trợ cấu trúc mô hình cha/con để tạo điều kiện cho các cấu trúc doanh nghiệp phức tạp nơi một số đơn vị có thể thuộc sở hữu của các đơn vị khác.

**Các quan hệ chính**:
- Có nhiều LedgerModel
- Có nhiều ChartOfAccountModel, với một là mặc định
- Có nhiều EntityUnitModel
- Có nhiều BankAccountModel
- Có nhiều CustomerModel và VendorModel
- Được quản lý bởi một UserModel (admin) và tùy chọn nhiều UserModel khác (managers)

## EntityManagementModel

Quản lý quyền giữa người dùng và đơn vị.

| Trường | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| entity | ForeignKey(EntityModel) | Đơn vị liên quan |
| user | ForeignKey(UserModel) | Người dùng có quyền |
| permission_level | CharField | Cấp độ quyền (đọc, ghi, tạm ngưng) |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: EntityManagementModel quản lý quyền hạn của người dùng đối với một EntityModel cụ thể, cho phép phân quyền chi tiết.

## EntityUnitModel

Đại diện cho các đơn vị con trong một tổ chức như phòng ban, chi nhánh, v.v.

| Trường | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| name | CharField | Tên đơn vị |
| slug | SlugField | Tên thân thiện với URL |
| entity | ForeignKey(EntityModel) | Đơn vị mẹ |
| document_prefix | CharField | Tiền tố cho tài liệu |
| hidden | BooleanField | Cờ ẩn đơn vị |
| active | BooleanField | Đơn vị có hoạt động không |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: EntityUnitModel cho phép một EntityModel được chia thành các đơn vị nhỏ hơn có ý nghĩa về mặt tổ chức. Điều này cho phép người dùng tạo báo cáo tài chính phân đoạn theo từng đơn vị tổ chức.

## EntityStateModel

Theo dõi trạng thái số hóa và theo dõi trình tự của tài liệu cho các EntityModel.

| Trường | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| entity_model | ForeignKey(EntityModel) | Đơn vị liên quan |
| entity_unit | ForeignKey(EntityUnitModel) | Đơn vị con liên quan (tùy chọn) |
| fiscal_year | SmallIntegerField | Năm tài chính |
| key | CharField | Loại tài liệu (bút toán, đơn hàng, hóa đơn...) |
| sequence | BigIntegerField | Số thứ tự, tăng dần cho mỗi tài liệu mới |

**Mục đích**: EntityStateModel theo dõi số hóa tài liệu tự động cho các loại tài liệu khác nhau (bút toán, hóa đơn, đơn hàng, v.v.). Nó đảm bảo rằng mỗi tài liệu có một số duy nhất phù hợp với cả năm tài chính và đơn vị tổ chức.
