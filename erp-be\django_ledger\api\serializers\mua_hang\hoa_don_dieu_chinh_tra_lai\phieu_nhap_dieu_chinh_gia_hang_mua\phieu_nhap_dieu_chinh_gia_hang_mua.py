"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for PhieuNhapDieuChinhGiaHangMua model.
"""
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from django_ledger.models import PhieuNhapDieuChinhGiaHangMuaModel
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.accounts import AccountModelSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer 
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.chung_tu import ChungTuSerializer
from django_ledger.api.serializers.mua_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_dieu_chinh_gia_hang_mua.chi_tiet_phieu_nhap_dieu_chinh_gia_hang_mua import ChiTietPhieuNhapDieuChinhGiaHangMuaSerializer
from django_ledger.api.serializers.mua_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_dieu_chinh_gia_hang_mua.thue_phieu_nhap_dieu_chinh_gia_hang_mua import ThuePhieuNhapDieuChinhGiaHangMuaSerializer



class PhieuNhapDieuChinhGiaHangMuaSerializer(serializers.ModelSerializer):
    """
    Serializer for PhieuNhapDieuChinhGiaHangMua model.
    """
    # Read-only fields for related objects
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    tk_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    so_ct0_data = serializers.SerializerMethodField(read_only=True)
    so_ct2_data = serializers.SerializerMethodField(read_only=True)
    so_ct_data = serializers.SerializerMethodField(read_only=True)

    # Child data
    chi_tiet_data = serializers.SerializerMethodField(read_only=True)
    thue_data = serializers.SerializerMethodField(read_only=True)

    # Write-only fields for child data
    chi_tiet = serializers.ListField(required=False, write_only=True)
    thue = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = PhieuNhapDieuChinhGiaHangMuaModel
        fields = [
            'uuid',
            'entity_model',
            'ma_ngv',
            'ma_kh',
            'ma_kh_data',
            'tk',
            'tk_data',
            'ma_tt',
            'unit_id',
            'ma_nt',
            'ma_nt_data',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_hd',
            'ma_dtt',
            'ma_ku',
            'ma_phi',
            'ma_sp',
            'ma_cp0',
            'ma_nk',
            'so_ct0',
            'so_ct0_data',
            'so_ct2',
            'so_ct2_data',
            'so_ct',
            'so_ct_data',
            'dien_giai',
            'xprogress',
            'i_so_ct',
            'xdatetime2',
            'status',
            'xfile',
            'id_progress',
            'ngay_ct',
            'ngay_lct',
            'ngay_ct0',
            'ty_gia',
            't_so_luong',
            't_tien_nt',
            't_tien',
            't_thue_nt',
            't_thue',
            't_tt_nt',
            't_tt',
            'transfer_yn',
            'chi_tiet_data',
            'thue_data',
            'chi_tiet',
            'thue',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'ma_kh_data',
            'tk_data',
            'ma_nt_data',
            'ma_bp_data',
            'so_ct0_data',
            'so_ct2_data',
            'so_ct_data',
            'chi_tiet_data',
            'thue_data',
            'created',
            'updated'
        ]


    def get_ma_kh_data(self, obj):
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_tk_data(self, obj):
        """
        Get account data.
        """
        if obj.tk:
            return AccountModelSerializer(obj.tk).data
        return None

    def get_ma_nt_data(self, obj):
        """
        Get currency data.
        """
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_so_ct0_data(self, obj):
        """
        Get document number 0 data.
        """
        if obj.so_ct0:
            return ChungTuSerializer(obj.so_ct0).data
        return None

    def get_so_ct2_data(self, obj):
        """
        Get document number 2 data.
        """
        if obj.so_ct2:
            return ChungTuSerializer(obj.so_ct2).data
        return None

    def get_so_ct_data(self, obj):
        """
        Get document number data.
        """
        if obj.so_ct:
            return ChungTuSerializer(obj.so_ct).data
        return None

    def get_chi_tiet_data(self, obj):
        """
        Get detail records data.
        """
        chi_tiet = obj.chi_tiet_list.all()
        return ChiTietPhieuNhapDieuChinhGiaHangMuaSerializer(chi_tiet, many=True).data

    def get_thue_data(self, obj):
        """
        Get tax records data.
        """
        thue = obj.chi_tiet_thue_list.all()
        return ThuePhieuNhapDieuChinhGiaHangMuaSerializer(thue, many=True).data

    def validate(self, attrs):
        """
        Validate the serializer data.
        """
        # Check if this is an update or create operation
        is_update = self.instance is not None

        # Validate required fields for create operations
        if not is_update:
            required_fields = ['tk', 'i_so_ct', 'ngay_ct', 'ngay_lct', 'ngay_ct0', 'status']
            for field in required_fields:
                if field not in attrs:
                    raise serializers.ValidationError({
                        field: _('This field is required.')
                    })

        # Validate i_so_ct uniqueness (if provided)
        if 'i_so_ct' in attrs:
            i_so_ct = attrs['i_so_ct']
            queryset = PhieuNhapDieuChinhGiaHangMuaModel.objects.filter(i_so_ct=i_so_ct)
            if is_update:
                queryset = queryset.exclude(uuid=self.instance.uuid)
            if queryset.exists():
                raise serializers.ValidationError({
                    'i_so_ct': _('Internal document number must be unique.')
                })

        # Validate decimal fields are positive
        decimal_fields = ['ty_gia', 't_so_luong', 't_tien_nt', 't_tien', 't_thue_nt', 't_thue', 't_tt_nt', 't_tt']
        for field in decimal_fields:
            if field in attrs and attrs[field] is not None and attrs[field] < 0:
                raise serializers.ValidationError({
                    field: _('This field must be positive.')
                })

        # Validate date relationships
        if 'ngay_ct' in attrs and 'ngay_lct' in attrs:
            if attrs['ngay_ct'] > attrs['ngay_lct']:
                raise serializers.ValidationError({
                    'ngay_ct': _('Document date cannot be later than creation date.')
                })

        return attrs

    def validate_chi_tiet(self, value):
        """
        Validate chi_tiet data.
        """
        if not isinstance(value, list):
            raise serializers.ValidationError(_('Chi tiet must be a list.'))

        # Validate line numbers are unique
        lines = []
        for item in value:
            if 'line' in item:
                line = item['line']
                if line in lines:
                    raise serializers.ValidationError(_('Line numbers must be unique.'))
                lines.append(line)

        return value

    def validate_thue(self, value):
        """
        Validate thue data.
        """
        if not isinstance(value, list):
            raise serializers.ValidationError(_('Thue must be a list.'))

        # Validate line numbers are unique
        lines = []
        for item in value:
            if 'line' in item:
                line = item['line']
                if line in lines:
                    raise serializers.ValidationError(_('Line numbers must be unique.'))
                lines.append(line)

        return value
