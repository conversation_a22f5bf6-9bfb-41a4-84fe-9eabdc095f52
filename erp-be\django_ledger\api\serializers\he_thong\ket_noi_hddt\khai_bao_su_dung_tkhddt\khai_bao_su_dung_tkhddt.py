"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the KhaiBaoSuDungTKHDDTSerializer, which handles serialization
for the KhaiBaoSuDungTKHDDTModel.
"""

from rest_framework import serializers

from django_ledger.models.he_thong.ket_noi_hddt.khai_bao_su_dung_tkhddt import KhaiBaoSuDungTKHDDTModel
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.unit import EntityUnitModelSimpleSerializer
from django_ledger.api.serializers.he_thong.ket_noi_hddt.tai_khoan_hoa_don_dien_tu.tai_khoan_hoa_don_dien_tu import TaiKhoanHoaDonDienTuSerializer


class KhaiBaoSuDungTKHDDTSerializer(serializers.ModelSerializer):
    """
    Serializer for KhaiBaoSuDungTKHDDTModel.
    """
    entity_model = EntityModelSerializer(read_only=True)

    # Reference data fields
    ma_tkhddt_data = serializers.SerializerMethodField()
    user_id_data = serializers.SerializerMethodField()
    ma_unit_data = serializers.SerializerMethodField()

    class Meta:
        model = KhaiBaoSuDungTKHDDTModel
        fields = [
            'uuid',
            'entity_model',
            'ma_tkhddt',
            'ma_tkhddt_data',
            'user_id',
            'user_id_data',
            'ma_unit',
            'ma_unit_data',
            'username',
            'e_mail',
            'unit_id',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'entity_model', 'created', 'updated']

    def get_ma_tkhddt_data(self, obj):
        """
        Get the ma_tkhddt data from the relationship
        """
        if obj.ma_tkhddt:
            return {
                'uuid': obj.ma_tkhddt.uuid,
                'ma_tkhddt': obj.ma_tkhddt.ma_tkhddt,
                'ten_tkhddt': obj.ma_tkhddt.ten_tkhddt
            }
        return None

    def get_user_id_data(self, obj):
        """
        Get the user_id data from the relationship
        """
        if obj.user_id:
            return {
                'uuid': obj.user_id.uuid,
                'user': obj.user_id.user.username if obj.user_id.user else None,
                'nickname': obj.user_id.nickname
            }
        return None

    def get_ma_unit_data(self, obj):
        """
        Get the ma_unit data from the relationship
        """
        if obj.ma_unit:
            return EntityUnitModelSimpleSerializer(obj.ma_unit).data
        return None
