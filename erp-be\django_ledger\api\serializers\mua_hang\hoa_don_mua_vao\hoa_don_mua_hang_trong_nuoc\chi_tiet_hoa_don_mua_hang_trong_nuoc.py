"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the serializers for the ChiTietHoaDonMuaHangTrongNuocModel.
"""

from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc import (
    ChiTietHoaDonMuaHangTrongNuocModel,
)
from django_ledger.models.vat_tu import VatTuModel
from rest_framework import serializers


class ChiTietHoaDonMuaHangTrongNuocModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the ChiTietHoaDonMuaHangTrongNuocModel.
    Used for read operations.
    """

    # Read-only fields for related objects
    hoa_don_data = serializers.SerializerMethodField(read_only=True)
    ma_vt_data = serializers.SerializerMethodField(read_only=True)
    ma_kho_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_lo_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietHoaDonMuaHangTrongNuocModel
        fields = [
            "uuid",
            "hoa_don",
            "hoa_don_data",
            "line",
            "ma_vt",
            "ma_vt_data",
            "dvt",
            "dvt_data",
            "ten_dvt",
            "ma_kho",
            "ma_kho_data",
            "ma_lo",
            "ma_lo_data",
            "ten_lo",
            "so_luong",
            "gia_nt0",
            "tien_nt0",
            "cp_nt",
            "gia_ton",
            "thue_xuat",
            "thue_nt",
            "tk_no",
            "ma_bp",
            "ma_bp_data",
            "ma_vv",
            "ma_vv_data",
            "ma_hd",
            "ma_dtt",
            "ma_ku",
            "ma_phi",
            "ma_lsx",
            "ma_cp0",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "hoa_don_data",
            "ma_vt_data",
            "dvt_data",
            "ma_kho_data",
            "ma_bp_data",
            "ma_vv_data",
            "ma_lo_data",
            "created",
            "updated",
        ]

    def get_hoa_don_data(self, obj):
        """
        Get basic information about the invoice
        """
        if not obj.hoa_don:
            return None

        # Get basic invoice data
        hoa_don_data = {
            "uuid": str(obj.hoa_don.uuid),
            "so_ct": getattr(obj.hoa_don, "so_ct_id", None),
            "ngay_ct": getattr(obj.hoa_don, "ngay_ct", None),
            "ten_kh": getattr(obj.hoa_don, "ten_kh", ""),
        }

        # Add document number if available
        if hasattr(obj.hoa_don, "so_ct") and obj.hoa_don.so_ct:
            hoa_don_data["so_ct_data"] = {
                "uuid": str(obj.hoa_don.so_ct.uuid),
                "ma_ct": getattr(obj.hoa_don.so_ct, "ma_ct", ""),
                "ten_ct": getattr(obj.hoa_don.so_ct, "ten_ct", ""),
            }

        return hoa_don_data

    def get_ma_vt_data(self, obj):
        """
        Get basic information about the material
        """
        if not obj.ma_vt:
            return None

        # Return a serialized dictionary instead of the VatTuModel object
        return {
            "uuid": str(obj.ma_vt.uuid) if hasattr(obj.ma_vt, "uuid") else None,
            "ma_vt": getattr(obj.ma_vt, "ma_vt", ""),
            "ten_vt": getattr(obj.ma_vt, "ten_vt", "") or getattr(obj, "ten_vt0", ""),
            "dvt": (
                str(getattr(obj.ma_vt, "dvt_id", ""))
                if hasattr(obj.ma_vt, "dvt_id")
                else None
            ),
        }

    def get_dvt_data(self, obj):
        """
        Get basic information about the unit
        """
        if not obj.dvt:
            return None

        return {
            "uuid": str(obj.dvt.uuid) if hasattr(obj.dvt, "uuid") else None,
            "dvt": getattr(obj.dvt, "dvt", ""),
            "ten_dvt": getattr(obj.dvt, "ten_dvt", "") or getattr(obj, "ten_dvt", ""),
        }

    def get_ma_kho_data(self, obj):
        """
        Get basic information about the warehouse
        """
        if not obj.ma_kho:
            return None

        return {
            "uuid": str(obj.ma_kho.uuid) if hasattr(obj.ma_kho, "uuid") else None,
            "ma_kho": getattr(obj.ma_kho, "ma_kho", ""),
            "ten_kho": getattr(obj.ma_kho, "ten_kho", "")
            or getattr(obj, "ten_kho", ""),
        }

    def get_ma_bp_data(self, obj):
        """
        Get basic information about the department
        """
        if not obj.ma_bp:
            return None

        return {
            "uuid": str(obj.ma_bp.uuid) if hasattr(obj.ma_bp, "uuid") else None,
            "ma_bp": getattr(obj.ma_bp, "ma_bp", ""),
            "ten_bp": getattr(obj.ma_bp, "ten_bp", "") or getattr(obj, "ten_bp", ""),
        }

    def get_ma_vv_data(self, obj):
        """
        Get basic information about the job
        """
        if not obj.ma_vv:
            return None

        return {
            "uuid": str(obj.ma_vv.uuid) if hasattr(obj.ma_vv, "uuid") else None,
            "ma_vv": getattr(obj.ma_vv, "ma_vv", ""),
            "ten_vv": getattr(obj.ma_vv, "ten_vv", "") or getattr(obj, "ten_vv", ""),
        }

    def get_ma_lo_data(self, obj):
        """
        Get basic information about the lot
        """
        if not obj.ma_lo:
            return None

        return {
            "uuid": str(obj.ma_lo.uuid) if hasattr(obj.ma_lo, "uuid") else None,
            "ma_lo": getattr(obj.ma_lo, "ma_lo", ""),
            "ten_lo": getattr(obj.ma_lo, "ten_lo", "") or getattr(obj, "ten_lo", ""),
        }


class ChiTietHoaDonMuaHangTrongNuocModelCreateUpdateSerializer(
    serializers.ModelSerializer
):
    """
    Serializer for the ChiTietHoaDonMuaHangTrongNuocModel.
    Used for create and update operations.
    """

    class Meta:
        model = ChiTietHoaDonMuaHangTrongNuocModel
        fields = [
            "line",
            "ma_vt",
            "dvt",
            "ten_dvt",
            "ma_kho",
            "ten_kho",
            "ma_lo",
            "ten_lo",
            "so_luong",
            "gia_nt0",
            "tien_nt0",
            "cp_nt",
            "gia_ton",
            "thue_xuat",
            "thue_nt",
            "tk_no",
            "ma_bp",
            "ma_vv",
            "ma_hd",
            "ma_dtt",
            "ma_ku",
            "ma_phi",
            "ma_lsx",
            "ma_cp0",
        ]

    def validate(self, data):
        """
        Validate the data before creating or updating the model instance.

        Parameters
        ----------
        data : dict
            The data to validate.

        Returns
        -------
        dict
            The validated data.
        """
        # Auto-calculate amount if not provided
        if "tien_nt0" not in data and "so_luong" in data and "gia_nt0" in data:
            data["tien_nt0"] = data["so_luong"] * data["gia_nt0"]

        return data
