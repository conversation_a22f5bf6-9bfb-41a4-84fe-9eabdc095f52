"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Admin class for ChiPhiKhongHopLe model.
"""

from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from django_ledger.models import ChiPhiKhongHopLeModel


class ChiPhiKhongHopLeModelAdmin(admin.ModelAdmin):
    """
    Admin class for the ChiPhiKhongHopLeModel (Invalid Expense) model.
    """
    list_display = ['ma_cpkhl', 'ten_cpkhl', 'status', 'created', 'updated']
    search_fields = ['ma_cpkhl', 'ten_cpkhl', 'ten_cpkhl2', 'ghi_chu']
    list_filter = ['status', 'entity_model']
    readonly_fields = ['uuid', 'created', 'updated']
    raw_id_fields = ['entity_model']
    fieldsets = [
        (
            _('Basic Information'), {
                'fields': [
                    'entity_model',
                    'ma_cpkhl',
                    'ten_cpkhl',
                    'ten_cpkhl2',
                    'ghi_chu',
                    'status',
                ]
            }
        ),
        (
            _('Metadata'), {
                'fields': [
                    'uuid',
                    'created',
                    'updated',
                ],
                'classes': ['collapse']
            }
        ),
    ]
