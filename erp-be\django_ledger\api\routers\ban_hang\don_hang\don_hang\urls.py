from django.urls import include, path
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.ban_hang.don_hang.don_hang import (
    ChiTietDonBanHangViewSet,
    DonBanHangViewSet,
)

router = DefaultRouter()
router.register("", DonBanHangViewSet, basename="don-hang")

urlpatterns = [
    # Include the main router for list/create operations
    path("", include(router.urls)),
    # Nested routes for specific don-hang instances
    path(
        "don-hang/<uuid:don_hang_uuid>/",
        include(
            [
                path(
                    "chi-tiet/",
                    ChiTietDonBanHangViewSet.as_view({"get": "list", "post": "create"}),
                    name="chi-tiet-don-ban-hang-list",
                ),
                path(
                    "chi-tiet/<uuid:uuid>/",
                    ChiTietDonBanHangViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="chi-tiet-don-ban-hang-detail",
                ),
                # Don ban hang actions
                path(
                    "approve/",
                    DonBanHangViewSet.as_view({"post": "approve"}),
                    name="don-ban-hang-approve",
                ),
                path(
                    "cancel/",
                    DonBanHangViewSet.as_view({"post": "cancel"}),
                    name="don-ban-hang-cancel",
                ),
                # Bulk create chi tiet don ban hang
                path(
                    "chi-tiet/bulk-create/",
                    ChiTietDonBanHangViewSet.as_view({"post": "bulk_create"}),
                    name="chi-tiet-don-ban-hang-bulk-create",
                ),
            ]
        ),
    ),
]
