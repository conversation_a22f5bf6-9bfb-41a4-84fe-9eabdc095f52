"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietButToanDieuChinhGiamCongNo (Debt Reduction Adjustment Detail) model.
"""

from rest_framework import serializers

from django_ledger.models.ban_hang.dieu_chinh_can_tru_cong_no.but_toan_dieu_chinh_giam_cong_no import ChiTietButToanDieuChinhGiamCongNoModel
from django_ledger.api.serializers.accounts import AccountModelSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer


class ChiTietButToanDieuChinhGiamCongNoSerializer(serializers.ModelSerializer):
    """
    Serializer for the ChiTietButToanDieuChinhGiamCongNoModel
    """
    ma_kh_data = serializers.SerializerMethodField()
    tk_co_data = serializers.SerializerMethodField()
    ma_bp_data = serializers.SerializerMethodField()
    ma_vv_data = serializers.SerializerMethodField()
    ma_hd_data = serializers.SerializerMethodField()
    ma_dtt_data = serializers.SerializerMethodField()
    ma_ku_data = serializers.SerializerMethodField()
    ma_phi_data = serializers.SerializerMethodField()
    ma_sp_data = serializers.SerializerMethodField()
    ma_cp0_data = serializers.SerializerMethodField()

    class Meta:
        model = ChiTietButToanDieuChinhGiamCongNoModel
        fields = [
            'uuid',
            'but_toan_dieu_chinh_giam_cong_no',
            'line',
            'ma_kh',
            'ma_kh_data',
            'id_hd',
            'tk_co',
            'tk_co_data',
            'ty_gia_hd',
            'ty_gia2',
            'tien_nt',
            'tien',
            'dien_giai',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_sp_data',
            'ma_lsx',
            'ma_cp0',
            'ma_cp0_data',
            'id_tt',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'ma_kh_data',
            'tk_co_data',
            'ma_bp_data',
            'ma_vv_data',
            'ma_hd_data',
            'ma_dtt_data',
            'ma_ku_data',
            'ma_phi_data',
            'ma_sp_data',
            'ma_cp0_data',
            'created',
            'updated'
        ]

    def get_ma_kh_data(self, obj):
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_tk_co_data(self, obj):
        if obj.tk_co:
            return AccountModelSerializer(obj.tk_co).data
        return None

    def get_model_data(self, obj):
        """
        Helper method to get all data from a model instance.
        """
        if not obj:
            return None

        # Create a serializer for the model
        class GenericModelSerializer(serializers.ModelSerializer):
            class Meta:
                model = obj.__class__
                fields = '__all__'

        # Return serialized data
        return GenericModelSerializer(obj).data

    def get_ma_bp_data(self, obj):
        return self.get_model_data(obj.ma_bp)

    def get_ma_vv_data(self, obj):
        return self.get_model_data(obj.ma_vv)

    def get_ma_hd_data(self, obj):
        return self.get_model_data(obj.ma_hd)

    def get_ma_dtt_data(self, obj):
        return self.get_model_data(obj.ma_dtt)

    def get_ma_ku_data(self, obj):
        return self.get_model_data(obj.ma_ku)

    def get_ma_phi_data(self, obj):
        return self.get_model_data(obj.ma_phi)

    def get_ma_sp_data(self, obj):
        return self.get_model_data(obj.ma_sp)

    def get_ma_cp0_data(self, obj):
        return self.get_model_data(obj.ma_cp0)
