"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for the TaiKhoanCuaHang (Store Account Details) model.
"""

from rest_framework import serializers
from django_ledger.models.tai_khoan_cua_hang import TaiKhoanCuaHangModel
from django_ledger.api.serializers.warehouse import KhoHangModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.account import AccountModelSerializer


class TaiKhoanCuaHangModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the TaiKhoanCuaHangModel (Store Account Details) model.

    This serializer handles the conversion between TaiKhoanCuaHangModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields (ma_kho, ma_bp, tk_cn, etc.)
    - Adds additional fields with "_data" suffix (ma_kho_data, ma_bp_data, tk_cn_data, etc.)
      that contain the complete nested object data with all available fields
    - When deserializing, accepts UUIDs for reference fields

    Fields:
    - uuid: Unique identifier (read-only)
    - entity_model: Reference to the entity (ForeignKey to EntityModel)
    - ma_cuahang: Store code
    - ma_kho: Reference to the warehouse (ForeignKey to KhoHangModel)
    - ma_kho_data: Full warehouse data (nested KhoHangModelSerializer)
    - ma_bp: Reference to the department (ForeignKey to BoPhanModel)
    - ma_bp_data: Full department data (nested BoPhanModelSerializer)
    - tk_cn: Reference to the accounts receivable account (ForeignKey to AccountModel)
    - tk_cn_data: Full account data (nested AccountModelSerializer)
    - tk_cn2: Reference to the secondary accounts receivable account (ForeignKey to AccountModel)
    - tk_cn2_data: Full account data (nested AccountModelSerializer)
    - tk_tm: Reference to the cash account (ForeignKey to AccountModel)
    - tk_tm_data: Full account data (nested AccountModelSerializer)
    - status: Status indicator (1=active, 0=inactive)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    """
    # Define additional fields for nested data
    ma_kho_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    tk_cn_data = serializers.SerializerMethodField(read_only=True)
    tk_cn2_data = serializers.SerializerMethodField(read_only=True)
    tk_tm_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = TaiKhoanCuaHangModel
        fields = ['uuid', 'entity_model', 'ma_cuahang', 'ma_kho', 'ma_kho_data', 'ma_bp', 'ma_bp_data',
                  'tk_cn', 'tk_cn_data', 'tk_cn2', 'tk_cn2_data', 'tk_tm', 'tk_tm_data', 'status', 'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated', 'ma_kho_data', 'ma_bp_data',
                           'tk_cn_data', 'tk_cn2_data', 'tk_tm_data']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_cuahang": "DBL",
                "ma_kho": "123e4567-e89b-12d3-a456-************",
                "ma_kho_data": {
                    "uuid": "123e4567-e89b-12d3-a456-************",
                    "ma_kho": "TTM_TMDT",
                    "ten_kho": "Kho TMDT",
                    "status": "1"
                },
                "ma_bp": "123e4567-e89b-12d3-a456-************",
                "ma_bp_data": {
                    "uuid": "123e4567-e89b-12d3-a456-************",
                    "ma_bp": "TTM-004",
                    "ten_bp": "Bộ phận TMDT"
                },
                "tk_cn": "123e4567-e89b-12d3-a456-************",
                "tk_cn_data": {
                    "uuid": "123e4567-e89b-12d3-a456-************",
                    "code": "112",
                    "name": "Phải thu khách hàng"
                },
                "tk_cn2": "123e4567-e89b-12d3-a456-************",
                "tk_cn2_data": {
                    "uuid": "123e4567-e89b-12d3-a456-************",
                    "code": "11212",
                    "name": "Phải thu khách hàng chi tiết"
                },
                "tk_tm": "123e4567-e89b-12d3-a456-************",
                "tk_tm_data": {
                    "uuid": "123e4567-e89b-12d3-a456-************",
                    "code": "1112",
                    "name": "Tiền mặt"
                },
                "status": 1,
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z"
            }
        }

    def get_ma_kho_data(self, obj):
        """Method field for ma_kho_data"""
        if obj.ma_kho:
            return KhoHangModelSerializer(obj.ma_kho).data
        return None

    def get_ma_bp_data(self, obj):
        """Method field for ma_bp_data"""
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_tk_cn_data(self, obj):
        """Method field for tk_cn_data"""
        if obj.tk_cn:
            return AccountModelSerializer(obj.tk_cn).data
        return None

    def get_tk_cn2_data(self, obj):
        """Method field for tk_cn2_data"""
        if obj.tk_cn2:
            return AccountModelSerializer(obj.tk_cn2).data
        return None

    def get_tk_tm_data(self, obj):
        """Method field for tk_tm_data"""
        if obj.tk_tm:
            return AccountModelSerializer(obj.tk_tm).data
        return None
