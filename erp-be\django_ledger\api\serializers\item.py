"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.
"""

from rest_framework import serializers
from django_ledger.models import ItemModel, UnitOfMeasureModel

class UnitOfMeasureModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the UnitOfMeasureModel
    """
    class Meta:
        model = UnitOfMeasureModel
        fields = [
            'uuid',
            'name',
            'unit_abbr',
            'is_active',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated']

class ItemModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the ItemModel
    """
    uom_name = serializers.CharField(source='uom.name', read_only=True)
    
    class Meta:
        model = ItemModel
        fields = [
            'uuid',
            'name',
            'uom',
            'uom_name',
            'sku',
            'upc',
            'item_id',
            'default_amount',
            'is_active',
            'is_product_or_service',
            'is_inventory',
            # Additional ERP fields
            'ma_san_pham',
            'ten_san_pham',
            'ten_khac',
            'tao_nhap_thanh_phan',
            'dvt_nhap_lieu',
            'theo_doi_ton_kho',
            'theo_doi_lo',
            'cach_tinh_gia_ton_kho',
            'loai_vat_tu',
            'ma_kho_mac_dinh',
            'ma_thue_mac_dinh',
            'tk_kho',
            'tk_doanh_thu',
            'tk_gia_von',
            'the_tich',
            'khoi_luong',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated']
