name: PR Review
on:
  pull_request:
  

# Required permissions
permissions:
  contents: read
  pull-requests: write

jobs:
  review:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    steps:
      - uses: actions/checkout@v3

      - name: AI PR Review
        uses: jonit-dev/openrouter-github-action@main
        with:
          # Required inputs
          github_token: ${{ secrets.GITHUB_TOKEN }} # Automatically provided
          open_router_key: ${{ secrets.OPEN_ROUTER_KEY }} # Must be set in repository secrets

          # Optional inputs with defaults
          model_id: 'deepseek/deepseek-chat-v3-0324:free' # Default model
          max_tokens: '40960' # Increased token limit
          #review_label: 'ai-review' # Optional: Only review PRs with this label

          # Optional custom prompt
          custom_prompt: |
            You are a senior principal engineer with 25+ years of experience specializing in secure, maintainable, and high-performance software development. Analyze this PR with a comprehensive evaluation focusing on:
            Security & Reliability

            First, scan the entire project to understand:
            - Component structure and interaction
            - API integration with bedrock-wrapper
            - Message handling flow
            - UI implementation with Tailwind

            Security vulnerabilities (OWASP Top 10, least privilege, secure defaults)
            - Authentication and authorization mechanisms
            - Data validation and input sanitization
            - Error handling and exception management
            - Secure data storage and transmission

            Architecture & Design

            - SOLID principles adherence
            - Single Responsibility Principle
            - Open/Closed Principle
            - Liskov Substitution Principle
            - Interface Segregation Principle
            - Dependency Inversion Principle


            Design patterns implementation and appropriateness
            - Separation of concerns
            - Modularity and component coupling
            - Testability of design

            Code Quality

            - DRY (Don't Repeat Yourself) principle
            - KISS (Keep It Simple, Stupid) principle
            - YAGNI (You Aren't Gonna Need It) principle
            - Naming conventions and readability
            - Code complexity and maintainability metrics

            Performance & Efficiency

            - Algorithmic efficiency
            - Resource utilization (memory, CPU, network, storage)
            - Scalability considerations
            - Performance bottlenecks
            - Caching strategies where appropriate

            Testing & Documentation

            - Test coverage and quality (optional)
            - Edge case handling
            - Documentation completeness
            - API contracts and interfaces
            - Developer onboarding considerations

            Provide an overall assessment with:

            A 1-5 star rating (with half-star precision)
            Top 3 strengths
            Top 3 areas for improvement
            Specific, actionable remediation steps for critical issues
            Suggestions for future enhancements

            Consider the context of the codebase, team standards, and business requirements in your evaluation.
            Provide a summary of your review in a comment on the PR.
            Then provide your analysis in this EXACT format:

            ### 🏆 Overall Score  
            [Rate feasibility 1-5 ⭐ for adding file/image attachments]

            ### 🐞 Potential Issues  
            [List specific technical blockers with file paths]
            - [File path] - [Specific issue]
            - ...

            ### 💡 Improvements Suggested  
            [List specific code changes needed with file paths]
            - [File path] - [Specific change needed to support attachments]
            - ...

            ### ⚡️ Performance  
            [Discuss performance implications with file paths]
            - [File path] - [Specific performance concern]
            - ...

            ### 🔐 Security Concerns  
            [List specific security issues for file handling with file paths]
            - [File path] - [Specific security concern]
            - ...

            ### 📏 Best Practices  
            [Suggest specific code standards needed with file paths]
            - [File path] - [Specific best practice recommendation]
            - ...
            Focus ONLY on what needs to be changed or improved to add file and image attachment functionality. No generic advice - only specific, actionable feedback with exact file paths. Don't mention what's good about the code, only focus on what needs to change to support attachments.
            Ensure the review is constructive, respectful, and encourages collaboration.
            Avoid using overly technical jargon or complex terminology that may not be understood by all team members.            
            Use a friendly and supportive tone, as if you were discussing the code in person.
            Avoid generic BS advice. For each advice, please provide a file Path of the related change. No need to paste the code itself.
            Do not mention what's good on the code. Just focus on what's bad and how to improve.
 

            Viết trả lời bằng tiếng Việt, những từ keyword quan trọng cần được bôi đậm và để nguyên bằng tiếng Anh.
