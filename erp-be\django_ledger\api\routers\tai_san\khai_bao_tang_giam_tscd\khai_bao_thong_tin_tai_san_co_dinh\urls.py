"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Khai Bao Thong Tin Tai San Co Dinh (Fixed Asset Information Declaration) module.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.tai_san.khai_bao_tang_giam_tscd.khai_bao_thong_tin_tai_san_co_dinh import KhaiBaoThongTinTaiSanCoDinhViewSet
from django_ledger.api.views.tai_san.khai_bao_tang_giam_tscd.khai_bao_thong_tin_tai_san_co_dinh import ChiTietDoiTuongHachToanTSCDViewSet
from django_ledger.api.views.tai_san.khai_bao_tang_giam_tscd.khai_bao_thong_tin_tai_san_co_dinh import ChiTietPhuTungKemTheoTSCDViewSet

# Main router for KhaiBaoThongTinTaiSanCoDinh
router = DefaultRouter()
router.register('', KhaiBaoThongTinTaiSanCoDinhViewSet, basename='khai-bao-thong-tin-tai-san-co-dinh')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),

    # Nested routes for khai_bao_thong_tin_tai_san_co_dinh
    path('<uuid:khai_bao_thong_tin_tai_san_co_dinh_uuid>/', include([
        # ChiTietDoiTuongHachToanTSCD routes
        path('chi-tiet-doi-tuong-hach-toan/', ChiTietDoiTuongHachToanTSCDViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-tiet-doi-tuong-hach-toan-list'),

        path('chi-tiet-doi-tuong-hach-toan/<uuid:uuid>/', ChiTietDoiTuongHachToanTSCDViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-tiet-doi-tuong-hach-toan-detail'),

        # ChiTietPhuTungKemTheoTSCD routes
        path('chi-tiet-phu-tung-kem-theo/', ChiTietPhuTungKemTheoTSCDViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-tiet-phu-tung-kem-theo-list'),

        path('chi-tiet-phu-tung-kem-theo/<uuid:uuid>/', ChiTietPhuTungKemTheoTSCDViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-tiet-phu-tung-kem-theo-detail'),
    ])),

]