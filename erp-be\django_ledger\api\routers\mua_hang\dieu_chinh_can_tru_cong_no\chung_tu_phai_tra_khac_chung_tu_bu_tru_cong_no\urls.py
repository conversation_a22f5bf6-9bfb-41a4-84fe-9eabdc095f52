"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Dieu Chinh Can Tru Cong No module.
"""

from django.urls import include, path
from django_ledger.api.views.mua_hang.dieu_chinh_can_tru_cong_no import (
    ChiTietChungTuPhaiTraKhacChungTuBuTruCongNoViewSet,
    ChungTuPhaiTraKhacChungTuBuTruCongNoViewSet,
    ThueChungTuPhaiTraKhacChungTuBuTruCongNoViewSet,
)
from rest_framework.routers import DefaultRouter

# Main router for ChungTuPhaiTraKhacChungTuBuTruCongNo
router = DefaultRouter()
router.register(
    "",
    ChungTuPhaiTraKhacChungTuBuTruCongNoViewSet,
    basename="chung-tu-phai-tra-khac-chung-tu-bu-tru-cong-no",
)

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path("", include(router.urls)),
    # Nested routes for chung-tu-phai-tra-khac-chung-tu-bu-tru-cong-no
    path(
        "<uuid:chung_tu_phai_tra_khac_chung_tu_bu_tru_cong_no_uuid>/",
        include(
            [
                # Chi tiet chung tu phai tra khac routes
                path(
                    "chi-tiet/",
                    ChiTietChungTuPhaiTraKhacChungTuBuTruCongNoViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="chi-tiet-chung-tu-phai-tra-khac-chung-tu-bu-tru-cong-no-list",
                ),
                path(
                    "chi-tiet/<uuid:uuid>/",
                    ChiTietChungTuPhaiTraKhacChungTuBuTruCongNoViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="chi-tiet-chung-tu-phai-tra-khac-chung-tu-bu-tru-cong-no-detail",
                ),
                # Additional chi-tiet endpoints
                path(
                    "chi-tiet/debit/",
                    ChiTietChungTuPhaiTraKhacChungTuBuTruCongNoViewSet.as_view(
                        {"get": "get_debit_entries"}
                    ),
                    name="chi-tiet-chung-tu-phai-tra-khac-chung-tu-bu-tru-cong-no-debit",
                ),
                path(
                    "chi-tiet/credit/",
                    ChiTietChungTuPhaiTraKhacChungTuBuTruCongNoViewSet.as_view(
                        {"get": "get_credit_entries"}
                    ),
                    name="chi-tiet-chung-tu-phai-tra-khac-chung-tu-bu-tru-cong-no-credit",
                ),
                # Thue chung tu phai tra khac routes
                path(
                    "thue/",
                    ThueChungTuPhaiTraKhacChungTuBuTruCongNoViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="thue-chung-tu-phai-tra-khac-chung-tu-bu-tru-cong-no-list",
                ),
                path(
                    "thue/<uuid:uuid>/",
                    ThueChungTuPhaiTraKhacChungTuBuTruCongNoViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="thue-chung-tu-phai-tra-khac-chung-tu-bu-tru-cong-no-detail",
                ),
                # Additional main endpoints
                path(
                    "calculate-totals/",
                    ChungTuPhaiTraKhacChungTuBuTruCongNoViewSet.as_view(
                        {"get": "calculate_totals"}
                    ),
                    name="chung-tu-phai-tra-khac-chung-tu-bu-tru-cong-no-calculate-totals",
                ),
                path(
                    "update-totals/",
                    ChungTuPhaiTraKhacChungTuBuTruCongNoViewSet.as_view(
                        {"post": "update_totals"}
                    ),
                    name="chung-tu-phai-tra-khac-chung-tu-bu-tru-cong-no-update-totals",
                ),
            ]
        ),
    ),
    # Additional endpoints not requiring UUID
    path(
        "active/",
        ChungTuPhaiTraKhacChungTuBuTruCongNoViewSet.as_view({"get": "get_active"}),
        name="chung-tu-phai-tra-khac-chung-tu-bu-tru-cong-no-active",
    ),
    # Tax calculation endpoint
    path(
        "thue/calculate-tax-amount/",
        ThueChungTuPhaiTraKhacChungTuBuTruCongNoViewSet.as_view(
            {"post": "calculate_tax_amount"}
        ),
        name="thue-chung-tu-phai-tra-khac-chung-tu-bu-tru-cong-no-calculate-tax-amount",
    ),
    # Tax query by customer 9 endpoint
    path(
        "thue/customer-9/<uuid:customer_9_uuid>/",
        ThueChungTuPhaiTraKhacChungTuBuTruCongNoViewSet.as_view(
            {"get": "get_by_customer_9"}
        ),
        name="thue-chung-tu-phai-tra-khac-chung-tu-bu-tru-cong-no-by-customer-9",
    ),
]
