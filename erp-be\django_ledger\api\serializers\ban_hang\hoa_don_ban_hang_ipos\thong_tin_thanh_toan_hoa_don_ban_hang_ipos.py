"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ThongTinThanhToanHoaDonBanHangIPosModel.
"""

from django.utils.translation import gettext_lazy as _
from django_ledger.api.serializers.accounts import AccountModelSerializer
from django_ledger.api.serializers.danh_muc.ban_hang import (
    HinhThucThanhToanModelSerializer,
)
from django_ledger.models import ThongTinThanhToanHoaDonBanHangIPosModel
from rest_framework import serializers


class ThongTinThanhToanHoaDonBanHangIPosSerializer(serializers.ModelSerializer):
    """
    Serializer for ThongTinThanhToanHoaDonBanHangIPosModel.
    """

    # Read-only fields for related objects
    hoa_don_data = serializers.SerializerMethodField(read_only=True)
    ma_httt_data = serializers.SerializerMethodField(read_only=True)
    tk_data = serializers.SerializerMethodField(read_only=True)
    ma_ct_data = serializers.SerializerMethodField(read_only=True)
    ma_nk_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ThongTinThanhToanHoaDonBanHangIPosModel
        fields = [
            "uuid",
            "hoa_don",
            "hoa_don_data",
            # Basic information
            "id",
            "line",
            # Payment method information
            "ma_httt",
            "ma_httt_data",
            "ten_httt",
            # Account information
            "tk",
            "tk_data",
            "ten_tk",
            # Document information
            "ma_ct",
            "ma_ct_data",
            "ten_ct",
            "ngay_ct",
            # Journal information
            "ma_nk",
            "ma_nk_data",
            "ten_nk",
            # Payment amount
            "t_tt_nt",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "hoa_don_data",
            "ma_httt_data",
            "tk_data",
            "ma_ct_data",
            "ma_nk_data",
            "created",
            "updated",
        ]

    def get_hoa_don_data(self, obj):
        """Get invoice data."""
        if obj.hoa_don:
            return {
                "uuid": str(obj.hoa_don.uuid),
                "so_ct": obj.hoa_don.so_ct,
                "ten_kh_thue": obj.hoa_don.ten_kh_thue,
            }
        return None

    def get_ma_httt_data(self, obj):
        """Get payment method data."""
        if obj.ma_httt:
            return HinhThucThanhToanModelSerializer(obj.ma_httt).data
        return None

    def get_tk_data(self, obj):
        """Get account data."""
        if obj.tk:
            return AccountModelSerializer(obj.tk).data
        return None

    def get_ma_ct_data(self, obj):
        """Get document data."""
        if obj.ma_ct:
            return {
                "uuid": str(obj.ma_ct.uuid),
                "ma_ct": obj.ma_ct.ma_ct,
                "ten_ct": getattr(obj.ma_ct, "ten_ct", None),
            }
        return None

    def get_ma_nk_data(self, obj):
        """Get journal data."""
        if obj.ma_nk:
            return {
                "uuid": str(obj.ma_nk.uuid),
                "ma_nk": obj.ma_nk.ma_nk,
                "ten_nk": getattr(obj.ma_nk, "ten_nk", None),
            }
        return None
