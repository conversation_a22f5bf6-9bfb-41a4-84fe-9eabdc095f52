"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for the PhuongTienVanChuyen (Shipping Method) model.
"""

from rest_framework import serializers
from django_ledger.models.phuong_tien_van_chuyen import PhuongTienVanChuyenModel


class PhuongTienVanChuyenModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the PhuongTienVanChuyenModel (Shipping Method) model.

    This serializer handles the conversion between PhuongTienVanChuyenModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - entity_model: Reference to the entity (ForeignKey to EntityModel)
    - ma_ptvc: Shipping method code
    - ten_ptvc: Primary name of the shipping method
    - ten_ptvc2: Secondary/alternative name (optional)
    - status: Status indicator ('0'=inactive, '1'=active)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    """
    class Meta:
        model = PhuongTienVanChuyenModel
        fields = ['uuid', 'entity_model', 'ma_ptvc', 'ten_ptvc', 'ten_ptvc2', 'status',
                  'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_ptvc": "FA",
                "ten_ptvc": "Vận chuyển hàng không",
                "ten_ptvc2": "Air Freight",
                "status": "1",
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z"
            }
        }

    def validate_ma_ptvc(self, value):
        """
        Validate ma_ptvc field
        
        Parameters
        ----------
        value : str
            The ma_ptvc value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Mã phương tiện vận chuyển không được để trống')
        if len(value) > 10:
            raise serializers.ValidationError('Mã phương tiện vận chuyển không được vượt quá 10 ký tự')
        return value.strip()

    def validate_ten_ptvc(self, value):
        """
        Validate ten_ptvc field
        
        Parameters
        ----------
        value : str
            The ten_ptvc value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Tên phương tiện vận chuyển không được để trống')
        return value.strip()
