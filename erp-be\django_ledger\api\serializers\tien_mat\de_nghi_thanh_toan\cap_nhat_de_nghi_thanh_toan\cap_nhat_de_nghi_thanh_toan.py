"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for CapNhatDeNghiThanhToan (Payment Proposal Update) model
"""

from django.utils.translation import gettext_lazy as _
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.tien_mat.de_nghi_thanh_toan.cap_nhat_de_nghi_thanh_toan.chi_tiet_cap_nhat_de_nghi_thanh_toan import (
    ChiTietCapNhatDeNghiThanhToanSerializer,
)
from django_ledger.models import CapNhatDeNghiThanhToanModel
from rest_framework import serializers


class CapNhatDeNghiThanhToanSerializer(serializers.ModelSerializer):
    """
    Serializer for CapNhatDeNghiThanhToan model.
    """

    # Read-only fields for related objects
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)

    # Child data
    chi_tiet_data = serializers.SerializerMethodField(read_only=True)
    chi_tiet = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = CapNhatDeNghiThanhToanModel
        fields = [
            "uuid",
            "entity_model",
            "ma_ngv",
            "ns_yn",
            "user_id",
            "ma_bp",
            "ma_bp_data",
            "ong_ba",
            "dia_chi",
            "dien_giai",
            "unit_id",
            "id_progress",
            "xprogress",
            "i_so_ct",
            "ma_nk",
            "so_ct",
            "ngay_ct",
            "ngay_lct",
            "xdatetime2",
            "ma_nt",
            "ma_nt_data",
            "ty_gia",
            "status",
            "transfer_yn",
            "ma_kh",
            "ma_kh_data",
            "so_ct_goc",
            "dien_giai_ct_goc",
            "email_tq",
            "t_tien_nt",
            "t_tien",
            "t_thue_nt",
            "t_thue",
            "xfile",
            "created_by",
            "chi_tiet_data",
            "chi_tiet",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "entity_model",
            "ma_bp_data",
            "ma_kh_data",
            "ma_nt_data",
            "chi_tiet_data",
            "created",
            "updated",
        ]

    def get_ma_bp_data(self, obj):
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_kh_data(self, obj):
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_ma_nt_data(self, obj):
        """
        Get currency data.
        """
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_chi_tiet_data(self, obj):
        """
        Get child details data.
        """
        chi_tiet = obj.chi_tiet.all()
        return ChiTietCapNhatDeNghiThanhToanSerializer(chi_tiet, many=True).data
