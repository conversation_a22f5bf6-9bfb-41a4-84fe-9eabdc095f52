from rest_framework import serializers
from django_ledger.models import GiaMuaModel
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.erp import CustomerModelSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer


class GiaMuaModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the GiaMuaModel (Purchase Price) model.

    This serializer handles the conversion between GiaMuaModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields (ma_vat_tu, don_vi_tinh, etc.)
    - Adds additional fields with "_data" suffix (ma_vat_tu_data, don_vi_tinh_data, etc.)
      that contain the complete nested object data with all available fields
    - When deserializing, accepts UUIDs for reference fields

    Example response:
    {
        "uuid": "dd06235c-76f3-44e1-b7f7-cb2be5e43639",
        "ma_vat_tu": "815dde91-3f8a-4a27-8316-9e40859780ae",
        "ma_vat_tu_data": {
            "uuid": "815dde91-3f8a-4a27-8316-9e40859780ae",
            "ma_vt": "VT001",
            "ten_vt": "Vật tư 1",
            "ten_vt2": "Material 1",
            "entity_model": "36d84c13-a4f8-4f13-bd01-fbd0b192cf11",
            "ton_kho_yn": true,
            "lo_yn": false,
            "qc_yn": false,
            ...
        },
        ...
    }
    """
    # Define fields for the serializer
    class Meta:
        model = GiaMuaModel
        fields = ['uuid', 'entity_model', 'ma_vat_tu', 'ma_vat_tu_data',
                  'don_vi_tinh', 'don_vi_tinh_data', 'ngay_hieu_luc',
                  'nha_cung_cap', 'nha_cung_cap_data',
                  'ngoai_te', 'ngoai_te_data',
                  'so_luong_tu', 'gia_mua', 'trang_thai',
                  'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated',
                           'ma_vat_tu_data', 'don_vi_tinh_data',
                           'nha_cung_cap_data', 'ngoai_te_data']

    # Define additional fields for nested data
    ma_vat_tu_data = serializers.SerializerMethodField(read_only=True)
    don_vi_tinh_data = serializers.SerializerMethodField(read_only=True)
    nha_cung_cap_data = serializers.SerializerMethodField(read_only=True)
    ngoai_te_data = serializers.SerializerMethodField(read_only=True)

    def get_ma_vat_tu_data(self, obj):
        """Method field for ma_vat_tu_data"""
        if obj.ma_vat_tu:
            return VatTuSerializer(obj.ma_vat_tu).data
        return None

    def get_don_vi_tinh_data(self, obj):
        """Method field for don_vi_tinh_data"""
        if obj.don_vi_tinh:
            return DonViTinhSerializer(obj.don_vi_tinh).data
        return None

    def get_nha_cung_cap_data(self, obj):
        """Method field for nha_cung_cap_data"""
        if obj.nha_cung_cap:
            return CustomerModelSerializer(obj.nha_cung_cap).data
        return None

    def get_ngoai_te_data(self, obj):
        """Method field for ngoai_te_data"""
        if obj.ngoai_te:
            return NgoaiTeSerializer(obj.ngoai_te).data
        return None

