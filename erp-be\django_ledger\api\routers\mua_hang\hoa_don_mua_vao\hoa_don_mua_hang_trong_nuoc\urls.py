"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the URL patterns for the HoaDonMuaHangTrongNuoc API.
"""

from django.urls import include, path
from django_ledger.api.views.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc import (
    ChiPhiChiTietHoaDonMuaHangTrongNuocViewSet,
    ChiPhiHoaDonMuaHangTrongNuocViewSet,
    ChiTietHoaDonMuaHangTrongNuocViewSet,
    HoaDonMuaHangTrongNuocModelViewSet,
    ThueHoaDonMuaHangTrongNuocViewSet,
)
from rest_framework.routers import DefaultRouter

# Main router for HoaDonMuaHangTrongNuoc
router = DefaultRouter()
router.register(
    "hoa-don-mua-hang-trong-nuoc",
    HoaDonMuaHangTrongNuocModelViewSet,
    basename="hoa-don-mua-hang-trong-nuoc",
)

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path("", include(router.urls)),
    # Nested routes for hoa-don-mua-hang-trong-nuoc
    path(
        "hoa-don-mua-hang-trong-nuoc/<uuid:hoa_don_uuid>/",
        include(
            [
                # Chi tiet hoa don mua hang routes
                path(
                    "chi-tiet-hoa-don/",
                    ChiTietHoaDonMuaHangTrongNuocViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="chi-tiet-hoa-don-mua-hang-list",
                ),
                path(
                    "chi-tiet-hoa-don/<uuid:uuid>/",
                    ChiTietHoaDonMuaHangTrongNuocViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="chi-tiet-hoa-don-mua-hang-detail",
                ),
                # Chi phi hoa don mua hang routes
                path(
                    "chi-phi-hoa-don/",
                    ChiPhiHoaDonMuaHangTrongNuocViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="chi-phi-hoa-don-mua-hang-list",
                ),
                path(
                    "chi-phi-hoa-don/<uuid:uuid>/",
                    ChiPhiHoaDonMuaHangTrongNuocViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="chi-phi-hoa-don-mua-hang-detail",
                ),
                # Chi phi chi tiet hoa don mua hang routes
                path(
                    "chi-phi-chi-tiet-hoa-don/",
                    ChiPhiChiTietHoaDonMuaHangTrongNuocViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="chi-phi-chi-tiet-hoa-don-mua-hang-list",
                ),
                path(
                    "chi-phi-chi-tiet-hoa-don/<uuid:uuid>/",
                    ChiPhiChiTietHoaDonMuaHangTrongNuocViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="chi-phi-chi-tiet-hoa-don-mua-hang-detail",
                ),
                # Thue hoa don mua hang routes
                path(
                    "thue-hoa-don/",
                    ThueHoaDonMuaHangTrongNuocViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="thue-hoa-don-mua-hang-list",
                ),
                path(
                    "thue-hoa-don/<uuid:uuid>/",
                    ThueHoaDonMuaHangTrongNuocViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="thue-hoa-don-mua-hang-detail",
                ),
                path(
                    "chi-tiet-hoa-don/bulk-create/",
                    ChiTietHoaDonMuaHangTrongNuocViewSet.as_view(
                        {"post": "bulk_create"}
                    ),
                    name="chi-tiet-hoa-don-mua-hang-bulk-create",
                ),
                path(
                    "chi-phi-hoa-don/bulk-create/",
                    ChiPhiHoaDonMuaHangTrongNuocViewSet.as_view(
                        {"post": "bulk_create"}
                    ),
                    name="chi-phi-hoa-don-mua-hang-bulk-create",
                ),
            ]
        ),
    ),
]
