"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for HanThanhToan (Payment Terms) model.
"""

from rest_framework import serializers
from django_ledger.models import HanThanhToanModel


class HanThanhToanModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the HanThanhToanModel (Payment Terms) model.

    This serializer handles the conversion between HanThanhToanModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - entity_model: Reference to the entity model (read-only)
    - ma_tt: Unique code for the payment term
    - ten_tt: Primary name of the payment term
    - ten_tt2: Secondary/alternative name (optional)
    - han_tt: Payment term period in days
    - status: Status indicator ('0'=inactive, '1'=active)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    - created_by: Username of creator
    - updated_by: Username of last updater
    """
    class Meta:
        model = HanThanhToanModel
        fields = ['uuid', 'entity_model', 'ma_tt', 'ten_tt', 'ten_tt2', 'han_tt', 'status',
                  'created', 'updated', 'created_by', 'updated_by']
        read_only_fields = ['uuid', 'created', 'updated', 'entity_model']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "title": "Payment Terms",
            "description": "Payment terms model serializer",
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "entity_model": "e7a37307-6381-439c-9e99-0bac858ba821",
                "ma_tt": "N30",
                "ten_tt": "Net 30",
                "ten_tt2": "30 days",
                "han_tt": 30,
                "status": "1",
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z",
                "created_by": "admin",
                "updated_by": "admin"
            }
        }
