"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Admin registration for LyDoTangGiamCCDC (Tool and Equipment Change Reason) model.
"""

from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from django_ledger.models import LyDoTangGiamCCDCModel


class LyDoTangGiamCCDCModelAdmin(admin.ModelAdmin):
    """
    Admin class for the LyDoTangGiamCCDCModel (Tool and Equipment Change Reason) model.
    """
    list_display = ['ma_tg_cc', 'ten_tg_cc', 'loai_tg_cc', 'status', 'created_by', 'updated_by']
    list_filter = ['status', 'loai_tg_cc', 'created', 'updated']
    search_fields = ['ma_tg_cc', 'ten_tg_cc', 'ten_tg_cc2', 'loai_tg_cc']
    readonly_fields = ['uuid', 'created', 'updated']
    fieldsets = [
        (
            _('Basic Information'), {
                'fields': [
                    'entity_model',
                    
                    'loai_tg_cc',
                    'ma_tg_cc',
                    'ten_tg_cc',
                    'ten_tg_cc2',
                    'status',
                ]
            }
        ),
        (
            _('Metadata'), {
                'fields': [
                    'created_by',
                    'updated_by',
                    'uuid',
                    'created',
                    'updated',
                ],
                'classes': ['collapse']
            }
        ),
    ]
