"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for DiaChiNhanHang (Delivery Address) model.
"""

from rest_framework import serializers
from django.shortcuts import get_object_or_404

from django_ledger.models import DiaChiNhanHangModel, KhoHangModel, EntityModel
from django_ledger.api.serializers.warehouse import KhoHangModelSerializer


class DiaChiNhanHangModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the DiaChiNhanHangModel (Delivery Address) model.

    This serializer handles the conversion between DiaChiNhanHangModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original references for foreign key fields
    - Adds additional fields with "_data" suffix that contain the complete nested object data
    - When deserializing, accepts string values for reference fields
    """

    # Add reference data field
    ma_kho_data = serializers.SerializerMethodField()

    class Meta:
        model = DiaChiNhanHangModel
        fields = [
            'uuid', 'entity_model', 'ma_dcnh', 'ten_dcnh', 'ten_dcnh2',
            'ma_kho', 'ma_kho_data', 'status', 'created', 'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated', 'entity_model', 'ma_kho_data']

    def get_ma_kho_data(self, obj):
        """
        Get the warehouse data for the delivery address.

        Parameters
        ----------
        obj : DiaChiNhanHangModel
            The delivery address model instance

        Returns
        -------
        dict
            The warehouse data or None if not found
        """
        try:
            # Get the warehouse directly from the foreign key
            if obj.ma_kho:
                return KhoHangModelSerializer(obj.ma_kho).data
            return None
        except Exception:
            return None

    def validate_ma_dcnh(self, value):
        """
        Validate the ma_dcnh field.

        Parameters
        ----------
        value : str
            The value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError("ma_dcnh is required.")
        if len(value) > 10:
            raise serializers.ValidationError(
                "ma_dcnh must not exceed 10 characters."
            )
        return value

    def validate_ten_dcnh(self, value):
        """
        Validate the ten_dcnh field.

        Parameters
        ----------
        value : str
            The value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError("ten_dcnh is required.")
        if len(value) > 100:
            raise serializers.ValidationError(
                "ten_dcnh must not exceed 100 characters."
            )
        return value

    def validate_ten_dcnh2(self, value):
        """
        Validate the ten_dcnh2 field.

        Parameters
        ----------
        value : str
            The value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if value and len(value) > 100:
            raise serializers.ValidationError(
                "ten_dcnh2 must not exceed 100 characters."
            )
        return value

    def validate_ma_kho(self, value):
        """
        Validate the ma_kho field.

        Parameters
        ----------
        value : KhoHangModel
            The warehouse model instance

        Returns
        -------
        KhoHangModel
            The validated warehouse model instance

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError("ma_kho is required.")
        return value

    def validate_status(self, value):
        """
        Validate the status field.

        Parameters
        ----------
        value : str
            The value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if value not in ['0', '1']:
            raise serializers.ValidationError(
                "status must be either '0' or '1'."
            )
        return value

    def to_internal_value(self, data):
        """
        Override to_internal_value to handle ma_kho field when it's provided as a string UUID.

        Parameters
        ----------
        data : dict
            The data dictionary to convert

        Returns
        -------
        dict
            The converted data dictionary
        """
        # Make a copy of the data to avoid modifying the original
        internal_data = data.copy() if isinstance(data, dict) else data

        # Handle ma_kho field if provided as a string UUID
        if 'ma_kho' in internal_data and internal_data['ma_kho'] and isinstance(internal_data['ma_kho'], str):
            # Get entity_slug from context
            entity_slug = None
            if self.context and 'entity_slug' in self.context:
                entity_slug = self.context['entity_slug']

            if entity_slug:
                try:
                    # Try to get the warehouse by UUID
                    warehouse = KhoHangModel.objects.filter(
                        entity_model__slug=entity_slug,
                        uuid=internal_data['ma_kho']
                    ).first()

                    if warehouse:
                        # Replace the UUID string with the actual warehouse model instance
                        internal_data['ma_kho'] = warehouse
                except Exception:
                    # If there's an error, let the validation handle it
                    pass

        # Call the parent method to handle the rest of the fields
        return super().to_internal_value(internal_data)

    def validate(self, data):
        """
        Validate the entire data dictionary.

        Parameters
        ----------
        data : dict
            The data dictionary to validate

        Returns
        -------
        dict
            The validated data

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        # When creating a new instance, check required fields
        if not self.instance:  # Create operation
            required_fields = ['ma_dcnh', 'ten_dcnh', 'ma_kho']
            for field in required_fields:
                if field not in data:
                    raise serializers.ValidationError({
                        field: f"{field} is required."
                    })

        return data
