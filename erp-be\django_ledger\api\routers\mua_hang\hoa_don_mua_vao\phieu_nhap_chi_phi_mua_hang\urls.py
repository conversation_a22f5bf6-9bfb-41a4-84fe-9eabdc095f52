"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the URL patterns for the PhieuNhapChiPhiMuaHang API.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import (
    PhieuNhapChiPhiMuaHangViewSet,
    ChiTietPhieuNhapChiPhiMuaHangViewSet,
    ChiPhiPhieuNhapChiPhiMuaHangViewSet,
    ChiPhiChiTietPhieuNhapChiPhiMuahangViewSet,
    ThuePhieuNhapChiPhiMuaHangViewSet
)

# Main router for PhieuNhapChiPhiMuaHang
router = DefaultRouter()
router.register('phieu-nhap-chi-phi-mua-hang', PhieuNhapChiPhiMuaHangViewSet, basename='phieu-nhap-chi-phi-mua-hang')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
    
    # Nested routes for phieu-nhap-chi-phi-mua-hang
    path('phieu-nhap-chi-phi-mua-hang/<uuid:phieu_nhap_uuid>/', include([
        # Chi tiet phieu nhap chi phi mua hang routes
        path('chi-tiet/', ChiTietPhieuNhapChiPhiMuaHangViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-tiet-phieu-nhap-chi-phi-mua-hang-list'),

        path('chi-tiet/<uuid:uuid>/', ChiTietPhieuNhapChiPhiMuaHangViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-tiet-phieu-nhap-chi-phi-mua-hang-detail'),
        
        # Chi phi phieu nhap chi phi mua hang routes
        path('chi-phi/', ChiPhiPhieuNhapChiPhiMuaHangViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-phi-phieu-nhap-chi-phi-mua-hang-list'),

        path('chi-phi/<uuid:uuid>/', ChiPhiPhieuNhapChiPhiMuaHangViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-phi-phieu-nhap-chi-phi-mua-hang-detail'),
        
        # Chi phi chi tiet phieu nhap chi phi mua hang routes
        path('chi-phi-chi-tiet/', ChiPhiChiTietPhieuNhapChiPhiMuahangViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-phi-chi-tiet-phieu-nhap-chi-phi-mua-hang-list'),

        path('chi-phi-chi-tiet/<uuid:uuid>/', ChiPhiChiTietPhieuNhapChiPhiMuahangViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-phi-chi-tiet-phieu-nhap-chi-phi-mua-hang-detail'),
        
        # Thue phieu nhap chi phi mua hang routes
        path('thue/', ThuePhieuNhapChiPhiMuaHangViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='thue-phieu-nhap-chi-phi-mua-hang-list'),

        path('thue/<uuid:uuid>/', ThuePhieuNhapChiPhiMuaHangViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='thue-phieu-nhap-chi-phi-mua-hang-detail'),
    ])),
]
