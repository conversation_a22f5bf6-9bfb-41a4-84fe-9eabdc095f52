"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Report Serializers Module
"""
from rest_framework import serializers

class ReportRequestSerializer(serializers.Serializer):
    """Serializer for report requests."""
    
    from_date = serializers.DateField(required=False)
    to_date = serializers.DateField(required=False)
    
    def validate(self, attrs):
        """Validate date range."""
        from_date = attrs.get('from_date')
        to_date = attrs.get('to_date')
        
        if from_date and to_date and from_date > to_date:
            raise serializers.ValidationError(
                'From date must be before to date'
            )
            
        return attrs
