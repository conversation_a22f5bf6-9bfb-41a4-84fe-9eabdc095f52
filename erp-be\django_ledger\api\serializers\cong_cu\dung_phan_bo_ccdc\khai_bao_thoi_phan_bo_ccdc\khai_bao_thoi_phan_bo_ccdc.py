"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

KhaiBaoThoiPhanBoCCDC (Tool Allocation Time Declaration) serializer implementation.
"""

from rest_framework import serializers

from django_ledger.api.serializers.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc import KhaiBaoThongTinCCDCModelSerializer
from django_ledger.models.cong_cu.dung_phan_bo_ccdc.khai_bao_thoi_phan_bo_ccdc import KhaiBaoThoiPhanBoCCDCModel
from django_ledger.models.cong_cu.khai_bao_tang_giam_ccdc.khai_bao_thong_tin_ccdc import KhaiBaoThongTinCCDCModel


class KhaiBaoThongTinCCDCSimpleSerializer(serializers.ModelSerializer):
    """
    Simple serializer for KhaiBaoThongTinCCDCModel that only returns ma_cc and ten_cc.
    """
    class Meta:
        model = KhaiBaoThongTinCCDCModel
        fields = ['ma_cc', 'ten_cc']


class KhaiBaoThoiPhanBoCCDCModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the KhaiBaoThoiPhanBoCCDCModel.

    This serializer handles the conversion between KhaiBaoThoiPhanBoCCDCModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields (entity_model, ma_cc)
    - Adds additional fields with "_data" suffix (entity_model_data, ma_cc_data)
      that contain the complete nested object data with all available fields
    - When deserializing, accepts UUIDs for reference fields
    """
    # Add nested serializers for foreign key fields
    ma_cc_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = KhaiBaoThoiPhanBoCCDCModel
        fields = [
            'uuid',
            'entity_model',
            'ma_cc',
            'ma_cc_data',
            'ngay_kh1',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'ma_cc_data',
            'created',
            'updated'
        ]

    def get_ma_cc_data(self, obj):
        """
        Returns the ma_cc data for the ma_cc field (only ma_cc and ten_cc).
        """
        if obj.ma_cc:
            return KhaiBaoThongTinCCDCSimpleSerializer(obj.ma_cc).data
        return None
