"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for VatTuQuyDoiDVTModel.
"""
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from django_ledger.models import VatTuModel, DonViTinhModel
from django_ledger.models.vat_tu.vat_tu_quy_doi_dvt import VatTuQuyDoiDVTModel
from django_ledger.api.serializers.base import GlobalModelSerializer
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer


class VatTuQuyDoiDVTSerializer(GlobalModelSerializer):
    """
    Serializer class for VatTuQuyDoiDVTModel that handles data serialization/deserialization.
    Inherits from GlobalModelSerializer for common fields and behaviors.
    """
    # Read-only fields for related objects
    dvt_data = serializers.SerializerMethodField(read_only=True)
    vat_tu_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = VatTuQuyDoiDVTModel
        fields = [
            'uuid',
            'vat_tu_id',
            'vat_tu_data',
            'dvt',
            'dvt_data',
            'heso',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated']

    def get_dvt_data(self, obj):
        """
        Get basic information about the unit of measurement
        """
        if not obj.dvt:
            return None
        
        return DonViTinhSerializer(obj.dvt).data

    def get_vat_tu_data(self, obj):
        """
        Get basic information about the material
        """
        if not obj.vat_tu_id:
            return None
        
        vat_tu = obj.vat_tu_id
        return {
            'uuid': str(vat_tu.uuid),
            'ma_vt': vat_tu.ma_vt,
            'ten_vt': vat_tu.ten_vt
        }

    def validate(self, attrs):
        """
        Custom validation for VatTuQuyDoiDVT data.

        Parameters
        ----------
        attrs: dict
            Dictionary of field values to validate

        Returns
        -------
        dict
            Validated data

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        attrs = super().validate(attrs)

        # Required fields validation
        required_fields = ['vat_tu_id', 'dvt', 'heso']
        for field in required_fields:
            if field not in attrs or not attrs.get(field):
                raise serializers.ValidationError({
                    field: _('This field is required.')
                })

        # Validate material exists
        if 'vat_tu_id' in attrs and isinstance(attrs['vat_tu_id'], str):
            try:
                attrs['vat_tu_id'] = VatTuModel.objects.get(uuid=attrs['vat_tu_id'])
            except VatTuModel.DoesNotExist:
                raise serializers.ValidationError({
                    'vat_tu_id': _('Material with this UUID does not exist.')
                })

        # Validate unit exists
        if 'dvt' in attrs and isinstance(attrs['dvt'], str):
            try:
                attrs['dvt'] = DonViTinhModel.objects.get(uuid=attrs['dvt'])
            except DonViTinhModel.DoesNotExist:
                raise serializers.ValidationError({
                    'dvt': _('Unit with this UUID does not exist.')
                })

        # Validate conversion factor is positive
        if 'heso' in attrs and attrs['heso'] <= 0:
            raise serializers.ValidationError({
                'heso': _('Conversion factor must be positive.')
            })

        # Check for duplicate unit conversion
        if self.instance is None and 'vat_tu_id' in attrs and 'dvt' in attrs:
            # Only check for duplicates when creating a new record
            vat_tu_id = attrs['vat_tu_id']
            dvt = attrs['dvt']
            
            if VatTuQuyDoiDVTModel.objects.filter(vat_tu_id=vat_tu_id, dvt=dvt).exists():
                raise serializers.ValidationError({
                    'dvt': _('A conversion for this unit already exists for this material.')
                })

        return attrs

    def create(self, validated_data):
        """
        Create a new VatTuQuyDoiDVT instance.
        
        Parameters
        ----------
        validated_data: dict
            Validated data for creating the instance
            
        Returns
        -------
        VatTuQuyDoiDVTModel
            The created instance
        """
        return super().create(validated_data)

    def update(self, instance, validated_data):
        """
        Update an existing VatTuQuyDoiDVT instance.
        
        Parameters
        ----------
        instance: VatTuQuyDoiDVTModel
            The instance to update
        validated_data: dict
            Validated data for updating the instance
            
        Returns
        -------
        VatTuQuyDoiDVTModel
            The updated instance
        """
        # Update fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
            
        instance.save()
        return instance
