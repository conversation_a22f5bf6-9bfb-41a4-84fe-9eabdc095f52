"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for TyGia (Exchange Rate) model.
"""

from rest_framework import serializers
from django_ledger.models import TyGiaModel, NgoaiTeModel
from django_ledger.api.serializers.base import BaseModelSerializer, GlobalModelSerializer

class NgoaiTeSimpleSerializer(GlobalModelSerializer):
    """
    Simplified serializer for the NgoaiTeModel (Currency) model.

    This serializer only includes basic currency information without nested account data.
    It's used to prevent deep nesting in the TyGia serializer response.
    """
    class Meta:
        model = NgoaiTeModel
        fields = ['uuid', 'ma_nt', 'ten_nt', 'ten_nt2', 'stt', 'ra_ndec', 'cach_doc', 'status', 'entity_model']
        read_only_fields = ['uuid', 'created', 'updated']

class TyGiaModelSerializer(BaseModelSerializer):
    """
    Serializer for the TyGiaModel (Exchange Rate) model.

    This serializer handles the conversion between TyGiaModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - ma_nt: Currency code (foreign key to NgoaiTeModel)
    - ma_nt_data: Complete data for the currency (read-only)
    - ngay_hl: Effective date of the exchange rate
    - ty_gia: Exchange rate value
    - status: Status indicator ('0'=inactive, '1'=active)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    """
    # Define additional field for nested currency data
    ma_nt_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = TyGiaModel
        fields = ['uuid', 'ma_nt', 'ma_nt_data', 'ngay_hl', 'ty_gia', 'status', 'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated', 'ma_nt_data']

    # Example response for Swagger documentation
    swagger_schema_fields = {
        "example": {
            "uuid": "123e4567-e89b-12d3-a456-************",
            "ma_nt": "USD",
            "ma_nt_data": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_nt": "USD",
                "ten_nt": "US Dollar",
                "ten_nt2": "Đô la Mỹ",
                "stt": 1,
                "ra_ndec": 2,
                "cach_doc": "đô la",
                "status": "1",
                "entity_model": "36d84c13-a4f8-4f13-bd01-fbd0b192cf11"
            },
            "ngay_hl": "2023-04-21",
            "ty_gia": "23500.0000",
            "status": "1",
            "created": "2023-04-21T10:30:00Z",
            "updated": "2023-04-21T10:30:00Z"
        }
    }

    def update(self, instance, validated_data):
        """
        Override update method to allow partial updates
        """
        instance.ty_gia = validated_data.get('ty_gia', instance.ty_gia)
        instance.status = validated_data.get('status', instance.status)

        # Only update ma_nt and ngay_hl if they are provided
        # This avoids unique constraint violations
        if 'ma_nt' in validated_data:
            instance.ma_nt = validated_data.get('ma_nt')
        if 'ngay_hl' in validated_data:
            instance.ngay_hl = validated_data.get('ngay_hl')

        instance.save()
        return instance

    def get_ma_nt_data(self, obj):
        """Method field for ma_nt_data"""
        if obj.ma_nt:
            return NgoaiTeSimpleSerializer(obj.ma_nt).data
        return None

    def validate_ty_gia(self, value):
        """Validate that exchange rate is positive"""
        if value <= 0:
            raise serializers.ValidationError("Exchange rate must be positive")
        return value
