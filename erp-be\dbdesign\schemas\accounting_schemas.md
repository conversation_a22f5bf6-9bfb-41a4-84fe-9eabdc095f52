# Sơ Đồ Cơ Sở Dữ Liệu Kế Toán - Django Ledger

Tài liệu này mô tả chi tiết cấu trúc cơ sở dữ liệu cho các thực thể liên quan đến kế toán trong hệ thống Django Ledger.

## Mụ<PERSON>

- [Sơ Đồ Cơ Sở Dữ Liệu Kế Toán - Django <PERSON>ger](#sơ-đồ-cơ-sở-dữ-liệu-kế-toán---django-ledger)
  - [<PERSON><PERSON><PERSON>](#mục-lục)
  - [ChartOfAccountModel](#chartofaccountmodel)
  - [AccountModel](#accountmodel)
  - [LedgerModel](#ledgermodel)
  - [JournalEntryModel](#journalentrymodel)
  - [TransactionModel](#transactionmodel)
  - [ClosingEntryModel](#closingentrymodel)

## ChartOfAccountModel

Đại diện cho một sơ đồ tài kho<PERSON>, tức là một tập hợp các tài khoản được tổ chức.

| Trường | Kiểu | <PERSON>ô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| name | CharField | Tên sơ đồ tài khoản |
| slug | SlugField | Tên thân thiện với URL |
| entity | ForeignKey(EntityModel) | Đơn vị liên quan |
| description | TextField | Mô tả (tùy chọn) |
| active | BooleanField | Sơ đồ tài khoản có đang hoạt động |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: ChartOfAccountModel là một tập hợp các tài khoản được tổ chức theo cách phản ánh cấu trúc tài chính của đơn vị. Mỗi đơn vị có thể có nhiều sơ đồ tài khoản, nhưng chỉ có một được đặt làm mặc định.

## AccountModel

Đại diện cho một tài khoản riêng lẻ trong sơ đồ tài khoản.

| Trường | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| code | CharField | Mã tài khoản (chữ số và chữ cái) |
| name | CharField | Tên tài khoản |
| role | CharField | Vai trò tài khoản (tài sản, nợ phải trả, vốn chủ sở hữu, v.v.) |
| role_default | BooleanField | Đây có phải là tài khoản mặc định cho vai trò này |
| balance_type | CharField | Loại số dư (ghi nợ hoặc ghi có) |
| locked | BooleanField | Tài khoản có bị khóa không |
| active | BooleanField | Tài khoản có đang hoạt động |
| coa_model | ForeignKey(ChartOfAccountModel) | Sơ đồ tài khoản liên quan |
| path | PathField | Đường dẫn trong cấu trúc cây |
| depth | IntegerField | Độ sâu trong cấu trúc cây |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: AccountModel đại diện cho các tài khoản riêng lẻ được sử dụng trong kế toán. Mỗi tài khoản có một vai trò cụ thể, ảnh hưởng đến cách nó xuất hiện trên báo cáo tài chính và cách nó được xử lý trong giao dịch.

**Điểm đặc biệt**:
- AccountModel sử dụng cấu trúc cây MP (Materialized Path) để hỗ trợ phân cấp tài khoản
- Mỗi AccountModel có một loại số dư: DEBIT hoặc CREDIT, ảnh hưởng đến cách nó hoạt động trong hệ thống kế toán kép

## LedgerModel

Đại diện cho một sổ cái, tức là một tập hợp các bút toán được tổ chức.

| Trường | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| name | CharField | Tên sổ cái |
| ledger_xid | SlugField | Định danh bên ngoài |
| entity | ForeignKey(EntityModel) | Đơn vị liên quan |
| posted | BooleanField | Sổ cái đã được ghi vào sổ cái chính |
| locked | BooleanField | Sổ cái đã bị khóa |
| hidden | BooleanField | Sổ cái bị ẩn |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: LedgerModel là một tập hợp các bút toán liên quan. Sổ cái có thể được sử dụng để tổ chức các giao dịch tài chính theo loại, mục đích hoặc dự án.

## JournalEntryModel

Đại diện cho một bút toán, tức là một tập hợp các giao dịch cân bằng.

| Trường | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| je_number | SlugField | Số bút toán |
| timestamp | DateTimeField | Thời điểm của bút toán |
| description | CharField | Mô tả (tùy chọn) |
| entity_unit | ForeignKey(EntityUnitModel) | Đơn vị liên quan (tùy chọn) |
| activity | CharField | Loại hoạt động (hoạt động, đầu tư, tài chính) |
| origin | CharField | Nguồn gốc/kích hoạt của bút toán |
| posted | BooleanField | Bút toán đã được ghi vào sổ cái |
| locked | BooleanField | Bút toán đã bị khóa |
| is_closing_entry | BooleanField | Đây có phải là bút toán đóng sổ |
| ledger | ForeignKey(LedgerModel) | Sổ cái liên quan |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: JournalEntryModel là một tập hợp các giao dịch cân bằng (tổng ghi nợ = tổng ghi có) ảnh hưởng đến các tài khoản khác nhau. Bút toán là đơn vị cơ bản của kế toán kép.

**Điểm đặc biệt**:
- Bút toán phải cân bằng để được đánh dấu là hợp lệ
- Bút toán có thể có một hoạt động liên quan (hoạt động, đầu tư, tài chính) được sử dụng trong báo cáo lưu chuyển tiền tệ
- Bút toán phải được gắn với một sổ cái để có thể được ghi vào sổ cái chính

## TransactionModel

Đại diện cho một giao dịch riêng lẻ ảnh hưởng đến một tài khoản.

| Trường | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| tx_type | CharField | Loại giao dịch (ghi nợ hoặc ghi có) |
| journal_entry | ForeignKey(JournalEntryModel) | Bút toán liên quan |
| account | ForeignKey(AccountModel) | Tài khoản liên quan |
| amount | DecimalField | Số tiền giao dịch |
| description | CharField | Mô tả (tùy chọn) |
| cleared | BooleanField | Giao dịch đã được xác nhận |
| reconciled | BooleanField | Giao dịch đã được đối chiếu |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: TransactionModel đại diện cho một ghi nợ hoặc ghi có đơn lẻ ảnh hưởng đến một tài khoản cụ thể. Các giao dịch luôn được nhóm trong một bút toán và chỉ có hiệu lực khi bút toán được ghi vào sổ cái.

## ClosingEntryModel

Đại diện cho một bút toán đóng sổ tại cuối kỳ kế toán.

| Trường | Kiểu | Mô tả |
|-------|------|-------------|
| uuid | UUIDField | Khóa chính |
| entity_model | ForeignKey(EntityModel) | Đơn vị liên quan |
| closing_date | DateField | Ngày đóng sổ |
| ledger_model | OneToOneField(LedgerModel) | Sổ cái liên quan |
| posted | BooleanField | Bút toán đã được ghi vào sổ cái chính |
| created | DateTimeField | Thời điểm tạo |
| updated | DateTimeField | Thời điểm cập nhật gần nhất |

**Mục đích**: ClosingEntryModel đại diện cho các bút toán đóng sổ đặc biệt, được tạo ra vào cuối kỳ kế toán để đóng các tài khoản tạm thời (thu nhập, chi phí) và chuyển số dư sang tài khoản vốn chủ sở hữu.
