"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for LyDoTangGiamCCDC API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.danh_muc.tai_san_va_cong_cu.ly_do_tang_giam_ccdc import LyDoTangGiamCCDCViewSet

# Main router for LyDoTangGiamCCDC
router = DefaultRouter()
router.register('', LyDoTangGiamCCDCViewSet, basename='ly-do-tang-giam-ccdc')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
