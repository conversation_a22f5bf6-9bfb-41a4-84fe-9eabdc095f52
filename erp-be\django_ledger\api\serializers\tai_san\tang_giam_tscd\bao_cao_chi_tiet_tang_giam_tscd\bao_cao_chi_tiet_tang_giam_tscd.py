"""
Django <PERSON>ger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Bao Cao Chi Tiet Tang Giam TSCD (Detailed Fixed Asset Increase/Decrease Report) API.
"""

from rest_framework import serializers


class BaoCaoChiTietTangGiamTSCDRequestSerializer(serializers.Serializer):
    """
    Serializer for validating Detailed Fixed Asset Increase/Decrease Report request data.
    
    This serializer validates POST body data for filtering the report.
    Based on the curl request parameters from the user.
    """
    
    # Required period fields
    tu_ky = serializers.IntegerField(required=True, help_text="From period (month)")
    tu_nam = serializers.IntegerField(required=True, help_text="From year")
    den_ky = serializers.IntegerField(required=True, help_text="To period (month)")
    den_nam = serializers.IntegerField(required=True, help_text="To year")
    
    # Optional filter fields - using <PERSON><PERSON><PERSON><PERSON> for UUID/string flexibility
    ma_lts = serializers.Char<PERSON><PERSON>(required=False, allow_blank=True, help_text="Asset type code/UUID")
    ma_bp = serializers.CharField(required=False, allow_blank=True, help_text="Department code/UUID")
    nh_ts1 = serializers.CharField(required=False, allow_blank=True, help_text="Asset group 1")
    nh_ts2 = serializers.CharField(required=False, allow_blank=True, help_text="Asset group 2")
    nh_ts3 = serializers.CharField(required=False, allow_blank=True, help_text="Asset group 3")
    
    # Asset increase/decrease type
    loai_tg_ts = serializers.CharField(required=False, allow_blank=True, help_text="Asset increase/decrease type")
    
    # Standard report fields
    ma_unit = serializers.CharField(required=False, allow_blank=True, help_text="Unit UUID")
    mau_bc = serializers.IntegerField(required=False, default=20, help_text="Report template")
    data_analysis_struct = serializers.CharField(required=False, allow_blank=True, help_text="Data analysis structure")


class BaoCaoChiTietTangGiamTSCDResponseSerializer(serializers.Serializer):
    """
    Serializer for formatting Detailed Fixed Asset Increase/Decrease Report response data.
    
    This serializer formats the report output data based on expected response fields.
    """
    
    # Sequential number
    stt = serializers.IntegerField(help_text="Sequential number")
    
    # Asset information
    ma_ts = serializers.CharField(help_text="Asset code")
    ten_ts = serializers.CharField(help_text="Asset name")
    ma_bp = serializers.CharField(help_text="Department code")
    ngay_mua = serializers.CharField(help_text="Purchase date")
    so_ky_kh = serializers.IntegerField(help_text="Depreciation periods")
    
    # Asset classification
    ma_lts = serializers.CharField(help_text="Asset type code")
    ma_tg_ts = serializers.CharField(help_text="Asset increase/decrease reason code")
    
    # Financial values
    nguyen_gia = serializers.DecimalField(max_digits=15, decimal_places=2, help_text="Original value")
    gt_da_kh = serializers.DecimalField(max_digits=15, decimal_places=2, help_text="Accumulated depreciation")
    gt_cl = serializers.DecimalField(max_digits=15, decimal_places=2, help_text="Remaining value")
    
    # Related names
    ten_bp = serializers.CharField(help_text="Department name")
    ten_lts = serializers.CharField(help_text="Asset type name")
    ten_tg_ts = serializers.CharField(help_text="Asset increase/decrease reason name")
    nh_ts1 = serializers.CharField(help_text="Asset group 1 name")
    nh_ts2 = serializers.CharField(help_text="Asset group 2 name")
