"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for GiayBaoCo model.
"""

from rest_framework import serializers

from django.utils.translation import gettext_lazy as _

from django_ledger.models import GiayBaoCoModel
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.bank_account import BankAccountModelSerializer
from django_ledger.api.serializers.accounts import AccountModelSerializer
from django_ledger.api.serializers.unit import EntityUnitModelSimpleSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer
from django_ledger.api.serializers.han_thanh_toan import HanThanhToanModelSerializer


class GiayBaoCoSerializer(serializers.ModelSerializer):
    """
    Serializer for GiayBaoCo model.
    """
    # Read-only fields for related objects
    tknh_data = serializers.SerializerMethodField(read_only=True)
    tk_data = serializers.SerializerMethodField(read_only=True)
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)
    ma_tt_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)

    # Child data
    chi_tiet_giay_bao_co_data = serializers.SerializerMethodField(read_only=True)
    chi_tiet_giay_bao_co_items = serializers.ListField(required=False, write_only=True)

    phieu_ngan_hang_giay_bao_co_data = serializers.SerializerMethodField(read_only=True)
    phieu_ngan_hang_giay_bao_co_items = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = GiayBaoCoModel
        fields = [
            'uuid',
            'entity_model',
            'ma_ngv',
            'dia_chi',
            'ong_ba',
            'dien_giai',
            'tknh',
            'tknh_data',
            'tk',
            'tk_data',
            'unit_id',
            'unit_id_data',
            'i_so_ct',
            'ma_nk',
            'so_ct',
            'ngay_ct',
            'ngay_lct',
            'ma_nt',
            'ma_nt_data',
            'ty_gia',
            'status',
            'transfer_yn',
            'hd_yn',
            'so_ct0',
            'ngay_ct0',
            'ma_tt',
            'ma_tt_data',
            'tg_dd',
            'cltg_yn',
            'ma_kh',
            'ma_kh_data',
            'so_ct_goc',
            'dien_giai_ct_goc',
            't_tien_nt',
            't_tien',
            'chi_tiet_giay_bao_co_data',
            'chi_tiet_giay_bao_co_items',
            'phieu_ngan_hang_giay_bao_co_data',
            'phieu_ngan_hang_giay_bao_co_items',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'tknh_data',
            'tk_data',
            'unit_id_data',
            'ma_nt_data',
            'ma_tt_data',
            'ma_kh_data',
            'chi_tiet_giay_bao_co_data',
            'phieu_ngan_hang_giay_bao_co_data',
            'created',
            'updated'
        ]


    def get_tknh_data(self, obj):
        """
        Get bank account data.
        """
        if obj.tknh:
            return BankAccountModelSerializer(obj.tknh).data
        return None

    def get_tk_data(self, obj):
        """
        Get account data.
        """
        if obj.tk:
            return AccountModelSerializer(obj.tk).data
        return None

    def get_unit_id_data(self, obj):
        """
        Get entity unit data.
        """
        if obj.unit_id:
            return EntityUnitModelSimpleSerializer(obj.unit_id).data
        return None

    def get_ma_nt_data(self, obj):
        """
        Get currency data.
        """
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_ma_tt_data(self, obj):
        """
        Get payment term data.
        """
        if obj.ma_tt:
            return HanThanhToanModelSerializer(obj.ma_tt).data
        return None

    def get_ma_kh_data(self, obj):
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_chi_tiet_giay_bao_co_data(self, obj):
        """
        Get chi tiet giay bao co data.
        """
        from django_ledger.api.serializers.tien_gui.hach_toan.giay_bao_co.chi_tiet_giay_bao_co import ChiTietGiayBaoCoSerializer
        chi_tiet = obj.chi_tiet_giay_bao_co.all()
        return ChiTietGiayBaoCoSerializer(chi_tiet, many=True, context=self.context).data

    def get_phieu_ngan_hang_giay_bao_co_data(self, obj):
        """
        Get phieu ngan hang giay bao co data.
        """
        from django_ledger.api.serializers.tien_gui.hach_toan.giay_bao_co.phieu_ngan_hang_giay_bao_co import PhieuNganHangGiayBaoCoSerializer
        phieu_ngan_hang = obj.phieu_ngan_hang_giay_bao_co.all()
        return PhieuNganHangGiayBaoCoSerializer(phieu_ngan_hang, many=True, context=self.context).data
