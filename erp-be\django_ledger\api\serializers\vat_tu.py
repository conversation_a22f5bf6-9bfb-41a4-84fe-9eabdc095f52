"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Vat Tu Serializers for API endpoints.
"""

from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.color import ColorModelSerializer
from django_ledger.api.serializers.danh_muc.ban_hang.gia_ban.gia_ban import (
    GiaBanSerializer,
)
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.group import GroupModelSerializer
from django_ledger.api.serializers.kich_co import KichCoModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.quoc_gia import QuocGiaModelSerializer
from django_ledger.api.serializers.tax import TaxModelSerializer
from django_ledger.api.serializers.vat_tu_quy_doi_dvt import VatTuQuyDoiDVTSerializer
from django_ledger.api.serializers.vi_tri import ViTriModelSerializer
from django_ledger.api.serializers.warehouse import KhoHangModelSerializer
from django_ledger.models import (
    BoPhanModel,
    ColorModel,
    DonViTinhModel,
    GiaBanModel,
    GroupModel,
    KhoHangModel,
    KichCoModel,
    QuocGiaModel,
    TaxModel,
    VatTuModel,
    VatTuQuyDoiDVTModel,
    ViTriModel,
)
from django_ledger.models.utils import lazy_loader
from rest_framework import serializers


class VatTuSerializer(serializers.ModelSerializer):
    """
    Serializer for VatTu model. Handles conversion between VatTu model instances and JSON representations.
    """

    # Nested serializers
    dvt_data = DonViTinhSerializer(read_only=True, source="dvt")
    # Group fields - these are regular fields that can be written to
    # The data fields are for read operations
    nh_vt1_data = serializers.SerializerMethodField(read_only=True)
    nh_vt2_data = serializers.SerializerMethodField(read_only=True)
    nh_vt3_data = serializers.SerializerMethodField(read_only=True)
    ma_thue = TaxModelSerializer(read_only=True)
    ma_thue_nk = TaxModelSerializer(read_only=True)

    # Reference data fields
    ma_kho_data = serializers.SerializerMethodField(read_only=True)
    ma_vi_tri_data = serializers.SerializerMethodField(read_only=True)
    nuoc_sx_data = serializers.SerializerMethodField(read_only=True)
    mau_sac_data = serializers.SerializerMethodField(read_only=True)
    kich_co_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)

    # Account data fields
    tk_khcp_data = serializers.SerializerMethodField(read_only=True)
    tk_dt_data = serializers.SerializerMethodField(read_only=True)
    tk_gv_data = serializers.SerializerMethodField(read_only=True)
    tk_ck_data = serializers.SerializerMethodField(read_only=True)
    tk_km_data = serializers.SerializerMethodField(read_only=True)
    tk_tl_data = serializers.SerializerMethodField(read_only=True)
    tk_spdd_data = serializers.SerializerMethodField(read_only=True)
    tk_cpnvl_data = serializers.SerializerMethodField(read_only=True)

    # Related models data for read operations
    quy_doi_dvt_data = VatTuQuyDoiDVTSerializer(
        source="quy_doi_dvt", many=True, read_only=True
    )
    gia_ban_data = serializers.SerializerMethodField(read_only=True)

    # Direct nested data fields for write operations
    quy_doi_dvt = serializers.ListField(
        child=serializers.JSONField(),
        required=False,
        write_only=True,
        help_text=_("List of unit conversions to create for this material"),
    )
    # Price lists should be created separately through the gia_ban API
    gia_ban = serializers.ListField(
        child=serializers.JSONField(),
        required=False,
        write_only=True,
        help_text=_(
            "DEPRECATED: Price lists should be created separately through the gia_ban API"
        ),
    )

    # Custom field for attributes (which has a DB column name "#attr")
    attributes = serializers.JSONField(source="#attr", required=False, allow_null=True)

    def to_internal_value(self, data):
        """
        Override to_internal_value to handle UUID conversion before validation
        """
        # Make a copy of the data to avoid modifying the input directly
        data_copy = data.copy()

        # Call the parent's to_internal_value with the modified data
        return super().to_internal_value(data_copy)

    class Meta:
        model = VatTuModel
        fields = [
            "uuid",
            "entity_model",
            "ma_vt",
            "ton_kho_yn",
            "lo_yn",
            "qc_yn",
            "ten_vt",
            "ten_vt2",
            "dvt",
            "dvt_data",
            "gia_ton",
            "ma_lvt",
            "nh_vt1",
            "nh_vt1_data",
            "nh_vt2",
            "nh_vt2_data",
            "nh_vt3",
            "nh_vt3_data",
            "ma_kho",
            "ma_kho_data",
            "ma_vi_tri",
            "ma_vi_tri_data",
            "ma_thue",
            "ma_thue_nk",
            "ma_phu",
            "tk_khcp",
            "tk_khcp_data",
            "sua_tk_vt",
            "tk_dt",
            "tk_dt_data",
            "tk_gv",
            "tk_gv_data",
            "tk_ck",
            "tk_ck_data",
            "tk_km",
            "tk_km_data",
            "tk_tl",
            "tk_tl_data",
            "tk_spdd",
            "tk_spdd_data",
            "tk_cpnvl",
            "tk_cpnvl_data",
            "the_tich",
            "khoi_luong",
            "nuoc_sx",
            "nuoc_sx_data",
            "mau_sac",
            "mau_sac_data",
            "kich_co",
            "kich_co_data",
            "kieu_hd",
            "ma_bp",
            "ma_bp_data",
            "sl_min",
            "sl_max",
            "ngay_nhap",
            "ngay_xuat",
            "ghi_chu",
            "status",
            "image_id",
            "attributes",
            "quy_doi_dvt",
            "quy_doi_dvt_data",
            "gia_ban",
            "gia_ban_data",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "entity_model",
            "created",
            "updated",
            "dvt_data",
            "nh_vt1_data",
            "nh_vt2_data",
            "nh_vt3_data",
            "ma_kho_data",
            "ma_vi_tri_data",
            "nuoc_sx_data",
            "mau_sac_data",
            "kich_co_data",
            "ma_bp_data",
            "tk_khcp_data",
            "tk_dt_data",
            "tk_gv_data",
            "tk_ck_data",
            "tk_km_data",
            "tk_tl_data",
            "tk_spdd_data",
            "tk_cpnvl_data",
        ]

    def get_ma_kho_data(self, obj):
        """Method field for ma_kho_data"""
        if obj.ma_kho:
            return KhoHangModelSerializer(obj.ma_kho).data
        return None

    def get_ma_vi_tri_data(self, obj):
        """Method field for ma_vi_tri_data"""
        if obj.ma_vi_tri:
            return ViTriModelSerializer(obj.ma_vi_tri).data
        return None

    def get_nuoc_sx_data(self, obj):
        """Method field for nuoc_sx_data"""
        if obj.nuoc_sx:
            return QuocGiaModelSerializer(obj.nuoc_sx).data
        return None

    def get_mau_sac_data(self, obj):
        """Method field for mau_sac_data"""
        if obj.mau_sac:
            return ColorModelSerializer(obj.mau_sac).data
        return None

    def get_kich_co_data(self, obj):
        """Method field for kich_co_data"""
        if obj.kich_co:
            return KichCoModelSerializer(obj.kich_co).data
        return None

    def get_ma_bp_data(self, obj):
        """Method field for ma_bp_data"""
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_nh_vt1_data(self, obj):
        """Method field for nh_vt1_data"""
        if obj.nh_vt1:
            return GroupModelSerializer(obj.nh_vt1).data
        return None

    def get_nh_vt2_data(self, obj):
        """Method field for nh_vt2_data"""
        if obj.nh_vt2:
            return GroupModelSerializer(obj.nh_vt2).data
        return None

    def get_nh_vt3_data(self, obj):
        """Method field for nh_vt3_data"""
        if obj.nh_vt3:
            return GroupModelSerializer(obj.nh_vt3).data
        return None

    def get_tk_khcp_data(self, obj):
        """Method field for tk_khcp_data"""
        if obj.tk_khcp:
            try:
                return AccountModelSerializer(obj.tk_khcp).data
            except Exception as e:
                # Log the error and continue - this is a read-only method
                print(f"Error getting tk_khcp account: {str(e)}")
        return None

    def get_tk_dt_data(self, obj):
        """Method field for tk_dt_data"""
        if obj.tk_dt:
            try:
                return AccountModelSerializer(obj.tk_dt).data
            except Exception as e:
                print(f"Error getting tk_dt account: {str(e)}")
        return None

    def get_tk_gv_data(self, obj):
        """Method field for tk_gv_data"""
        if obj.tk_gv:
            try:
                return AccountModelSerializer(obj.tk_gv).data
            except Exception as e:
                print(f"Error getting tk_gv account: {str(e)}")
        return None

    def get_tk_ck_data(self, obj):
        """Method field for tk_ck_data"""
        if obj.tk_ck:
            try:
                return AccountModelSerializer(obj.tk_ck).data
            except Exception as e:
                print(f"Error getting tk_ck account: {str(e)}")
        return None

    def get_tk_km_data(self, obj):
        """Method field for tk_km_data"""
        if obj.tk_km:
            try:
                return AccountModelSerializer(obj.tk_km).data
            except Exception as e:
                print(f"Error getting tk_km account: {str(e)}")
        return None

    def get_tk_tl_data(self, obj):
        """Method field for tk_tl_data"""
        if obj.tk_tl:
            try:
                return AccountModelSerializer(obj.tk_tl).data
            except Exception as e:
                print(f"Error getting tk_tl account: {str(e)}")
        return None

    def get_tk_spdd_data(self, obj):
        """Method field for tk_spdd_data"""
        if obj.tk_spdd:
            try:
                return AccountModelSerializer(obj.tk_spdd).data
            except Exception as e:
                print(f"Error getting tk_spdd account: {str(e)}")
        return None

    def get_tk_cpnvl_data(self, obj):
        """Method field for tk_cpnvl_data"""
        if obj.tk_cpnvl:
            try:
                return AccountModelSerializer(obj.tk_cpnvl).data
            except Exception as e:
                print(f"Error getting tk_cpnvl account: {str(e)}")
        return None

    def get_gia_ban_data(self, obj):
        """
        Get sales prices for this material
        """
        try:
            # Get all active prices for this material
            prices = GiaBanModel.objects.filter(
                entity_model=obj.entity_model, ma_vt=obj.ma_vt, status="1"
            ).order_by("-ngay_hl")

            # Use the consolidated GiaBanSerializer with nested=True context
            return GiaBanSerializer(prices, many=True, context={"nested": True}).data

        except Exception as e:
            # Log the error and return empty list - this is a read-only method so we don't raise
            print(f"Error getting price lists: {str(e)}")
            return []

    def validate(self, attrs):
        """
        Custom validation for VatTu data.

        Parameters
        ----------
        attrs: dict
            Dictionary of field values to validate

        Returns
        -------
        dict
            Validated data
        """
        # Process reference fields that might contain UUIDs BEFORE calling super().validate()
        # This ensures the conversion happens before Django's field validation

        # Account fields are now ForeignKey fields, so we don't need to process them specially
        # The standard validation will handle them

        # Now call super().validate() after we've converted UUIDs to account codes
        attrs = super().validate(attrs)

        # Check if this is an update operation (instance exists) or create operation
        is_update = hasattr(self, "instance") and self.instance is not None

        # Required fields validation - only for create operations
        if not is_update:
            required_fields = ["ma_vt", "ten_vt", "dvt"]
            for field in required_fields:
                if not attrs.get(field):
                    raise serializers.ValidationError(
                        {field: _("This field is required.")}
                    )

        # Define mapping of fields to their model classes for validation
        reference_fields = {
            "dvt": DonViTinhModel,
            "nh_vt1": GroupModel,
            "nh_vt2": GroupModel,
            "nh_vt3": GroupModel,
            "ma_thue": TaxModel,
            "ma_thue_nk": TaxModel,
            "ma_kho": KhoHangModel,
            "ma_vi_tri": ViTriModel,
            "nuoc_sx": QuocGiaModel,
            "mau_sac": ColorModel,
            "kich_co": KichCoModel,
            "ma_bp": BoPhanModel,
        }

        # Validate reference fields
        for field, model_class in reference_fields.items():
            if field in attrs and attrs[field]:
                value = attrs[field]
                # Check if the value looks like a UUID (36 characters with hyphens)
                if isinstance(value, str) and len(value) == 36 and "-" in value:
                    try:
                        # Try to find the object by UUID
                        obj = model_class.objects.get(uuid=value)

                        # Special validation for group types
                        if model_class == GroupModel:
                            # Check specific group type based on field name
                            if field == "nh_vt1" and obj.loai_nhom != "VT1":
                                raise serializers.ValidationError(
                                    {
                                        field: _(
                                            "Group for nh_vt1 must have loai_nhom=VT1"
                                        )
                                    }
                                )
                            elif field == "nh_vt2" and obj.loai_nhom != "VT2":
                                raise serializers.ValidationError(
                                    {
                                        field: _(
                                            "Group for nh_vt2 must have loai_nhom=VT2"
                                        )
                                    }
                                )
                            elif field == "nh_vt3" and obj.loai_nhom != "VT3":
                                raise serializers.ValidationError(
                                    {
                                        field: _(
                                            "Group for nh_vt3 must have loai_nhom=VT3"
                                        )
                                    }
                                )

                        # Store the object instance directly
                        attrs[field] = obj
                    except model_class.DoesNotExist:
                        raise serializers.ValidationError(
                            {field: _("Invalid reference ID.")}
                        )
                    except ValueError:
                        # If value is not a valid UUID, keep the original value
                        pass

        # Status validation
        if attrs.get("status") and attrs["status"] not in ["0", "1"]:
            raise serializers.ValidationError(
                {"status": _("Invalid status value. Must be 0 or 1.")}
            )

        # Numeric field validation
        if "gia_ton" in attrs and attrs["gia_ton"] < 0:
            raise serializers.ValidationError(
                {"gia_ton": _("Price cannot be negative.")}
            )

        if all(k in attrs for k in ["sl_min", "sl_max"]):
            if attrs["sl_min"] > attrs["sl_max"]:
                raise serializers.ValidationError(
                    {
                        "sl_min": _(
                            "Minimum quantity cannot be greater than maximum quantity."
                        )
                    }
                )

        return attrs

    def create(self, validated_data):
        """Create new VatTu instance."""
        # Process reference fields that might contain UUIDs

        # Define mapping of fields to their model classes
        reference_fields = {
            "dvt": DonViTinhModel,
            "nh_vt1": GroupModel,
            "nh_vt2": GroupModel,
            "nh_vt3": GroupModel,
            "ma_thue": TaxModel,
            "ma_thue_nk": TaxModel,
            "ma_kho": KhoHangModel,
            "ma_vi_tri": ViTriModel,
            "nuoc_sx": QuocGiaModel,
            "mau_sac": ColorModel,
            "kich_co": KichCoModel,
            "ma_bp": BoPhanModel,
        }

        # Process reference fields
        for field, model_class in reference_fields.items():
            if field in validated_data and validated_data[field]:
                value = validated_data[field]
                # Check if the value looks like a UUID (36 characters with hyphens)
                if isinstance(value, str) and len(value) == 36 and "-" in value:
                    try:
                        # Try to find the object by UUID
                        obj = model_class.objects.get(uuid=value)
                        # Store the object instance directly
                        validated_data[field] = obj
                    except (model_class.DoesNotExist, ValueError):
                        # If object doesn't exist or value is not a valid UUID, keep the original value
                        pass

        # Account fields are now ForeignKey fields, so we don't need to process them specially
        # The standard validation will handle them

        # Extract nested data from direct fields
        quy_doi_dvt_data = validated_data.pop("quy_doi_dvt", [])
        gia_ban_data = validated_data.pop("gia_ban", [])

        # Add entity_model from context
        EntityModel = lazy_loader.get_entity_model()
        entity_slug = self.context["request"].parser_context["kwargs"]["entity_slug"]
        entity_model = EntityModel.objects.get(slug=entity_slug)
        validated_data["entity_model"] = entity_model

        # Create the instance with all the processed data
        instance = super().create(validated_data)

        # Create unit conversions
        created_quy_doi_dvt = []
        for quy_doi_dvt_item in quy_doi_dvt_data:
            # Validate required fields
            if not all(k in quy_doi_dvt_item for k in ["dvt", "heso"]):
                continue

            # Convert dvt from UUID to DonViTinhModel if needed
            dvt_value = quy_doi_dvt_item["dvt"]
            if isinstance(dvt_value, str) and len(dvt_value) == 36 and "-" in dvt_value:
                try:
                    dvt_obj = DonViTinhModel.objects.get(uuid=dvt_value)
                    dvt_value = dvt_obj
                except DonViTinhModel.DoesNotExist:
                    print(f"Error: DonViTinhModel with UUID {dvt_value} not found")
                    continue

            # Create the unit conversion
            quy_doi_dvt_obj = {
                "vat_tu_id": instance,
                "dvt": dvt_value,
                "heso": quy_doi_dvt_item["heso"],
            }

            try:
                conversion = VatTuQuyDoiDVTModel.objects.create(**quy_doi_dvt_obj)
                created_quy_doi_dvt.append(conversion)
            except Exception as e:
                # Raise the exception to properly handle it in the transaction
                raise ValidationError(f"Error creating unit conversion: {str(e)}")

        # Create price lists (DEPRECATED - should use the gia_ban API instead)
        # This code is kept for backward compatibility but will be removed in a future version
        return instance

    def update(self, instance, validated_data):
        """
        Update existing VatTu instance.

        This method processes the quy_doi_dvt field and other fields
        when updating a material.
        """
        # Extract nested data from direct fields
        quy_doi_dvt_data = validated_data.pop("quy_doi_dvt", [])

        # Extract gia_ban data for backward compatibility
        gia_ban_data = validated_data.pop("gia_ban", [])

        # Process reference fields that might contain UUIDs
        reference_fields = {
            "dvt": DonViTinhModel,
            "nh_vt1": GroupModel,
            "nh_vt2": GroupModel,
            "nh_vt3": GroupModel,
            "ma_thue": TaxModel,
            "ma_thue_nk": TaxModel,
            "ma_kho": KhoHangModel,
            "ma_vi_tri": ViTriModel,
            "nuoc_sx": QuocGiaModel,
            "mau_sac": ColorModel,
            "kich_co": KichCoModel,
            "ma_bp": BoPhanModel,
        }

        # Process reference fields
        for field, model_class in reference_fields.items():
            if field in validated_data and validated_data[field]:
                value = validated_data[field]
                # Check if the value looks like a UUID (36 characters with hyphens)
                if isinstance(value, str) and len(value) == 36 and "-" in value:
                    try:
                        # Try to find the object by UUID
                        obj = model_class.objects.get(uuid=value)

                        # Special validation for group types
                        if model_class == GroupModel:
                            # Check specific group type based on field name
                            if field == "nh_vt1" and obj.loai_nhom != "VT1":
                                raise serializers.ValidationError(
                                    {
                                        field: _(
                                            "Group for nh_vt1 must have loai_nhom=VT1"
                                        )
                                    }
                                )
                            elif field == "nh_vt2" and obj.loai_nhom != "VT2":
                                raise serializers.ValidationError(
                                    {
                                        field: _(
                                            "Group for nh_vt2 must have loai_nhom=VT2"
                                        )
                                    }
                                )
                            elif field == "nh_vt3" and obj.loai_nhom != "VT3":
                                raise serializers.ValidationError(
                                    {
                                        field: _(
                                            "Group for nh_vt3 must have loai_nhom=VT3"
                                        )
                                    }
                                )

                        # Store the object instance directly
                        validated_data[field] = obj
                    except model_class.DoesNotExist:
                        raise serializers.ValidationError(
                            {field: _("Invalid reference ID.")}
                        )

        # Update the instance with the validated data
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Create unit conversions if provided
        if quy_doi_dvt_data:
            for quy_doi_dvt_item in quy_doi_dvt_data:
                # Validate required fields
                if not all(k in quy_doi_dvt_item for k in ["dvt", "heso"]):
                    continue

                # Convert dvt from UUID to DonViTinhModel if needed
                dvt_value = quy_doi_dvt_item["dvt"]
                if (
                    isinstance(dvt_value, str)
                    and len(dvt_value) == 36
                    and "-" in dvt_value
                ):
                    try:
                        dvt_obj = DonViTinhModel.objects.get(uuid=dvt_value)
                        dvt_value = dvt_obj
                    except DonViTinhModel.DoesNotExist:
                        print(f"Error: DonViTinhModel with UUID {dvt_value} not found")
                        continue

                # Create the unit conversion
                quy_doi_dvt_obj = {
                    "vat_tu_id": instance,
                    "dvt": dvt_value,
                    "heso": quy_doi_dvt_item["heso"],
                }

                try:
                    # Check if this unit conversion already exists
                    existing = VatTuQuyDoiDVTModel.objects.filter(
                        vat_tu_id=instance, dvt=dvt_value
                    ).first()

                    if existing:
                        # Update existing conversion
                        existing.heso = quy_doi_dvt_item["heso"]
                        existing.save()
                    else:
                        # Create new conversion
                        VatTuQuyDoiDVTModel.objects.create(**quy_doi_dvt_obj)
                except Exception as e:
                    # Log the error but continue
                    print(f"Error creating/updating unit conversion: {str(e)}")

        return instance
