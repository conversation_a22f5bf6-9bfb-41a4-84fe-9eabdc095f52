"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for ChiPhiKhongHopLe model.
"""

from rest_framework import serializers

from django_ledger.api.serializers.base import GlobalModelSerializer
from django_ledger.models.danh_muc import ChiPhiKhongHopLeModel


class ChiPhiKhongHopLeSerializer(GlobalModelSerializer):
    """
    Serializer for ChiPhiKhongHopLeModel.
    """

    class Meta:
        model = ChiPhiKhongHopLeModel
        fields = [
            "uuid",
            "entity_model",
            "ma_cpkhl",
            "ten_cpkhl",
            "ten_cpkhl2",
            "ghi_chu",
            "status",
            "created",
            "updated",
        ]
        read_only_fields = ["uuid", "created", "updated", "entity_model"]
        swagger_schema_fields = {
            "title": "ChiPhiKhongHopLe",
            "description": "<PERSON> ph<PERSON> không hợp lệ model serializer",
        }

   