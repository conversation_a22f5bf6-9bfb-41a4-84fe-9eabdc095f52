"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

SQL Query Serializers
"""

from rest_framework import serializers


class SQLQueryRequestSerializer(serializers.Serializer):
    """
    Serializer for SQL query requests.

    Attributes
    ----------
    query : str
        The SQL query to execute. Only SELECT queries are allowed.
    params : list
        Optional parameters for the SQL query. Used for parameterized queries.
    limit : int
        Optional limit for the number of results to return.
    offset : int
        Optional offset for pagination.
    auto_join : bool
        Automatically fetch all data from joined tables.
    """
    query = serializers.Char<PERSON>ield(required=True, help_text='SQL query to execute (only SELECT queries are allowed)')
    params = serializers.ListField(
        child=serializers.JSONField(),
        required=False,
        default=list,
        help_text='Parameters for the SQL query'
    )
    limit = serializers.IntegerField(required=False, min_value=1, max_value=1000, default=100,
                                    help_text='Limit the number of results (max 1000)')
    offset = serializers.IntegerField(required=False, min_value=0, default=0,
                                     help_text='Offset for pagination')
    auto_join = serializers.BooleanField(required=False, default=True,
                                       help_text='Automatically fetch all data from joined tables')


class SQLQueryResponseSerializer(serializers.Serializer):
    """
    Serializer for SQL query responses.

    Attributes
    ----------
    results : list
        The results of the SQL query.
    columns : list
        The column names from the query result.
    count : int
        The total number of results.
    next : str
        URL for the next page of results (if applicable).
    previous : str
        URL for the previous page of results (if applicable).
    """
    results = serializers.ListField(help_text='Query results as a list of dictionaries')
    columns = serializers.ListField(child=serializers.CharField(), help_text='Column names from the query')
    count = serializers.IntegerField(required=False, help_text='Total number of results')
    next = serializers.URLField(required=False, allow_null=True, help_text='URL for the next page of results')
    previous = serializers.URLField(required=False, allow_null=True, help_text='URL for the previous page of results')
