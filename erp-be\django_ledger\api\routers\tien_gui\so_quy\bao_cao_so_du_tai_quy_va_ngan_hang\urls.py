"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for Bao Cao So Du Tai Quy Va Ngan Hang (Cash and Bank Balance Report) API endpoints.
"""

from django.urls import path
from django_ledger.api.views.tien_gui.so_quy.bao_cao_so_du_tai_quy_va_ngan_hang import BaoCaoSoDuTaiQuyVaNganHangViewSet

# URL patterns - Single endpoint for cash and bank balance report with filters as POST body data
urlpatterns = [
    # Cash and Bank Balance Report endpoint - returns report directly with filter POST body data
    path("", BaoCaoSoDuTaiQuyVaNganHangViewSet.as_view({"post": "get_report"}), name="bao-cao-so-du-tai-quy-va-ngan-hang-report"),
]
