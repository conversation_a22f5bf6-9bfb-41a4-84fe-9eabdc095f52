"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Tai San (Fixed Asset) module.
URL patterns for Tai San (Asset) module.
"""

from django.urls import include, path

# URL patterns for the module
urlpatterns = [
    # Include khai_bao_tang_giam_tscd URLs
    path(
        "khai-bao-tang-giam-tscd/",
        include("django_ledger.api.routers.tai_san.khai_bao_tang_giam_tscd.urls"),
    ),
    # Include dung_khau_hao_tscd URLs
    path(
        "dung-khau-hao-tscd/",
        include("django_ledger.api.routers.tai_san.dung_khau_hao_tscd.urls"),
    ),
    # Include tinh_khau_hao_tscd URLs
    path(
        "tinh-khau-hao-tscd/",
        include("django_ledger.api.routers.tai_san.tinh_khau_hao_tscd.urls"),
    ),
    # Include dieu_chuyen_tscd URLs
    path(
        "dieu-chuyen-tscd/",
        include("django_ledger.api.routers.tai_san.dieu_chuyen_tscd.urls"),
    ),
    # Include kiem_ke_tscd URLs
    path(
        "kiem-ke-tscd/", include("django_ledger.api.routers.tai_san.kiem_ke_tscd.urls")
    ),
    # Include tang_giam_tscd URLs
    path(
        "tang-giam-tscd/",
        include("django_ledger.api.routers.tai_san.tang_giam_tscd.urls"),
    ),
    # Add other tai_san-related URLs here
]
