"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Thanh Toan Tam Ung (Advance Payment Settlement) module.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.mua_hang.thanh_toan_tam_ung.phieu_thanh_toan_tam_ung import (
    PhieuThanhToanTamUngViewSet,
    ChiTietPhieuThanhToanTamUngViewSet,
    ThuePhieuThanhToanTamUngViewSet,
    QuyetToanCacLanTamUngViewSet
)

# Main router for PhieuThanhToanTamUng
router = DefaultRouter()
router.register('phieu-thanh-toan-tam-ung', PhieuThanhToanTamUngViewSet, basename='phieu-thanh-toan-tam-ung')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
    
    # Nested routes for phieu-thanh-toan-tam-ung
    path('phieu-thanh-toan-tam-ung/<uuid:phieu_thanh_toan_uuid>/', include([
        # Chi tiet phieu thanh toan tam ung routes
        path('chi-tiet/', ChiTietPhieuThanhToanTamUngViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-tiet-phieu-thanh-toan-tam-ung-list'),

        path('chi-tiet/<uuid:uuid>/', ChiTietPhieuThanhToanTamUngViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-tiet-phieu-thanh-toan-tam-ung-detail'),
        
        # Thue phieu thanh toan tam ung routes
        path('thue/', ThuePhieuThanhToanTamUngViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='thue-phieu-thanh-toan-tam-ung-list'),

        path('thue/<uuid:uuid>/', ThuePhieuThanhToanTamUngViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='thue-phieu-thanh-toan-tam-ung-detail'),
        
        # Quyet toan cac lan tam ung routes
        path('quyet-toan/', QuyetToanCacLanTamUngViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='quyet-toan-cac-lan-tam-ung-list'),

        path('quyet-toan/<uuid:uuid>/', QuyetToanCacLanTamUngViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='quyet-toan-cac-lan-tam-ung-detail'),
    ])),
]
