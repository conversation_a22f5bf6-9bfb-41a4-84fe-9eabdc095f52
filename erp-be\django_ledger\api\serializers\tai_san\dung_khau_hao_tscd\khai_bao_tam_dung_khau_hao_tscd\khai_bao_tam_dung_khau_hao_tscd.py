"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

KhaiBaoTamDungKhauHaoTSCD (Fixed Asset Depreciation Suspension Declaration) serializer implementation.
"""

from rest_framework import serializers

from django_ledger.models.tai_san.dung_khau_hao_tscd.khai_bao_tam_dung_khau_hao_tscd import KhaiBaoTamDungKhauHaoTSCDModel
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.tai_san.khai_bao_tang_giam_tscd.khai_bao_thong_tin_tai_san_co_dinh.khai_bao_thong_tin_tai_san_co_dinh import KhaiBaoThongTinTaiSanCoDinhSerializer


class KhaiBaoTamDungKhauHaoTSCDSerializer(serializers.ModelSerializer):
    """
    Serializer for the KhaiBaoTamDungKhauHaoTSCDModel.

    This serializer handles the conversion between KhaiBaoTamDungKhauHaoTSCDModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).
    """
    # Add nested serializers for foreign key fields
    ma_ts_data = KhaiBaoThongTinTaiSanCoDinhSerializer(source='ma_ts', read_only=True)

    class Meta:
        model = KhaiBaoTamDungKhauHaoTSCDModel
        fields = [
            'uuid',
            'entity_model',
            'ma_ts',
            'ma_ts_data',
            'ngay_hl_tu',
            'ngay_hl_den',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'ma_ts_data',
            'created',
            'updated'
        ]
