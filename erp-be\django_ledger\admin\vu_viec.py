from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from django_ledger.models import VuViecModel


class VuViecModelAdmin(admin.ModelAdmin):
    """
    Admin class for the VuViecModel (Case) model.
    """
    list_display = ['ma_vu_viec', 'ten_vu_viec', 'trang_thai']
    list_filter = ['trang_thai']
    search_fields = ['ma_vu_viec', 'ten_vu_viec']
    readonly_fields = ['uuid', 'created', 'updated']
