"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for all API routers.
"""

from django.urls import include, path

# URL patterns for all routers
urlpatterns = [
    # Include mua_hang URLs
    path("mua-hang/", include("django_ledger.api.routers.mua_hang.urls")),
    # Include ban_hang URLs
    path("ban-hang/", include("django_ledger.api.routers.ban_hang.urls")),
    # Include danh_muc URLs
    path("danh-muc/", include("django_ledger.api.routers.danh_muc.urls")),
    # Include gia_thanh URLs
    path("gia-thanh/", include("django_ledger.api.routers.gia_thanh.urls")),
    # Include he_thong URLs
    path("he-thong/", include("django_ledger.api.routers.he_thong.urls")),
    # Include ton_kho URLs
    path("ton-kho/", include("django_ledger.api.routers.ton_kho.urls")),
    # Include tai_san URLs
    path("tai-san/", include("django_ledger.api.routers.tai_san.urls")),
    # Include ngan_sach URLs
    path("ngan-sach/", include("django_ledger.api.routers.ngan_sach.urls")),
    # Include cong_cu URLs
    path("cong-cu/", include("django_ledger.api.routers.cong_cu.urls")),
    # Include tien_gui URLs
    path("tien-gui/", include("django_ledger.api.routers.tien_gui.urls")),
    # Include tien_mat URLs
    path("tien-mat/", include("django_ledger.api.routers.tien_mat.urls")),
    # Include tong_hop URLs
    path("tong-hop/", include("django_ledger.api.routers.tong_hop.urls")),
    # Include thue URLs
    path("thue/", include("django_ledger.api.routers.thue.urls")),
    # Add other module URLs here
]
