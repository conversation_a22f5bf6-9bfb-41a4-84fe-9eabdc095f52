from rest_framework import serializers
from django_ledger.models.khu_vuc import KhuVucModel
from django_ledger.models import GroupModel
from django_ledger.api.serializers.group import GroupModelSimpleSerializer


class KhuVucParentSerializer(serializers.ModelSerializer):
    """
    Serializer for parent region information.
    """
    class Meta:
        model = KhuVucModel
        fields = ['uuid', 'rg_code', 'rgname', 'rgname2']


class KhuVucModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the KhuVucModel (Region) model.

    This serializer handles the conversion between KhuVucModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - rg_code: Region code
    - rgname: Primary name of the region
    - rgname2: Secondary/alternative name (optional)
    - parent_rg: Reference to the parent region (self-reference)
    - parent: Nested parent region object (read-only)
    - nh_rg1: UUID reference to region group 1
    - nh_rg1_group: Nested region group 1 object (read-only)
    - stt: Order number
    - status: Status indicator ('0'=inactive, '1'=active)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    """
    parent_rg_data = serializers.SerializerMethodField(read_only=True)
    nh_rg1_group = serializers.SerializerMethodField(read_only=True)
    nh_rg2_group = serializers.SerializerMethodField(read_only=True)
    nh_rg3_group = serializers.SerializerMethodField(read_only=True)

    def get_parent_rg_data(self, obj):
        """
        Get parent region data for nested response
        """
        if obj.parent_rg:
            return KhuVucParentSerializer(obj.parent_rg).data
        return None

    def get_nh_rg1_group(self, obj):
        """
        Get group 1 data for nested response
        """
        if obj.nh_rg1:
            return GroupModelSimpleSerializer(obj.nh_rg1).data
        return None

    def get_nh_rg2_group(self, obj):
        """
        Get group 2 data for nested response
        """
        if obj.nh_rg2:
            return GroupModelSimpleSerializer(obj.nh_rg2).data
        return None

    def get_nh_rg3_group(self, obj):
        """
        Get group 3 data for nested response
        """
        if obj.nh_rg3:
            return GroupModelSimpleSerializer(obj.nh_rg3).data
        return None

    class Meta:
        model = KhuVucModel
        fields = ['uuid', 'entity_model', 'rg_code', 'rgname', 'rgname2', 'parent_rg',
                  'parent_rg_data', 'stt', 'status', 'nh_rg1', 'nh_rg1_group', 'nh_rg2', 'nh_rg2_group',
                  'nh_rg3', 'nh_rg3_group', 'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated', 'parent_rg_data', 'nh_rg1_group', 'nh_rg2_group', 'nh_rg3_group']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "rg_code": "MN",
                "rgname": "Miền Nam",
                "rgname2": "Southern Region",
                "parent_rg": None,
                "parent_rg_data": None,
                "stt": 1.0,
                "status": "1",
                "nh_rg1": "123e4567-e89b-12d3-a456-************",
                "nh_rg1_group": {
                    "uuid": "123e4567-e89b-12d3-a456-************",
                    "ma_nhom": "RG-SOUTH",
                    "ten_phan_nhom": "Southern Region Group",
                    "loai_nhom": "RG1"
                },
                "nh_rg2": "123e4567-e89b-12d3-a456-************",
                "nh_rg2_group": {
                    "uuid": "123e4567-e89b-12d3-a456-************",
                    "ma_nhom": "RG-CENTRAL",
                    "ten_phan_nhom": "Central Region Group",
                    "loai_nhom": "RG2"
                },
                "nh_rg3": "123e4567-e89b-12d3-a456-************",
                "nh_rg3_group": {
                    "uuid": "123e4567-e89b-12d3-a456-************",
                    "ma_nhom": "RG-NORTH",
                    "ten_phan_nhom": "Northern Region Group",
                    "loai_nhom": "RG3"
                },
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z"
            }
        }
