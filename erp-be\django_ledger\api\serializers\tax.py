from rest_framework import serializers
from django.shortcuts import get_object_or_404

from django_ledger.models import TaxModel, GroupModel, AccountModel
from django_ledger.api.serializers.group import GroupModelSerializer
from django_ledger.api.serializers.account import AccountModelSerializer

class TaxModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the TaxModel (Tax) model.

    This serializer handles the conversion between TaxModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields (nhom_thue, tk_thue_dau_ra, etc.)
    - Adds additional fields with "_data" suffix (nhom_thue_data, tk_thue_dau_ra_data, etc.)
      that contain the complete nested object data with all available fields
    - When deserializing, accepts UUIDs for reference fields
    """
    # Define additional fields for nested data
    nhom_thue_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_dau_ra_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_dau_ra_duoc_gia_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_dau_vao_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_dau_vao_duoc_gia_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = TaxModel
        fields = ['uuid', 'ma_thue', 'ten_thue', 'ten_thue2', 'thue_suat',
                  'thue_suat_hddt', 'nhom_thue', 'nhom_thue_data',
                  'tk_thue_dau_ra', 'tk_thue_dau_ra_data',
                  'tk_thue_dau_ra_duoc_gia', 'tk_thue_dau_ra_duoc_gia_data',
                  'tk_thue_dau_vao', 'tk_thue_dau_vao_data',
                  'tk_thue_dau_vao_duoc_gia', 'tk_thue_dau_vao_duoc_gia_data',
                  'stt', 'loai_thue', 'status',
                  'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated',
                           'nhom_thue_data', 'tk_thue_dau_ra_data',
                           'tk_thue_dau_ra_duoc_gia_data', 'tk_thue_dau_vao_data',
                           'tk_thue_dau_vao_duoc_gia_data']

    def get_nhom_thue_data(self, obj):
        """Method field for nhom_thue_data"""
        if obj.nhom_thue:
            return GroupModelSerializer(obj.nhom_thue).data
        return None

    def get_tk_thue_dau_ra_data(self, obj):
        """Method field for tk_thue_dau_ra_data"""
        if obj.tk_thue_dau_ra:
            return AccountModelSerializer(obj.tk_thue_dau_ra).data
        return None

    def get_tk_thue_dau_ra_duoc_gia_data(self, obj):
        """Method field for tk_thue_dau_ra_duoc_gia_data"""
        if obj.tk_thue_dau_ra_duoc_gia:
            return AccountModelSerializer(obj.tk_thue_dau_ra_duoc_gia).data
        return None

    def get_tk_thue_dau_vao_data(self, obj):
        """Method field for tk_thue_dau_vao_data"""
        if obj.tk_thue_dau_vao:
            return AccountModelSerializer(obj.tk_thue_dau_vao).data
        return None

    def get_tk_thue_dau_vao_duoc_gia_data(self, obj):
        """Method field for tk_thue_dau_vao_duoc_gia_data"""
        if obj.tk_thue_dau_vao_duoc_gia:
            return AccountModelSerializer(obj.tk_thue_dau_vao_duoc_gia).data
        return None

    def to_internal_value(self, data):
        """
        Override to_internal_value to convert UUID strings to model instances
        """
        # Create a copy of the data to avoid modifying the original
        data_copy = data.copy()

        # Handle nhom_thue (tax group) field
        
        # Call the parent's to_internal_value with our processed data
        return super().to_internal_value(data_copy)

    def create(self, validated_data):
        """
        Create a new TaxModel instance
        """
        # Create the instance
        return TaxModel.objects.create(**validated_data)

    def update(self, instance, validated_data):
        """
        Update an existing TaxModel instance
        """
        # Update the instance fields
        for key, value in validated_data.items():
            setattr(instance, key, value)

        instance.save()
        return instance
