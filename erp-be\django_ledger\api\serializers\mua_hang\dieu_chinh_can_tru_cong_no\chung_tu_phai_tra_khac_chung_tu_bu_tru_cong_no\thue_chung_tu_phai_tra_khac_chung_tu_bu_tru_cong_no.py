"""
Serializer for ThueChungTuPhaiTraKhacChungTuBuTruCongNoModel.
"""

from django.utils.translation import gettext_lazy as _
from django_ledger.models.mua_hang.dieu_chinh_can_tru_cong_no import (
    ThueChungTuPhaiTraKhacChungTuBuTruCongNoModel,
)
from rest_framework import serializers


class ThueChungTuPhaiTraKhacChungTuBuTruCongNoSerializer(serializers.ModelSerializer):
    """
    Serializer for ThueChungTuPhaiTraKhacChungTuBuTruCongNoModel.
    """

    # Read-only fields for related objects
    giao_dich_data = serializers.SerializerMethodField(read_only=True)
    so_ct0_data = serializers.SerializerMethodField(read_only=True)
    so_ct2_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_no_data = serializers.SerializerMethodField(read_only=True)
    tk_du_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    ma_tt_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_dtt_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_cp0_data = serializers.SerializerMethodField(read_only=True)
    ma_kh9_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ThueChungTuPhaiTraKhacChungTuBuTruCongNoModel
        fields = [
            "uuid",
            "giao_dich",
            "giao_dich_data",
            "line",
            "so_ct0",
            "so_ct0_data",
            "so_ct2",
            "so_ct2_data",
            "ngay_ct0",
            "ma_thue",
            "thue_suat",
            "ma_mau_ct",
            "ma_mau_bc",
            "ma_tc_thue",
            "ma_kh",
            "ma_kh_data",
            "ten_kh_thue",
            "dia_chi",
            "ma_so_thue",
            "ten_vt_thue",
            "t_tien_nt",
            "t_tien",
            "tk_thue_no",
            "tk_thue_no_data",
            "ten_tk_thue_no",
            "tk_du",
            "tk_du_data",
            "ten_tk_du",
            "t_thue_nt",
            "t_thue",
            "ma_kh9",
            "ma_kh9_data",
            "ten_kh9",
            "ma_tt",
            "ma_tt_data",
            "ten_tt",
            "ghi_chu",
            "id_tt",
            "ma_bp",
            "ma_bp_data",
            "ma_vv",
            "ma_vv_data",
            "ma_hd",
            "ma_hd_data",
            "ma_dtt",
            "ma_dtt_data",
            "ma_ku",
            "ma_ku_data",
            "ma_phi",
            "ma_phi_data",
            "ma_sp",
            "ma_sp_data",
            "ma_lsx",
            "ma_cp0",
            "ma_cp0_data",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "giao_dich",
            "giao_dich_data",
            "so_ct0_data",
            "so_ct2_data",
            "tk_thue_no_data",
            "tk_du_data",
            "ma_kh_data",
            "ma_tt_data",
            "ma_bp_data",
            "ma_vv_data",
            "ma_hd_data",
            "ma_dtt_data",
            "ma_ku_data",
            "ma_phi_data",
            "ma_sp_data",
            "ma_cp0_data",
            "ma_kh9_data",
            "created",
            "updated",
        ]

    def get_giao_dich_data(self, obj):
        """
        Get parent transaction data.
        """
        if obj.giao_dich:
            return {
                "uuid": str(obj.giao_dich.uuid),
                "so_ct": obj.giao_dich.so_ct,
                "dien_giai": obj.giao_dich.dien_giai,
            }
        return None

    def get_so_ct0_data(self, obj):
        """
        Get reference document data.
        """
        if obj.so_ct0:
            return {
                "uuid": str(obj.so_ct0.uuid),
                "so_ct": obj.so_ct0.so_ct if hasattr(obj.so_ct0, "so_ct") else None,
                "dien_giai": (
                    obj.so_ct0.dien_giai if hasattr(obj.so_ct0, "dien_giai") else None
                ),
            }
        return None

    def get_so_ct2_data(self, obj):
        """
        Get tax document data.
        """
        if obj.so_ct2:
            return {
                "uuid": str(obj.so_ct2.uuid),
                "so_ct": obj.so_ct2.so_ct if hasattr(obj.so_ct2, "so_ct") else None,
                "dien_giai": (
                    obj.so_ct2.dien_giai if hasattr(obj.so_ct2, "dien_giai") else None
                ),
            }
        return None

    def get_tk_thue_no_data(self, obj):
        """
        Get tax debit account data.
        """
        if obj.tk_thue_no:
            return {
                "uuid": str(obj.tk_thue_no.uuid),
                "code": (
                    obj.tk_thue_no.code if hasattr(obj.tk_thue_no, "code") else None
                ),
                "name": (
                    obj.tk_thue_no.name if hasattr(obj.tk_thue_no, "name") else None
                ),
            }
        return None

    def get_tk_du_data(self, obj):
        """
        Get offsetting account data.
        """
        if obj.tk_du:
            return {
                "uuid": str(obj.tk_du.uuid),
                "code": obj.tk_du.code if hasattr(obj.tk_du, "code") else None,
                "name": obj.tk_du.name if hasattr(obj.tk_du, "name") else None,
            }
        return None

    def get_ma_kh_data(self, obj):
        """
        Get customer data.
        """
        if obj.ma_kh:
            return {
                "uuid": str(obj.ma_kh.uuid),
                "ma_kh": obj.ma_kh.ma_kh if hasattr(obj.ma_kh, "ma_kh") else None,
                "ten_kh": obj.ma_kh.ten_kh if hasattr(obj.ma_kh, "ten_kh") else None,
            }
        return None

    def get_ma_tt_data(self, obj):
        """
        Get payment term data.
        """
        if obj.ma_tt:
            return {
                "uuid": str(obj.ma_tt.uuid),
                "ma_tt": obj.ma_tt.ma_tt if hasattr(obj.ma_tt, "ma_tt") else None,
                "ten_tt": obj.ma_tt.ten_tt if hasattr(obj.ma_tt, "ten_tt") else None,
            }
        return None

    def get_ma_bp_data(self, obj):
        """
        Get department data.
        """
        if obj.ma_bp:
            return {
                "uuid": str(obj.ma_bp.uuid),
                "ma_bp": obj.ma_bp.ma_bp if hasattr(obj.ma_bp, "ma_bp") else None,
                "ten_bp": obj.ma_bp.ten_bp if hasattr(obj.ma_bp, "ten_bp") else None,
            }
        return None

    def get_ma_vv_data(self, obj):
        """
        Get task data.
        """
        if obj.ma_vv:
            return {
                "uuid": str(obj.ma_vv.uuid),
                "ma_vv": obj.ma_vv.ma_vv if hasattr(obj.ma_vv, "ma_vv") else None,
                "ten_vv": obj.ma_vv.ten_vv if hasattr(obj.ma_vv, "ten_vv") else None,
            }
        return None

    def get_ma_hd_data(self, obj):
        """
        Get contract data.
        """
        if obj.ma_hd:
            return {
                "uuid": str(obj.ma_hd.uuid),
                "ma_hd": obj.ma_hd.ma_hd if hasattr(obj.ma_hd, "ma_hd") else None,
                "ten_hd": obj.ma_hd.ten_hd if hasattr(obj.ma_hd, "ten_hd") else None,
            }
        return None

    def get_ma_dtt_data(self, obj):
        """
        Get payment batch data.
        """
        if obj.ma_dtt:
            return {
                "uuid": str(obj.ma_dtt.uuid),
                "ma_dtt": obj.ma_dtt.ma_dtt if hasattr(obj.ma_dtt, "ma_dtt") else None,
                "ten_dtt": (
                    obj.ma_dtt.ten_dtt if hasattr(obj.ma_dtt, "ten_dtt") else None
                ),
            }
        return None

    def get_ma_ku_data(self, obj):
        """
        Get agreement data.
        """
        if obj.ma_ku:
            return {
                "uuid": str(obj.ma_ku.uuid),
                "ma_ku": obj.ma_ku.ma_ku if hasattr(obj.ma_ku, "ma_ku") else None,
                "ten_ku": obj.ma_ku.ten_ku if hasattr(obj.ma_ku, "ten_ku") else None,
            }
        return None

    def get_ma_phi_data(self, obj):
        """
        Get fee data.
        """
        if obj.ma_phi:
            return {
                "uuid": str(obj.ma_phi.uuid),
                "ma_phi": obj.ma_phi.ma_phi if hasattr(obj.ma_phi, "ma_phi") else None,
                "ten_phi": (
                    obj.ma_phi.ten_phi if hasattr(obj.ma_phi, "ten_phi") else None
                ),
            }
        return None

    def get_ma_sp_data(self, obj):
        """
        Get product data.
        """
        if obj.ma_sp:
            return {
                "uuid": str(obj.ma_sp.uuid),
                "ma_vt": obj.ma_sp.ma_vt if hasattr(obj.ma_sp, "ma_vt") else None,
                "ten_vt": obj.ma_sp.ten_vt if hasattr(obj.ma_sp, "ten_vt") else None,
            }
        return None

    def get_ma_kh9_data(self, obj):
        """
        Get customer 9 data.
        """
        if obj.ma_kh9:
            return {
                "uuid": str(obj.ma_kh9.uuid),
                "ma_kh": obj.ma_kh9.ma_kh if hasattr(obj.ma_kh9, "ma_kh") else None,
                "ten_kh": obj.ma_kh9.ten_kh if hasattr(obj.ma_kh9, "ten_kh") else None,
            }
        return None

    def get_ma_cp0_data(self, obj):
        """
        Get invalid cost data.
        """
        if obj.ma_cp0:
            return {
                "uuid": str(obj.ma_cp0.uuid),
                "ma_cp0": obj.ma_cp0.ma_cp0 if hasattr(obj.ma_cp0, "ma_cp0") else None,
                "ten_cp0": (
                    obj.ma_cp0.ten_cp0 if hasattr(obj.ma_cp0, "ten_cp0") else None
                ),
            }
        return None

    def validate(self, attrs):
        """
        Validate the serializer data.
        """
        # Check if this is an update or create operation
        is_update = self.instance is not None

        # Validate required fields for create operations
        if not is_update:
            required_fields = ["line", "ma_thue"]
            for field in required_fields:
                if field not in attrs:
                    raise serializers.ValidationError(
                        {field: _("This field is required.")}
                    )

        # Set default values if not provided
        if "t_tien" not in attrs:
            attrs["t_tien"] = 0

        if "t_tien_nt" not in attrs:
            attrs["t_tien_nt"] = 0

        if "t_thue" not in attrs:
            attrs["t_thue"] = 0

        if "t_thue_nt" not in attrs:
            attrs["t_thue_nt"] = 0

        if "thue_suat" not in attrs:
            attrs["thue_suat"] = 0

        if "id_tt" not in attrs:
            attrs["id_tt"] = 0

        # Validate tax rate
        thue_suat = attrs.get("thue_suat", 0)
        if thue_suat < 0 or thue_suat > 100:
            raise serializers.ValidationError(
                {"thue_suat": _("Tax rate must be between 0 and 100")}
            )

        return attrs
