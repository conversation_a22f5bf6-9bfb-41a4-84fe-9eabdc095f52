"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for NgoaiTe model.
"""
from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models import NgoaiTeModel, AccountModel
from django_ledger.api.serializers.accounts import AccountModelSerializer


class NgoaiTeSerializer(serializers.ModelSerializer):
    """
    Serializer for NgoaiTe model.
    """
    # Read-only fields for related objects
    tk_pscl_no_data = serializers.SerializerMethodField(read_only=True)
    tk_pscl_co_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = NgoaiTeModel
        fields = [
            'uuid',
            'entity_model',
            'ma_nt',
            'ten_nt',
            'ten_nt2',
            'tk_pscl_no',
            'tk_pscl_no_data',
            'tk_pscl_co',
            'tk_pscl_co_data',
            'stt',
            'ra_ndec',
            'ra_1',
            'ra_2',
            'ra_3',
            'ra_4',
            'ra_5',
            'ra_12',
            'ra_22',
            'ra_32',
            'ra_42',
            'ra_52',
            'cach_doc',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'tk_pscl_no_data',
            'tk_pscl_co_data',
            'created',
            'updated'
        ]

    def get_tk_pscl_no_data(self, obj):
        """
        Get debit exchange difference account data.
        """
        if obj.tk_pscl_no:
            return AccountModelSerializer(obj.tk_pscl_no).data
        return None

    def get_tk_pscl_co_data(self, obj):
        """
        Get credit exchange difference account data.
        """
        if obj.tk_pscl_co:
            return AccountModelSerializer(obj.tk_pscl_co).data
        return None

    def validate_ma_nt(self, value):
        """
        Validate currency code.
        """
        if not value:
            raise serializers.ValidationError(_('Currency code is required.'))
        
        if len(value) > 10:
            raise serializers.ValidationError(_('Currency code cannot exceed 10 characters.'))
        
        return value.upper()

    def validate_ten_nt(self, value):
        """
        Validate currency name.
        """
        if not value:
            raise serializers.ValidationError(_('Currency name is required.'))
        
        if len(value) > 50:
            raise serializers.ValidationError(_('Currency name cannot exceed 50 characters.'))
        
        return value

    def validate_stt(self, value):
        """
        Validate sort order.
        """
        if value is None:
            raise serializers.ValidationError(_('Sort order is required.'))
        
        if value < 0:
            raise serializers.ValidationError(_('Sort order must be a positive number.'))
        
        return value

    def validate_ra_ndec(self, value):
        """
        Validate decimal places.
        """
        if value is None:
            raise serializers.ValidationError(_('Decimal places is required.'))
        
        if value < 0 or value > 10:
            raise serializers.ValidationError(_('Decimal places must be between 0 and 10.'))
        
        return value

   