"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Bao Cao Luu Chuyen Tien Gian Tiep (Indirect Cash Flow Statement Report).
"""

from django.urls import path
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.tong_hop.bao_cao_tai_chinh.bao_cao_luu_chuyen_tien_gian_tiep import BaoCaoLuuChuyenTienGianTiepViewSet

# Create router and register viewsets
router = DefaultRouter()

# URL patterns for the module
urlpatterns = [
    # POST endpoint for generating cash flow report
    path('', BaoCaoLuuChuyenTienGianTiepViewSet.as_view({'post': 'get_report'}), name='bao-cao-luu-chuyen-tien-gian-tiep'),
    
    # GET endpoint for API documentation (returns method not allowed with example)
    path('', BaoCaoLuuChuyenTienGianTiepViewSet.as_view({'get': 'list'}), name='bao-cao-luu-chuyen-tien-gian-tiep-info'),
]

# Add router URLs
urlpatterns += router.urls
