"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Admin registration for ViTriKhoHang (Warehouse Location) model.
"""

from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from django_ledger.models.vi_tri_kho_hang import ViTriKhoHangModel


class ViTriKhoHangModelAdmin(admin.ModelAdmin):
    """
    Admin class for the ViTriKhoHangModel.
    """
    list_display = [
        'ma_vi_tri',
        'ten_vi_tri',
        'ma_kho',
        'status',
        'created',
        'updated'
    ]
    list_filter = ['status', 'ma_kho', 'entity_model']
    search_fields = ['ma_vi_tri', 'ten_vi_tri', 'ten_vi_tri2', 'ghi_chu']
    readonly_fields = ['uuid', 'created', 'updated']
    fieldsets = [
        (_('Basic Information'), {
            'fields': [
                'uuid',
                'entity_model',
                'ma_kho',
                'ma_vi_tri',
                'ten_vi_tri',
                'ten_vi_tri2',
                'ghi_chu',
                'status',
            ]
        }),
        (_('Metadata'), {
            'fields': [
                'created',
                'updated',
            ]
        }),
    ]
