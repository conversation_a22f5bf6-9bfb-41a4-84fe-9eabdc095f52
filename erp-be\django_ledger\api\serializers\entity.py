"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.
"""

from rest_framework import serializers
from django_ledger.models import EntityModel

class EntityModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the EntityModel
    """
    admin_email = serializers.EmailField(source='admin.email', read_only=True)
    default_coa_name = serializers.CharField(source='default_coa.name', read_only=True)

    update_fields = [
        'name',
        'accrual_method',
        'fy_start_month',
        'picture'
    ]

    def validate(self, data):
        # Only check entity count during creation
        if self.instance is None:  # Creation
            user = self.context['request'].user
            if EntityModel.objects.for_user(user_model=user).exists():
                raise serializers.ValidationError('Users are limited to creating one entity only.')
        return data

    class Meta:
        model = EntityModel
        fields = [
            'uuid',
            'name',
            'slug',
            'admin_email',
            'default_coa_name',
            'default_coa_slug',
            'default_coa',
            'accrual_method',
            'fy_start_month',
            'picture',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated', 'admin', 'slug']

    def create(self, validated_data):
        """
        Create method is overridden but not used directly.
        The actual entity creation happens in EntityViewSet.perform_create
        which uses EntityModel.create_entity() to properly initialize tree fields.
        """
        # This method should not be called directly when creating entities
        # It's here for completeness, but the actual creation is handled in the viewset
        return super().create(validated_data)

    def update(self, instance, validated_data):
        # Only allow updates to specified fields
        for field in self.update_fields:
            if field in validated_data:
                setattr(instance, field, validated_data[field])
        instance.save()
        return instance
