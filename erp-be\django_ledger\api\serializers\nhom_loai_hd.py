"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for NhomLoaiHD (Contract Type Group) model
"""

from rest_framework import serializers

from django_ledger.models import NhomLoaiHDModel


class NhomLoaiHDModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the NhomLoaiHDModel
    """

    class Meta:
        model = NhomLoaiHDModel
        fields = [
            'uuid',
            'entity_model',
            'ma_nhom',
            'ten_nhom',
            'ten_nhom2',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated', 'entity_model']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "entity_model": "31c8d823-0dca-4843-8058-57589e3fbd7f",
                "ma_nhom": "HDMB",
                "ten_nhom": "<PERSON>ợ<PERSON> đồng mua bán",
                "ten_nhom2": "Sales Contract",
                "status": "1",
                "created": "2023-01-01T00:00:00Z",
                "updated": "2023-01-02T00:00:00Z"
            }
        }

    def create(self, validated_data):
        # Get the entity_slug from the context
        entity_slug = self.context['view'].kwargs['entity_slug']

        # Get the EntityModel instance
        from django_ledger.models.entity import EntityModel
        entity_model = EntityModel.objects.for_user(
            user_model=self.context['request'].user
        ).get(slug__exact=entity_slug)

        # Set the entity_model in the validated_data
        validated_data['entity_model'] = entity_model

        # Create the NhomLoaiHDModel instance
        return super().create(validated_data)
