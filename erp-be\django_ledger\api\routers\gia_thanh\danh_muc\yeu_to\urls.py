"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for YeuTo API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.gia_thanh.danh_muc.yeu_to import (
    YeuToModelViewSet,
    YeuToChiTietModelViewSet
)

# Main router for YeuTo
router = DefaultRouter()
router.register('', YeuToModelViewSet, basename='yeu-to')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),

    # Nested routes for yeu-to
    path('<uuid:yeu_to_uuid>/', include([
        # Chi tiet yeu to routes
        path('chi-tiet/', YeuToChiTietModelViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-tiet-yeu-to-list'),

        path('chi-tiet/<uuid:uuid>/', YeuToChiTietModelViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-tiet-yeu-to-detail'),
    ])),
]
