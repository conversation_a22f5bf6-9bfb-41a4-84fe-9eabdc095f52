"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for DotThanhToan model.
"""
from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models.danh_muc import DotThanhToanModel


class DotThanhToanSerializer(serializers.ModelSerializer):
    """
    Serializer for DotThanhToan model.
    """

    class Meta:
        model = DotThanhToanModel
        fields = [
            'uuid',
            'entity_model',
            'ma_dtt',
            'ten_dtt',
            'ten_dtt2',
            'stt',
            'status',
            'created_by',
            'updated_by',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'created_by',
            'updated_by',
            'created',
            'updated'
        ]

    def validate_ma_dtt(self, value):
        """
        Validate ma_dtt field.
        """
        if not value or len(value.strip()) == 0:
            raise serializers.ValidationError(_('<PERSON><PERSON> đợt thanh toán không được để trống.'))
        return value.strip()

    def validate_ten_dtt(self, value):
        """
        Validate ten_dtt field.
        """
        if not value or len(value.strip()) == 0:
            raise serializers.ValidationError(_('Tên đợt thanh toán không được để trống.'))
        return value.strip()

    def validate_status(self, value):
        """
        Validate status field.
        """
        if value not in ['0', '1']:
            raise serializers.ValidationError(_('Trạng thái phải là "0" hoặc "1".'))
        return value

    def validate_stt(self, value):
        """
        Validate stt field.
        """
        if value is None:
            return 0
        if not isinstance(value, int) or value < 0:
            raise serializers.ValidationError(_('Số thứ tự phải là số nguyên không âm.'))
        return value

    def validate(self, attrs):
        """
        Validate the entire object.
        """
        # Set default values
        if 'status' not in attrs:
            attrs['status'] = '1'
        
        if 'stt' not in attrs:
            attrs['stt'] = 0
            
        # Set ten_dtt2 equal to ten_dtt if not provided
        if not attrs.get('ten_dtt2') and attrs.get('ten_dtt'):
            attrs['ten_dtt2'] = attrs['ten_dtt']

        return attrs
