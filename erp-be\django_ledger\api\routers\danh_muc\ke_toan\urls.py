"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the URL patterns for the Ke Toan (Accounting) API.
"""

from django.urls import path, include

urlpatterns = [
    # <PERSON> <PERSON>hong Hop Le (Invalid Expense) URLs
    path('chi-phi-khong-hop-le/', include('django_ledger.api.routers.danh_muc.ke_toan.chi_phi_khong_hop_le.urls')),

    # Include ngoai_te URLs
    path('ngoai-te/', include('django_ledger.api.routers.danh_muc.ke_toan.ngoai_te.urls')),

    # Phi (Fee) endpoints
    path('phi/', include('django_ledger.api.routers.danh_muc.ke_toan.phi.urls')),
]