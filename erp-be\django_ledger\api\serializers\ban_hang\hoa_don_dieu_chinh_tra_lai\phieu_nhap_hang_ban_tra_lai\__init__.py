"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuNhapHangBanTraLai (Customer Return Receipt) serializer package initialization.
"""

from django_ledger.api.serializers.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai.phieu_nhap_hang_ban_tra_lai import (
    PhieuNhapHangBanTraLaiModelSerializer,
    PhieuNhapHangBanTraLaiModelCreateUpdateSerializer
)

from django_ledger.api.serializers.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai.chi_tiet_phieu_nhap_hang_ban_tra_lai import (
    ChiTietPhieuNhapHangBanTraLaiModelSerializer,
    ChiTietPhieuNhapHangBanTraLaiModelCreateUpdateSerializer
)

__all__ = [
    'PhieuNhapHangBanTraLaiModelSerializer',
    'PhieuNhapHangBanTraLaiModelCreateUpdateSerializer',
    'ChiTietPhieuNhapHangBanTraLaiModelSerializer',
    'ChiTietPhieuNhapHangBanTraLaiModelCreateUpdateSerializer',
]
