"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for KheUoc (Loan/Contract) model.
"""

from rest_framework import serializers

from django_ledger.models.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import KheUocModel
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.erp import CustomerModelSerializer
from django_ledger.services.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import KheUocService
from django_ledger.services.danh_muc.hop_dong_khe_uoc.khe_uoc.chi_tiet_khe_uoc_lai_suat import ChiTietKheUocLaiSuatService
from django_ledger.services.danh_muc.hop_dong_khe_uoc.khe_uoc.chi_tiet_khe_uoc_thanh_toan import ChiTietKheUocThanhToanService


class ChiTietKheUocLaiSuatNestedSerializer(serializers.Serializer):
    """
    Serializer for nested ChiTietKheUocLaiSuat data
    """
    line = serializers.IntegerField()
    ngay_hl = serializers.DateTimeField()
    ls = serializers.DecimalField(max_digits=10, decimal_places=2)
    ghi_chu = serializers.CharField(required=False, allow_null=True, allow_blank=True)


class ChiTietKheUocThanhToanNestedSerializer(serializers.Serializer):
    """
    Serializer for nested ChiTietKheUocThanhToan data
    """
    line = serializers.IntegerField()
    ngay_tt = serializers.DateField()
    tt = serializers.DecimalField(max_digits=16, decimal_places=2)
    tt_nt = serializers.DecimalField(max_digits=16, decimal_places=2, required=False, allow_null=True)
    ghi_chu = serializers.CharField(required=False, allow_null=True, allow_blank=True)


class KheUocModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the KheUocModel
    """
    entity_model = serializers.PrimaryKeyRelatedField(read_only=True)
    entity_model_data = EntityModelSerializer(source='entity_model', read_only=True)

    # Foreign key data fields
    ma_hd_data = serializers.SerializerMethodField()
    ma_bp_data = serializers.SerializerMethodField()
    ma_kh_data = serializers.SerializerMethodField()
    tk_data = serializers.SerializerMethodField()

    # Nested serializers for subsidiary tables
    chi_tiet_lai_suat = ChiTietKheUocLaiSuatNestedSerializer(many=True, required=False)
    chi_tiet_thanh_toan = ChiTietKheUocThanhToanNestedSerializer(many=True, required=False)

    class Meta:
        model = KheUocModel
        fields = [
            'uuid', 'entity_model', 'entity_model_data',
            'ma_ku', 'ten_ku', 'ten_ku2', 'loai_ku',
            'ngay_ku', 'ngay_ku1', 'ngay_ku2',
            'tien_nt', 'tien',
            'ma_hd', 'ma_hd_data',
            'ma_bp', 'ma_bp_data',
            'ma_kh', 'ma_kh_data',
            'tk', 'tk_data',
            'tinh_trang', 'status',
            'chi_tiet_lai_suat', 'chi_tiet_thanh_toan',
            'created', 'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated', 'entity_model']

    def get_ma_hd_data(self, obj):
        """
        Get contract data
        """
        if obj.ma_hd:
            return {
                'uuid': obj.ma_hd.uuid,
                'ma_hd': obj.ma_hd.ma_hd,
                'ten_hd': obj.ma_hd.ten_hd
            }
        return None

    def get_ma_bp_data(self, obj):
        """
        Get department data
        """
        if obj.ma_bp:
            return {
                'uuid': obj.ma_bp.uuid,
                'ma_bp': obj.ma_bp.ma_bp,
                'ten_bp': obj.ma_bp.ten_bp
            }
        return None

    def get_ma_kh_data(self, obj):
        """
        Get customer data - returns the complete customer object using CustomerModelSerializer
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_tk_data(self, obj):
        """
        Get account data
        """
        if obj.tk:
            return {
                'uuid': obj.tk.uuid,
                'code': obj.tk.code,
                'name': obj.tk.name
            }
        return None

    def create(self, validated_data):
        """
        Create a new loan with subsidiary details
        """
        entity_slug = self.context.get('entity_slug')
        user_model = self.context.get('request').user

        # Extract subsidiary data
        chi_tiet_lai_suat_data = validated_data.pop('chi_tiet_lai_suat', [])
        chi_tiet_thanh_toan_data = validated_data.pop('chi_tiet_thanh_toan', [])

        # Create the main record
        service = KheUocService(
            entity_slug=entity_slug,
            user_model=user_model
        )

        instance = service.create(validated_data)

        # Create subsidiary records if provided
        if chi_tiet_lai_suat_data:
            lai_suat_service = ChiTietKheUocLaiSuatService(
                entity_slug=entity_slug,
                user_model=user_model
            )

            for item in chi_tiet_lai_suat_data:
                # Map fields from nested serializer to model fields
                data = {
                    'ma_ku': instance,
                    'line': item['line'],
                    'ls': item['ls'],
                    'ngay_hl': item['ngay_hl'],
                    'ghi_chu': item.get('ghi_chu')
                }
                lai_suat_service.create(data)

        if chi_tiet_thanh_toan_data:
            thanh_toan_service = ChiTietKheUocThanhToanService(
                entity_slug=entity_slug,
                user_model=user_model
            )

            for item in chi_tiet_thanh_toan_data:
                # Map fields from nested serializer to model fields
                data = {
                    'ma_ku': instance,
                    'line': item['line'],
                    'ngay_tt': item['ngay_tt'],
                    'tt': item['tt'],
                    'tt_nt': item.get('tt_nt'),
                    'ghi_chu': item.get('ghi_chu')
                }
                thanh_toan_service.create(data)

        return instance

    def update(self, instance, validated_data):
        """
        Update an existing loan with subsidiary details
        """
        entity_slug = self.context.get('entity_slug')
        user_model = self.context.get('request').user

        # Extract subsidiary data
        chi_tiet_lai_suat_data = validated_data.pop('chi_tiet_lai_suat', [])
        chi_tiet_thanh_toan_data = validated_data.pop('chi_tiet_thanh_toan', [])

        # Update the main record
        service = KheUocService(
            entity_slug=entity_slug,
            user_model=user_model
        )

        instance = service.update(instance.uuid, validated_data)

        # Handle subsidiary records if provided
        if chi_tiet_lai_suat_data:
            lai_suat_service = ChiTietKheUocLaiSuatService(
                entity_slug=entity_slug,
                user_model=user_model
            )

            # Delete existing records
            existing_records = lai_suat_service.get_for_loan(instance.uuid)
            for record in existing_records:
                lai_suat_service.delete(record.uuid)

            # Create new records
            for item in chi_tiet_lai_suat_data:
                item['ma_ku'] = instance
                lai_suat_service.create(item)

        if chi_tiet_thanh_toan_data:
            thanh_toan_service = ChiTietKheUocThanhToanService(
                entity_slug=entity_slug,
                user_model=user_model
            )

            # Delete existing records
            existing_records = thanh_toan_service.get_for_loan(instance.uuid)
            for record in existing_records:
                thanh_toan_service.delete(record.uuid)

            # Create new records
            for item in chi_tiet_thanh_toan_data:
                item['ma_ku'] = instance
                thanh_toan_service.create(item)

        return instance
