"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Invoice Serializers Module
"""
from rest_framework import serializers
from django.core.exceptions import ValidationError

from django_ledger.models import InvoiceModel, ItemTransactionModel
from django_ledger.services.provider import service_provider

class ItemTransactionSerializer(serializers.ModelSerializer):
    """Serializer for invoice items."""
    
    class Meta:
        model = ItemTransactionModel
        fields = [
            'item_model',
            'quantity',
            'unit_cost',
            'entity_unit'
        ]

class InvoiceModelSerializer(serializers.ModelSerializer):
    """Serializer for invoices."""
    
    items = ItemTransactionSerializer(many=True, required=False)
    amount_due = serializers.DecimalField(
        max_digits=20,
        decimal_places=2,
        read_only=True
    )
    
    class Meta:
        model = InvoiceModel
        fields = [
            'uuid',
            'customer',
            'terms',
            'cash_account',
            'prepaid_account',
            'unearned_account',
            'date_draft',
            'invoice_number',
            'invoice_status',
            'date_paid',
            'amount_due',
            'markdown_notes',
            'items'
        ]
        read_only_fields = [
            'uuid',
            'invoice_number',
            'date_paid',
            'amount_due'
        ]

    def create(self, validated_data):
        """Create invoice using service."""
        items_data = validated_data.pop('items', [])
        entity_slug = self.context['entity_slug']
        
        # Get service factory
        factory = service_provider.get_factory(entity_slug)
        
        # Create invoice
        invoice = factory.invoice.create_invoice(
            customer=validated_data['customer'],
            terms=validated_data.get('terms', 'net_30'),
            cash_account=validated_data.get('cash_account'),
            prepaid_account=validated_data.get('prepaid_account'),
            unearned_account=validated_data.get('unearned_account'),
            date_draft=validated_data.get('date_draft'),
            additional_info={
                'markdown_notes': validated_data.get('markdown_notes')
            }
        )
        
        # Add items if provided
        if items_data:
            factory.invoice.update_invoice_items(
                invoice=invoice,
                items_data=items_data
            )
            
        return invoice
        
    def update(self, instance, validated_data):
        """Update invoice."""
        if instance.invoice_status != 'draft':
            raise ValidationError(
                'Can only update draft invoices'
            )
            
        items_data = validated_data.pop('items', None)
        entity_slug = self.context['entity_slug']
        
        # Get service factory
        factory = service_provider.get_factory(entity_slug)
        
        # Update items if provided
        if items_data is not None:
            factory.invoice.update_invoice_items(
                invoice=instance,
                items_data=items_data
            )
            
        # Update other fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        return instance
