"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Tai San Va Cong Cu (Assets and Tools) module.
"""

from django.urls import path, include

# URL patterns for the module
urlpatterns = [
    # Include loai_tai_san_cong_cu URLs
    path('loai-tai-san-cong-cu/', include('django_ledger.api.routers.danh_muc.tai_san_va_cong_cu.loai_tai_san_cong_cu.urls')),
    path('ly-do-tang-giam-tai-san-co-dinh/', include('django_ledger.api.routers.danh_muc.tai_san_va_cong_cu.ly_do_tang_giam_tai_san_co_dinh.urls')),

    # Include bo_phan_su_dung_ts URLs
    path('bo-phan-su-dung-ts/', include('django_ledger.api.routers.danh_muc.tai_san_va_cong_cu.bo_phan_su_dung_ts.urls')),


    # Include ly_do_tang_giam_ccdc URLs
    path('ly-do-tang-giam-ccdc/', include('django_ledger.api.routers.danh_muc.tai_san_va_cong_cu.ly_do_tang_giam_ccdc.urls')),


    # Include bo_phan_su_dung_ccdc URLs
    path('bo-phan-su-dung-ccdc/', include('django_ledger.api.routers.danh_muc.tai_san_va_cong_cu.bo_phan_su_dung_ccdc.urls')),

    # Add other tai_san_va_cong_cu-related URLs here
]

