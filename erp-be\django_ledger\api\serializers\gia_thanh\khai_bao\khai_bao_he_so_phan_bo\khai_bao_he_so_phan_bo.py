"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for KhaiBaoHeSoPhanBo model.
"""
from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models.gia_thanh.khai_bao.khai_bao_he_so_phan_bo import KhaiBaoHeSoPhanBoModel
from django_ledger.models.entity import EntityModel
from django_ledger.models.unit import EntityUnitModel
from django_ledger.models.gia_thanh.danh_muc.yeu_to.yeu_to import YeuToModel
from django_ledger.models.gia_thanh.danh_muc.danh_muc_cong_doan import DanhMucCongDoanModel
from django_ledger.models.vat_tu.vat_tu import VatTuModel

from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.unit import EntityUnitModelSerializer
from django_ledger.api.serializers.gia_thanh.danh_muc.yeu_to.yeu_to import YeuToModelSerializer
from django_ledger.api.serializers.gia_thanh.danh_muc.danh_muc_cong_doan import DanhMucCongDoanSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.base import GlobalModelSerializer


class KhaiBaoHeSoPhanBoSerializer(GlobalModelSerializer):
    """
    Serializer for KhaiBaoHeSoPhanBo model.
    """
    # Read-only fields for related objects
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    ma_yt_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_bp0_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_vt_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = KhaiBaoHeSoPhanBoModel
        fields = [
            'uuid',
            'entity_model',
            'unit_id',
            'unit_id_data',
            'ky',
            'nam',
            'ma_yt',
            'ma_yt_data',
            'ma_bp',
            'ma_bp_data',
            'ma_bp0',
            'ma_bp0_data',
            'ma_sp',
            'ma_sp_data',
            'ma_vt',
            'ma_vt_data',
            'ma_lsx',
            'he_so',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'unit_id_data',
            'ma_yt_data',
            'ma_bp_data',
            'ma_bp0_data',
            'ma_sp_data',
            'ma_vt_data',
            'created',
            'updated'
        ]


    def get_unit_id_data(self, obj):
        """
        Get unit data.
        """
        if obj.unit_id:
            return EntityUnitModelSerializer(obj.unit_id).data
        return None

    def get_ma_yt_data(self, obj):
        """
        Get factor data.
        """
        if obj.ma_yt:
            return YeuToModelSerializer(obj.ma_yt).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Get department data.
        """
        if obj.ma_bp:
            return DanhMucCongDoanSerializer(obj.ma_bp).data
        return None

    def get_ma_bp0_data(self, obj):
        """
        Get source department data.
        """
        if obj.ma_bp0:
            return DanhMucCongDoanSerializer(obj.ma_bp0).data
        return None

    def get_ma_sp_data(self, obj):
        """
        Get product/material data.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_vt_data(self, obj):
        """
        Get material data.
        """
        if obj.ma_vt:
            return VatTuSerializer(obj.ma_vt).data
        return None

    def validate_he_so(self, value):
        """
        Validate he_so field.
        """
        if value <= 0:
            raise serializers.ValidationError(_('Hệ số phải lớn hơn 0'))
        return value

    def validate(self, data):
        """
        Validate the data.
        """
        # Check if ky is valid (1-12)
        if 'ky' in data and (data['ky'] < 1 or data['ky'] > 12):
            raise serializers.ValidationError(_('Kỳ phải từ 1 đến 12'))

        # Check if nam is valid (> 0)
        if 'nam' in data and data['nam'] <= 0:
            raise serializers.ValidationError(_('Năm phải lớn hơn 0'))

        return data
