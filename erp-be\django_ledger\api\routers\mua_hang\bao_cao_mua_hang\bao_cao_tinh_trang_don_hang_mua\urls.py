"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for Purchase Order Status Report (Bao Cao Tinh Trang Don Hang Mua) API endpoints.
"""

from django.urls import path
from django_ledger.api.views.mua_hang.bao_cao_mua_hang.bao_cao_tinh_trang_don_hang_mua import BaoCaoTinhTrangDonHangMuaViewSet

# URL patterns - Single endpoint for purchase order status report with filters as POST body data
urlpatterns = [
    # Purchase Order Status Report endpoint - returns report directly with filter POST body data
    path("", BaoCaoTinhTrangDonHangMuaViewSet.as_view({"post": "get_report", "get": "list"}), name="bao-cao-tinh-trang-don-hang-mua-report"),
]
