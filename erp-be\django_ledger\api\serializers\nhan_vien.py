from rest_framework import serializers

from django_ledger.models.nhan_vien import NhanVienModel
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.ngan_hang import NganHangModelSerializer
from django_ledger.api.serializers.tinh_thanh import TinhThanhModelSerializer
from django_ledger.models import TinhThanhModel

class NhanVienModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the NhanVienModel (Employee) model.

    This serializer handles the conversion between NhanVienModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields (ma_bo_phan, ngan_hang, etc.)
    - Adds additional fields with "_data" suffix (ma_bo_phan_data, ngan_hang_data, etc.)
      that contain the complete nested object data with all available fields
    - When deserializing, accepts UUIDs for reference fields

    Example response:
    {
        "uuid": "dd06235c-76f3-44e1-b7f7-cb2be5e43639",
        "ma_nhan_vien": "NV001",
        "ho_ten_nhan_vien": "Nguyen <PERSON> A",
        "ma_bo_phan": "815dde91-3f8a-4a27-8316-9e40859780ae",
        "ma_bo_phan_data": {
            "uuid": "815dde91-3f8a-4a27-8316-9e40859780ae",
            "ma_bp": "BP001",
            "ten_bp": "Bộ phận kỹ thuật",
            ...
        },
        ...
    }
    """
    # Define additional fields for nested data
    ma_bo_phan_data = serializers.SerializerMethodField(read_only=True)
    noi_cap_cmnd_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = NhanVienModel
        fields = [
            'uuid',
            'entity_model',
            'ma_nhan_vien',
            'nhan_vien_ban_hang',
            'nhan_vien_mua_hang',
            'cong_no_tam_ung',
            'ho_ten_nhan_vien',
            'chuc_vu',
            'ma_bo_phan',
            'ma_bo_phan_data',
            'gioi_tinh',
            'ngay_sinh',
            'noi_sinh',
            'dia_chi',
            'dien_thoai',
            'email',
            'so_cmnd',
            'ngay_hieu_luc_cmnd',
            'noi_cap_cmnd',
            'noi_cap_cmnd_data',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'created',
            'updated',
            'entity_model',
            'ma_bo_phan_data',
            'noi_cap_cmnd_data',
        ]

    def get_ma_bo_phan_data(self, obj):
        """Method field for ma_bo_phan_data"""
        if obj.ma_bo_phan:
            return BoPhanModelSerializer(obj.ma_bo_phan).data
        return None

    def get_noi_cap_cmnd_data(self, obj):
        """Method field for noi_cap_cmnd_data"""
        if obj.noi_cap_cmnd:
            try:
                # Try to get TinhThanhModel by UUID
                tinh_thanh = TinhThanhModel.objects.filter(uuid=obj.noi_cap_cmnd).first()
                if tinh_thanh:
                    return TinhThanhModelSerializer(tinh_thanh).data
            except (ValueError, TypeError):
                pass
        return None
