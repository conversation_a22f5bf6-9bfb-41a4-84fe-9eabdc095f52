"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Admin configuration for TyGiaModel (Exchange Rate).
"""

from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from django_ledger.models import TyGiaModel


class TyGiaModelAdmin(admin.ModelAdmin):
    """
    Admin class for TyGiaModel (Exchange Rate).
    """
    list_display = [
        'ma_nt',
        'ngay_hl',
        'ty_gia',
        'status',
    ]
    list_filter = ['status', 'ma_nt', 'ngay_hl']
    search_fields = ['ma_nt__ma_nt', 'ma_nt__ten_nt']
    readonly_fields = ['uuid', 'created', 'updated']
    fieldsets = [
        (_('Basic Information'), {
            'fields': [
                'uuid',
                'ma_nt',
                'ngay_hl',
                'ty_gia',
                'status',
            ]
        }),
        (_('Metadata'), {
            'fields': [
                'created',
                'updated',
            ]
        }),
    ]
