"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for PhieuKeToaNghiepVu model.
"""
from rest_framework import serializers

from django_ledger.models import PhieuKeToaNghiepVuModel
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.unit import EntityUnitModelSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer
from django_ledger.api.serializers.quyen_chung_tu import QuyenChungTuDetailSerializer
from django_ledger.api.serializers.chung_tu import ChungTuSerializer


class PhieuKeToaNghiepVuSerializer(serializers.ModelSerializer):
    """
    Serializer for PhieuKeToaNghiepVu model.
    """
    # Read-only fields for related objects
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    ma_nk_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)
    so_ct_data = serializers.SerializerMethodField(read_only=True)

    # Child data
    chi_tiet_data = serializers.SerializerMethodField(read_only=True)
    thong_tin_thue_data = serializers.SerializerMethodField(read_only=True)

    # Write-only fields for child data
    chi_tiet = serializers.ListField(required=False, write_only=True)
    thong_tin_thue = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = PhieuKeToaNghiepVuModel
        fields = [
            'uuid',
            'entity_model',
            'ma_ngv',
            'unit_id',
            'unit_id_data',
            'ma_nk',
            'ma_nk_data',
            'ma_nt',
            'ma_nt_data',
            'i_so_ct',
            'so_ct',
            'so_ct_data',
            'ngay_ct',
            'ngay_lct',
            'dien_giai',
            'ty_gia',
            'status',
            'transfer_yn',
            't_ps_nt',
            't_ps',
            'id_progress',
            'chi_tiet_data',
            'thong_tin_thue_data',
            'chi_tiet',
            'thong_tin_thue',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'unit_id_data',
            'ma_nk_data',
            'ma_nt_data',
            'so_ct_data',
            'chi_tiet_data',
            'thong_tin_thue_data',
            'created',
            'updated'
        ]


    def get_unit_id_data(self, obj):
        """
        Get entity unit data for unit_id.
        """
        if obj.unit_id:
            return EntityUnitModelSerializer(obj.unit_id).data
        return None

    def get_ma_nk_data(self, obj):
        """
        Get document authority data for ma_nk.
        """
        if obj.ma_nk:
            return QuyenChungTuDetailSerializer(obj.ma_nk).data
        return None

    def get_ma_nt_data(self, obj):
        """
        Get currency data for ma_nt.
        """
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_so_ct_data(self, obj):
        """
        Get document data for so_ct.
        """
        if obj.so_ct:
            return ChungTuSerializer(obj.so_ct).data
        return None

    def get_chi_tiet_data(self, obj):
        """
        Get child details data.
        """
        # Import here to avoid circular imports
        from django_ledger.api.serializers.tong_hop.hach_toan.phieu_ke_toan_theo_nghiep_vu.chi_tiet_phieu_ke_toan_theo_nghiep_vu import ChiTietPhieuKeToaNghiepVuSerializer

        if hasattr(obj, 'chi_tiet_prepared'):
            return ChiTietPhieuKeToaNghiepVuSerializer(obj.chi_tiet_prepared, many=True).data
        # Fallback if no prepared data
        return ChiTietPhieuKeToaNghiepVuSerializer(obj.chi_tiet.all(), many=True).data

    def get_thong_tin_thue_data(self, obj):
        """
        Get tax information data.
        """
        # Import here to avoid circular imports
        from django_ledger.api.serializers.tong_hop.hach_toan.phieu_ke_toan_theo_nghiep_vu.thue_phieu_ke_toan_theo_nghiep_vu import ThuePhieuKeToaNghiepVuSerializer

        if hasattr(obj, 'thong_tin_thue_prepared'):
            return ThuePhieuKeToaNghiepVuSerializer(obj.thong_tin_thue_prepared, many=True).data
        # Fallback if no prepared data
        return ThuePhieuKeToaNghiepVuSerializer(obj.thong_tin_thue.all(), many=True).data
