"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL Configuration for KhaiBaoDoiTuongNhanPhanBoChiPhi API.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.gia_thanh.khai_bao.khai_bao_doi_tuong_nhan_phan_bo_chi_phi import KhaiBaoDoiTuongNhanPhanBoChiPhiViewSet

# Main router for KhaiBaoDoiTuongNhanPhanBoChiPhi
router = DefaultRouter()
router.register('', KhaiBaoDoiTuongNhanPhanBoChiPhiViewSet, basename='khai-bao-doi-tuong-nhan-phan-bo-chi-phi')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
