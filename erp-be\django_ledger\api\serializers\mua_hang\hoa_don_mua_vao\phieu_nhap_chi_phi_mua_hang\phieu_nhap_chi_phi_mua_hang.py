"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the PhieuNhapChiPhiMuaHangSerializer, which handles serialization
for the PhieuNhapChiPhiMuaHangModel.
"""

from rest_framework import serializers

from django_ledger.models.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import PhieuNhapChiPhiMuaHangModel
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang.chi_tiet_phieu_nhap_chi_phi_mua_hang import ChiTietPhieuNhapChiPhiMuaHangSerializer
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang.chi_phi_phieu_nhap_chi_phi_mua_hang import ChiPhiPhieuNhapChiPhiMuaHangSerializer
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang.chi_phi_chi_tiet_phieu_nhap_chi_phi_mua_hang import ChiPhiChiTietPhieuNhapChiPhiMuahangSerializer
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang.thue_phieu_nhap_chi_phi_mua_hang import ThuePhieuNhapChiPhiMuaHangSerializer


class PhieuNhapChiPhiMuaHangSerializer(serializers.ModelSerializer):
    """
    Serializer for PhieuNhapChiPhiMuaHangModel.
    """

    chi_tiet_phieu_nhaps = ChiTietPhieuNhapChiPhiMuaHangSerializer(many=True, required=False)
    chi_phi_phieu_nhaps = ChiPhiPhieuNhapChiPhiMuaHangSerializer(many=True, required=False)
    chi_phi_chi_tiet_phieu_nhaps = ChiPhiChiTietPhieuNhapChiPhiMuahangSerializer(many=True, required=False)
    thue_phieu_nhaps = ThuePhieuNhapChiPhiMuaHangSerializer(many=True, required=False)

    # Reference data fields
    ma_kh_data = serializers.SerializerMethodField()
    tk_data = serializers.SerializerMethodField()
    ma_nk_data = serializers.SerializerMethodField()
    so_ct_data = serializers.SerializerMethodField()
    ma_nt_data = serializers.SerializerMethodField()

    class Meta:
        model = PhieuNhapChiPhiMuaHangModel
        fields = [
            'uuid', 'entity_model', 'ma_kh', 'ten_kh', 'ong_ba', 'e_mail', 'tk', 'ma_tt', 'dien_giai',
            'ma_ngv', 'unit_id', 'i_so_ct', 'ma_nk', 'so_ct', 'ngay_ct', 'ngay_lct', 'so_ct0', 'so_ct2',
            'ngay_ct0', 'ma_nt', 'ty_gia', 'status', 'transfer_yn', 'ma_gd', 't_tien_nt', 't_tien',
            't_cp_nt', 't_cp', 't_thue_nt', 't_thue', 't_tt_nt', 't_tt', 'created_at', 'created_by',
            'chi_tiet_phieu_nhaps', 'chi_phi_phieu_nhaps', 'chi_phi_chi_tiet_phieu_nhaps', 'thue_phieu_nhaps',
            'ma_kh_data', 'tk_data', 'ma_nk_data', 'so_ct_data', 'ma_nt_data'
        ]
        read_only_fields = ['uuid', 'entity_model', 'created_at', 'created_by']

    def get_ma_kh_data(self, obj):
        """
        Returns the customer data for the ma_kh field.
        """
        if obj.ma_kh:
            return {
                'uuid': obj.ma_kh.uuid,
                'ma_kh': obj.ma_kh.ma_kh,
                'ten_kh': obj.ma_kh.ten_kh
            }
        return None

    def get_tk_data(self, obj):
        """
        Returns the account data for the tk field.
        """
        if obj.tk:
            return {
                'uuid': obj.tk.uuid,
                'code': obj.tk.code,
                'name': obj.tk.name
            }
        return None

    def get_ma_nk_data(self, obj):
        """
        Returns the document permission data for the ma_nk field.
        """
        if obj.ma_nk:
            return {
                'uuid': obj.ma_nk.uuid,
                'ma_nk': obj.ma_nk.ma_nk,
                'ten_nk': obj.ma_nk.ten_nk
            }
        return None

    def get_so_ct_data(self, obj):
        """
        Returns the document data for the so_ct field.
        """
        if obj.so_ct:
            return {
                'uuid': obj.so_ct.uuid,
                'so_ct': obj.so_ct.so_ct,
                'ten_ct': obj.so_ct.ten_ct
            }
        return None

    def get_ma_nt_data(self, obj):
        """
        Returns the currency data for the ma_nt field.
        """
        if obj.ma_nt:
            return {
                'uuid': obj.ma_nt.uuid,
                'ma_nt': obj.ma_nt.ma_nt,
                'ten_nt': obj.ma_nt.ten_nt
            }
        return None


