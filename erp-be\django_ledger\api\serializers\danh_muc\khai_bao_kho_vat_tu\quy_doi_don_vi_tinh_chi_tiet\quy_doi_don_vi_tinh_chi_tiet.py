"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for QuyDoiDonViTinhChiTiet model.
"""
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from django_ledger.models.danh_muc.khai_bao_kho_vat_tu.quy_doi_don_vi_tinh_chi_tiet import QuyDoiDonViTinhChiTietModel
from django_ledger.models.vat_tu.vat_tu import VatTuModel
from django_ledger.models.don_vi_tinh import DonViTinhModel
from django_ledger.api.serializers.base import GlobalModelSerializer
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer


class QuyDoiDonViTinhChiTietSerializer(GlobalModelSerializer):
    """
    Serializer class for QuyDoiDonViTinhChiTietModel that handles data serialization/deserialization.
    Inherits from GlobalModelSerializer for common fields and behaviors.

    Fields:
    - uuid: Unique identifier (read-only)
    - entity_model: Entity model UUID
    - ma_vt: Material UUID
    - ma_vt_data: Material object (read-only)
    - dvt: Unit UUID
    - dvt_data: Unit object (read-only)
    - he_so: Conversion factor
    - status: Status indicator ('1'=active, '0'=inactive)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    """
    # Read-only fields for related objects
    ma_vt_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = QuyDoiDonViTinhChiTietModel
        fields = [
            'uuid',
            'entity_model',
            'ma_vt',
            'ma_vt_data',
            'dvt',
            'dvt_data',
            'he_so',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated']

    def get_ma_vt_data(self, obj):
        """
        Get basic information about the material
        """
        if not obj.ma_vt:
            return None

        # Create a dictionary for the dvt field instead of using the object directly
        dvt_data = None
        if obj.ma_vt.dvt:
            dvt_data = {
                'uuid': obj.ma_vt.dvt.uuid,
                'dvt': obj.ma_vt.dvt.dvt,
                'ten_dvt': obj.ma_vt.dvt.ten_dvt
            }

        return {
            'uuid': obj.ma_vt.uuid,
            'ma_vt': obj.ma_vt.ma_vt,
            'ten_vt': obj.ma_vt.ten_vt,
            'dvt': dvt_data
        }

    def get_dvt_data(self, obj):
        """
        Get basic information about the unit of measurement
        """
        if not obj.dvt:
            return None

        return {
            'uuid': obj.dvt.uuid,
            'dvt': obj.dvt.dvt,
            'ten_dvt': obj.dvt.ten_dvt
        }

    def create(self, validated_data):
        """
        Create a new QuyDoiDonViTinhChiTiet instance.

        Parameters
        ----------
        validated_data: dict
            Validated data for creating the instance

        Returns
        -------
        QuyDoiDonViTinhChiTietModel
            The created instance
        """
        return super().create(validated_data)

    def update(self, instance, validated_data):
        """
        Update an existing QuyDoiDonViTinhChiTiet instance.

        Parameters
        ----------
        instance: QuyDoiDonViTinhChiTietModel
            The instance to update
        validated_data: dict
            Validated data for updating the instance

        Returns
        -------
        QuyDoiDonViTinhChiTietModel
            The updated instance
        """
        # Update fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        instance.save()
        return instance


class QuyDoiDonViTinhChiTietCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating QuyDoiDonViTinhChiTietModel
    """
    class Meta:
        model = QuyDoiDonViTinhChiTietModel
        fields = [
            'ma_vt',
            'dvt',
            'he_so',
            'status',
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Make fields optional for PATCH requests
        request = self.context.get('request')
        if request and request.method == 'PATCH':
            for field in self.fields:
                self.fields[field].required = False
