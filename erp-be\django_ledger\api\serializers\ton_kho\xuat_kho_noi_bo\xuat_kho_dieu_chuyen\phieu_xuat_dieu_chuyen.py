"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuXuatDieuChuyen (Warehouse Transfer) serializer implementation.
"""

from rest_framework import serializers

from django_ledger.models.ton_kho.xuat_kho_noi_bo.xuat_kho_dieu_chuyen import PhieuXuatDieuChuyenModel
from django_ledger.api.serializers.ton_kho.xuat_kho_noi_bo.xuat_kho_dieu_chuyen.chi_tiet_phieu_xuat_dieu_chuyen import ChiTietPhieuXuatDieuChuyenModelSerializer

# Import serializers for foreign key fields
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.erp import CustomerModelSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer 
from django_ledger.api.serializers.quyen_chung_tu import QuyenChungTuListSerializer
from django_ledger.api.serializers.chung_tu import ChungTuSerializer
from django_ledger.api.serializers.warehouse import KhoHangModelSerializer
from django_ledger.api.serializers.phuong_tien_van_chuyen import PhuongTienVanChuyenModelSerializer


class PhieuXuatDieuChuyenModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the PhieuXuatDieuChuyenModel.

    This serializer handles the conversion between PhieuXuatDieuChuyenModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields
    - Adds additional fields with "_data" suffix that contain the complete nested object data
    - When deserializing, accepts UUIDs for reference fields
    """
    # Add nested serializers for foreign key fields
    entity_model_data = EntityModelSerializer(source='entity_model', read_only=True)
    ma_nk_data = QuyenChungTuListSerializer(source='ma_nk', read_only=True)
    so_ct_data = ChungTuSerializer(source='so_ct', read_only=True)
    ma_kho_data = KhoHangModelSerializer(source='ma_kho', read_only=True)
    ma_kh_vc_data = CustomerModelSerializer(source='ma_kh_vc', read_only=True)
    ma_nt_data = NgoaiTeSerializer(source='ma_nt', read_only=True)
    ma_ptvc_data = PhuongTienVanChuyenModelSerializer(source='ma_ptvc', read_only=True)

    # Add chi_tiet field for nested serialization
    chi_tiet = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = PhieuXuatDieuChuyenModel
        fields = [
            'uuid',
            'entity_model',
            'entity_model_data',
            'created_at',
            'created_by',
            'ma_ngv',
            'so_buoc',
            'ma_gd',
            'ma_kho',
            'ma_kho_data',
            'dien_giai',
            'unit_id',
            'i_so_ct',
            'ma_nk',
            'ma_nk_data',
            'so_ct',
            'so_ct_data',
            'ngay_ct',
            'ngay_lct',
            'ma_nt',
            'ma_nt_data',
            'ty_gia',
            'status',
            'transfer_yn',
            'ma_tthddt',
            'ma_pttt',
            'lenh_dd',
            'ngay_dd',
            'nguoi_dd',
            'viec_dd',
            'ma_kh_vc',
            'ma_kh_vc_data',
            'ten_kh_vc',
            'so_hd_vc',
            'ma_ptvc',
            'ma_ptvc_data',
            'ly_do_huy',
            'ly_do',
            't_so_luong',
            't_tien_nt',
            't_tien',
            'chi_tiet',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model_data',
            'ma_nk_data',
            'so_ct_data',
            'ma_kho_data',
            'ma_kh_vc_data',
            'ma_nt_data',
            'ma_ptvc_data',
            'chi_tiet',
            'created',
            'updated'
        ]

    def get_chi_tiet(self, obj):
        """
        Get the chi_tiet (details) for the PhieuXuatDieuChuyen.
        This is a method field that returns a list of ChiTietPhieuXuatDieuChuyen objects.

        Parameters
        ----------
        obj : PhieuXuatDieuChuyenModel
            The PhieuXuatDieuChuyenModel instance

        Returns
        -------
        list
            List of serialized ChiTietPhieuXuatDieuChuyen objects
        """
        chi_tiet = obj.chi_tiet_phieu_xuat_dieu_chuyen.all()
        return ChiTietPhieuXuatDieuChuyenModelSerializer(chi_tiet, many=True).data
