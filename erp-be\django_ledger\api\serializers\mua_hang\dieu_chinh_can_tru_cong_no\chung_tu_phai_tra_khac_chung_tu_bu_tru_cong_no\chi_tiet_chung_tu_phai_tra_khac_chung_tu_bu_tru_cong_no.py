"""
Serializer for ChiTietChungTuPhaiTraKhacChungTuBuTruCongNoModel.
"""

from django.utils.translation import gettext_lazy as _
from django_ledger.models.mua_hang.dieu_chinh_can_tru_cong_no import (
    ChiTietChungTuPhaiTraKhacChungTuBuTruCongNoModel,
)
from rest_framework import serializers


class ChiTietChungTuPhaiTraKhacChungTuBuTruCongNoSerializer(
    serializers.ModelSerializer
):
    """
    Serializer for ChiTietChungTuPhaiTraKhacChungTuBuTruCongNoModel.
    """

    # Read-only fields for related objects
    giao_dich_data = serializers.SerializerMethodField(read_only=True)
    tk_data = serializers.SerializerMethodField(read_only=True)
    tk_cn_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    so_ct0_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_dtt_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_lsx_data = serializers.SerializerMethodField(read_only=True)
    ma_cp0_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietChungTuPhaiTraKhacChungTuBuTruCongNoModel
        fields = [
            "uuid",
            "giao_dich",
            "giao_dich_data",
            "line",
            "tk",
            "tk_data",
            "tk_cn",
            "tk_cn_data",
            "ma_kh",
            "ma_kh_data",
            "ty_gia2",
            "ps_no_nt",
            "ps_co_nt",
            "nh_dk",
            "dien_giai",
            "so_ct0",
            "so_ct0_data",
            "ngay_ct0",
            "ps_no",
            "ps_co",
            "ma_bp",
            "ma_bp_data",
            "ma_vv",
            "ma_vv_data",
            "ma_hd",
            "ma_hd_data",
            "ma_dtt",
            "ma_dtt_data",
            "ma_ku",
            "ma_ku_data",
            "ma_phi",
            "ma_phi_data",
            "ma_sp",
            "ma_sp_data",
            "ma_lsx",
            "ma_lsx_data",
            "ma_cp0",
            "ma_cp0_data",
            "id_tt",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "giao_dich",
            "giao_dich_data",
            "tk_data",
            "tk_cn_data",
            "ma_kh_data",
            "so_ct0_data",
            "ma_bp_data",
            "ma_vv_data",
            "ma_hd_data",
            "ma_dtt_data",
            "ma_ku_data",
            "ma_phi_data",
            "ma_sp_data",
            "ma_lsx_data",
            "ma_cp0_data",
            "created",
            "updated",
        ]

    def get_giao_dich_data(self, obj):
        """
        Get parent transaction data.
        """
        if obj.giao_dich:
            return {
                "uuid": str(obj.giao_dich.uuid),
                "so_ct": obj.giao_dich.so_ct,
                "dien_giai": obj.giao_dich.dien_giai,
            }
        return None

    def get_tk_data(self, obj):
        """
        Get account data.
        """
        if obj.tk:
            return {
                "uuid": str(obj.tk.uuid),
                "code": obj.tk.code if hasattr(obj.tk, "code") else None,
                "name": obj.tk.name if hasattr(obj.tk, "name") else None,
            }
        return None

    def get_tk_cn_data(self, obj):
        """
        Get branch account data.
        """
        if obj.tk_cn:
            return {
                "uuid": str(obj.tk_cn.uuid),
                "code": obj.tk_cn.code if hasattr(obj.tk_cn, "code") else None,
                "name": obj.tk_cn.name if hasattr(obj.tk_cn, "name") else None,
            }
        return None

    def get_so_ct0_data(self, obj):
        """
        Get reference document data.
        """
        if obj.so_ct0:
            return {
                "uuid": str(obj.so_ct0.uuid),
                "so_ct": obj.so_ct0.so_ct if hasattr(obj.so_ct0, "so_ct") else None,
                "dien_giai": (
                    obj.so_ct0.dien_giai if hasattr(obj.so_ct0, "dien_giai") else None
                ),
            }
        return None

    def get_ma_kh_data(self, obj):
        """
        Get customer data.
        """
        if obj.ma_kh:
            return {
                "uuid": str(obj.ma_kh.uuid),
                "ma_kh": obj.ma_kh.ma_kh if hasattr(obj.ma_kh, "ma_kh") else None,
                "ten_kh": obj.ma_kh.ten_kh if hasattr(obj.ma_kh, "ten_kh") else None,
            }
        return None

    def get_ma_bp_data(self, obj):
        """
        Get department data.
        """
        if obj.ma_bp:
            return {
                "uuid": str(obj.ma_bp.uuid),
                "ma_bp": obj.ma_bp.ma_bp if hasattr(obj.ma_bp, "ma_bp") else None,
                "ten_bp": obj.ma_bp.ten_bp if hasattr(obj.ma_bp, "ten_bp") else None,
            }
        return None

    def get_ma_vv_data(self, obj):
        """
        Get task data.
        """
        if obj.ma_vv:
            return {
                "uuid": str(obj.ma_vv.uuid),
                "ma_vv": obj.ma_vv.ma_vv if hasattr(obj.ma_vv, "ma_vv") else None,
                "ten_vv": obj.ma_vv.ten_vv if hasattr(obj.ma_vv, "ten_vv") else None,
            }
        return None

    def get_ma_hd_data(self, obj):
        """
        Get contract data.
        """
        if obj.ma_hd:
            return {
                "uuid": str(obj.ma_hd.uuid),
                "ma_hd": obj.ma_hd.ma_hd if hasattr(obj.ma_hd, "ma_hd") else None,
                "ten_hd": obj.ma_hd.ten_hd if hasattr(obj.ma_hd, "ten_hd") else None,
            }
        return None

    def get_ma_dtt_data(self, obj):
        """
        Get payment batch data.
        """
        if obj.ma_dtt:
            return {
                "uuid": str(obj.ma_dtt.uuid),
                "ma_dtt": obj.ma_dtt.ma_dtt if hasattr(obj.ma_dtt, "ma_dtt") else None,
                "ten_dtt": (
                    obj.ma_dtt.ten_dtt if hasattr(obj.ma_dtt, "ten_dtt") else None
                ),
            }
        return None

    def get_ma_ku_data(self, obj):
        """
        Get agreement data.
        """
        if obj.ma_ku:
            return {
                "uuid": str(obj.ma_ku.uuid),
                "ma_ku": obj.ma_ku.ma_ku if hasattr(obj.ma_ku, "ma_ku") else None,
                "ten_ku": obj.ma_ku.ten_ku if hasattr(obj.ma_ku, "ten_ku") else None,
            }
        return None

    def get_ma_phi_data(self, obj):
        """
        Get fee data.
        """
        if obj.ma_phi:
            return {
                "uuid": str(obj.ma_phi.uuid),
                "ma_phi": obj.ma_phi.ma_phi if hasattr(obj.ma_phi, "ma_phi") else None,
                "ten_phi": (
                    obj.ma_phi.ten_phi if hasattr(obj.ma_phi, "ten_phi") else None
                ),
            }
        return None

    def get_ma_sp_data(self, obj):
        """
        Get product data.
        """
        if obj.ma_sp:
            return {
                "uuid": str(obj.ma_sp.uuid),
                "ma_vt": obj.ma_sp.ma_vt if hasattr(obj.ma_sp, "ma_vt") else None,
                "ten_vt": obj.ma_sp.ten_vt if hasattr(obj.ma_sp, "ten_vt") else None,
            }
        return None

    def get_ma_lsx_data(self, obj):
        """
        Get production order data.
        """
        if obj.ma_lsx:
            return {
                "ma_lsx": obj.ma_lsx,
            }
        return None

    def get_ma_cp0_data(self, obj):
        """
        Get invalid cost data.
        """
        if obj.ma_cp0:
            return {
                "uuid": str(obj.ma_cp0.uuid),
                "ma_cp0": obj.ma_cp0.ma_cp0 if hasattr(obj.ma_cp0, "ma_cp0") else None,
                "ten_cp0": (
                    obj.ma_cp0.ten_cp0 if hasattr(obj.ma_cp0, "ten_cp0") else None
                ),
            }
        return None

    def validate(self, attrs):
        """
        Validate the serializer data.
        """
        # Check if this is an update or create operation
        is_update = self.instance is not None

        # Validate required fields for create operations
        if not is_update:
            required_fields = ["line", "tk"]
            for field in required_fields:
                if field not in attrs:
                    raise serializers.ValidationError(
                        {field: _("This field is required.")}
                    )

        # Set default values if not provided
        if "ps_no" not in attrs:
            attrs["ps_no"] = 0

        if "ps_co" not in attrs:
            attrs["ps_co"] = 0

        if "ps_no_nt" not in attrs:
            attrs["ps_no_nt"] = 0

        if "ps_co_nt" not in attrs:
            attrs["ps_co_nt"] = 0

        if "ty_gia2" not in attrs:
            attrs["ty_gia2"] = 1

        if "nh_dk" not in attrs:
            attrs["nh_dk"] = 0

        if "id_tt" not in attrs:
            attrs["id_tt"] = 0

        # Validate that either debit or credit is non-zero, but not both
        ps_no = attrs.get("ps_no", 0)
        ps_co = attrs.get("ps_co", 0)

        if ps_no > 0 and ps_co > 0:
            raise serializers.ValidationError(
                {
                    "non_field_errors": _(
                        "A line cannot have both debit and credit amounts"
                    )
                }
            )

        if ps_no == 0 and ps_co == 0:
            raise serializers.ValidationError(
                {
                    "non_field_errors": _(
                        "A line must have either debit or credit amount"
                    )
                }
            )

        return attrs
