"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for <PERSON><PERSON> (Catalog) module.
"""

from django.urls import include, path

# URL patterns for the module
urlpatterns = [
    # Include hop_dong_khe_uoc URLs
    path(
        "hop-dong-khe-uoc/",
        include("django_ledger.api.routers.danh_muc.hop_dong_khe_uoc.urls"),
    ),
    # Include khai_bao_kho_vat_tu URLs
    path(
        "khai-bao-kho-vat-tu/",
        include("django_ledger.api.routers.danh_muc.khai_bao_kho_vat_tu.urls"),
    ),
    # Include ban_hang URLs
    path("ban-hang/", include("django_ledger.api.routers.danh_muc.ban_hang.urls")),
    # Include tai_san_va_cong_cu URLs
    path(
        "tai-san-va-cong-cu/",
        include("django_ledger.api.routers.danh_muc.tai_san_va_cong_cu.urls"),
    ),
    # Include ke_toan URLs
    path("ke-toan/", include("django_ledger.api.routers.danh_muc.ke_toan.urls")),
    # Add other danh_muc-related URLs here
]
