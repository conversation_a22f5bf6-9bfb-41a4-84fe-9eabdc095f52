"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for ChiTietDonMuaHang (Purchase Order Detail) model.
"""

from rest_framework import serializers
from decimal import Decimal
from django_ledger.api.serializers.base import GlobalModelSerializer
from django_ledger.models.mua_hang.don_mua_hang.chi_tiet_don_mua_hang import ChiTietDonMuaHangModel
from django.utils.translation import gettext_lazy as _


class ChiTietDonMuaHangSerializer(GlobalModelSerializer):
    """
    Serializer for ChiTietDonMuaHangModel (Purchase Order Detail)
    """
    entity_model = serializers.UUIDField(read_only=True)
    don_mua_hang = serializers.UUIDField(read_only=True)

    # Nested data fields for reference fields
    product_code_data = serializers.SerializerMethodField(read_only=True)
    unit_data = serializers.SerializerMethodField(read_only=True)
    department_data = serializers.SerializerMethodField(read_only=True)
    project_data = serializers.SerializerMethodField(read_only=True)
    contract_data = serializers.SerializerMethodField(read_only=True)
    payment_period_data = serializers.SerializerMethodField(read_only=True)
    agreement_data = serializers.SerializerMethodField(read_only=True)
    fee_code_data = serializers.SerializerMethodField(read_only=True)
    product_ref_data = serializers.SerializerMethodField(read_only=True)
    invalid_cost_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietDonMuaHangModel
        fields = [
            'uuid',
            'entity_model',
            'don_mua_hang',
            'line_number',
            'product_code',
            'product_code_data',
            'product_name',
            'unit',
            'unit_data',
            'quantity',
            'price_vnd',
            'amount_vnd',
            'delivery_date',
            'tax_code',
            'tax_rate',
            'tax_amount_vnd',
            'department',
            'department_data',
            'project',
            'project_data',
            'contract',
            'contract_data',
            'payment_period',
            'payment_period_data',
            'agreement',
            'agreement_data',
            'fee_code',
            'fee_code_data',
            'product_ref',
            'product_ref_data',
            'production_order',
            'invalid_cost',
            'invalid_cost_data',
            'invoice_quantity',
            'receipt_quantity',
            'order_number',
            'price_comparison_number',
            'price_comparison_line',
            'active',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated', 'entity_model', 'don_mua_hang']

    def validate(self, data):
        """Custom validation for the entire object"""
        # Validate quantity
        quantity = data.get('quantity')
        if quantity is not None and quantity <= 0:
            raise serializers.ValidationError(
                _("Số lượng phải lớn hơn 0")
            )

        # Validate price
        price_vnd = data.get('price_vnd')
        if price_vnd is not None and price_vnd < 0:
            raise serializers.ValidationError(
                _("Giá không được âm")
            )

        # Validate tax rate
        tax_rate = data.get('tax_rate')
        if tax_rate is not None and (tax_rate < 0 or tax_rate > 100):
            raise serializers.ValidationError(
                _("Thuế suất phải nằm trong khoảng từ 0 đến 100")
            )

        # Calculate amount_vnd if not provided
        if 'quantity' in data and 'price_vnd' in data and 'amount_vnd' not in data:
            data['amount_vnd'] = Decimal(str(data['quantity'])) * Decimal(str(data['price_vnd']))

        # Calculate tax_amount_vnd if not provided
        if 'amount_vnd' in data and 'tax_rate' in data and 'tax_amount_vnd' not in data:
            data['tax_amount_vnd'] = Decimal(str(data['amount_vnd'])) * (Decimal(str(data['tax_rate'])) / 100)

        return data

    def create(self, validated_data):
        """
        Create a new ChiTietDonMuaHang instance
        The entity_model and don_mua_hang will be handled by the service layer
        """
        return super().create(validated_data)

    def update(self, instance, validated_data):
        """
        Update a ChiTietDonMuaHang instance
        """
        return super().update(instance, validated_data)

    def get_product_code_data(self, obj):
        """Get nested data for product_code field"""
        if obj.product_code:
            return {
                'uuid': obj.product_code.uuid,
                'ma_vt': obj.product_code.ma_vt,
                'ten_vt': obj.product_code.ten_vt,
                'ma_dvt': getattr(obj.product_code.ma_dvt, 'uuid', None),
                'ma_dvt_data': {
                    'uuid': obj.product_code.ma_dvt.uuid,
                    'ma_dvt': obj.product_code.ma_dvt.ma_dvt,
                    'ten_dvt': obj.product_code.ma_dvt.ten_dvt
                } if obj.product_code.ma_dvt else None
            }
        return None

    def get_unit_data(self, obj):
        """Get nested data for unit field"""
        if obj.unit:
            return {
                'uuid': obj.unit.uuid,
                'ma_dvt': obj.unit.ma_dvt,
                'ten_dvt': obj.unit.ten_dvt
            }
        return None

    def get_department_data(self, obj):
        """Get nested data for department field"""
        if obj.department:
            return {
                'uuid': obj.department.uuid,
                'ma_nhom': obj.department.ma_nhom,
                'ten_nhom': obj.department.ten_nhom
            }
        return None

    def get_project_data(self, obj):
        """Get nested data for project field"""
        if obj.project:
            return {
                'uuid': obj.project.uuid,
                'ma_vv': obj.project.ma_vv,
                'ten_vv': obj.project.ten_vv
            }
        return None

    def get_contract_data(self, obj):
        """Get nested data for contract field"""
        if obj.contract:
            return {
                'uuid': obj.contract.uuid,
                'ma_hd': obj.contract.ma_hd,
                'ten_hd': obj.contract.ten_hd
            }
        return None

    def get_payment_period_data(self, obj):
        """Get nested data for payment_period field"""
        if obj.payment_period:
            return {
                'uuid': obj.payment_period.uuid,
                'ma_dtt': obj.payment_period.ma_dtt,
                'ten_dtt': obj.payment_period.ten_dtt
            }
        return None

    def get_agreement_data(self, obj):
        """Get nested data for agreement field"""
        if obj.agreement:
            return {
                'uuid': obj.agreement.uuid,
                'ma_ku': obj.agreement.ma_ku,
                'ten_ku': obj.agreement.ten_ku
            }
        return None

    def get_fee_code_data(self, obj):
        """Get nested data for fee_code field"""
        if obj.fee_code:
            return {
                'uuid': obj.fee_code.uuid,
                'ma_phi': obj.fee_code.ma_phi,
                'ten_phi': obj.fee_code.ten_phi
            }
        return None

    def get_product_ref_data(self, obj):
        """Get nested data for product_ref field"""
        if obj.product_ref:
            return {
                'uuid': obj.product_ref.uuid,
                'ma_vt': obj.product_ref.ma_vt,
                'ten_vt': obj.product_ref.ten_vt,
                'ma_dvt': getattr(obj.product_ref.ma_dvt, 'uuid', None),
                'ma_dvt_data': {
                    'uuid': obj.product_ref.ma_dvt.uuid,
                    'ma_dvt': obj.product_ref.ma_dvt.ma_dvt,
                    'ten_dvt': obj.product_ref.ma_dvt.ten_dvt
                } if obj.product_ref.ma_dvt else None
            }
        return None

    def get_invalid_cost_data(self, obj):
        """Get nested data for invalid_cost field"""
        # Kiểm tra xem invalid_cost có phải là ForeignKey hay không
        if hasattr(obj, 'invalid_cost') and obj.invalid_cost and hasattr(obj.invalid_cost, 'uuid'):
            return {
                'uuid': obj.invalid_cost.uuid,
                'ma_cpkhl': getattr(obj.invalid_cost, 'ma_cpkhl', ''),
                'ten_cpkhl': getattr(obj.invalid_cost, 'ten_cpkhl', '')
            }
        return None