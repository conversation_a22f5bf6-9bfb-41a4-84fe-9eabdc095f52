from rest_framework import serializers

from django_ledger.models import ChartOfAccountModel

class ChartOfAccountModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the Chart of Accounts model.
    """
    entity_slug = serializers.SerializerMethodField()
    account_count = serializers.SerializerMethodField()
    accounts_active_count = serializers.SerializerMethodField()
    accounts_locked_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ChartOfAccountModel
        fields = [
            'uuid',
            'slug',
            'name',
            'description',
            'entity',
            'entity_slug',
            'active',
            'account_count',
            'accounts_active_count',
            'accounts_locked_count',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'slug', 'entity_slug']

    def get_entity_slug(self, obj):
        return obj.entity_slug

    def get_account_count(self, obj):
        return getattr(obj, 'accountmodel_total__count', 0)

    def get_accounts_active_count(self, obj):
        return getattr(obj, 'accountmodel_active__count', 0)

    def get_accounts_locked_count(self, obj):
        return getattr(obj, 'accountmodel_locked__count', 0)
