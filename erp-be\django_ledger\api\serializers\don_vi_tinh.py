"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.
"""
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from django_ledger.models.don_vi_tinh import DonViTinhModel
from django_ledger.api.serializers.base import GlobalModelSerializer

class DonViTinhSerializer(GlobalModelSerializer):
    """
    Serializer for DonViTinhModel
    """
    class Meta:
        model = DonViTinhModel
        fields = [
            'uuid',
            'entity_model',
            'dvt',
            'ten_dvt',
            'ten_khac',
            'dvt2',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated']

    def validate_dvt(self, value):
        """Validates dvt is unique within the entity"""
        request = self.context.get('request')
        entity_slug = request.parser_context.get('kwargs').get('entity_slug')
        instance = getattr(self, 'instance', None)

        # Check if dvt exists for this entity
        qs = DonViTinhModel.objects.filter(
            entity__slug__exact=entity_slug,
            dvt=value
        )
        if instance:
            qs = qs.exclude(uuid=instance.uuid)

        if qs.exists():
            raise serializers.ValidationError(
                _("Mã đơn vị tính đã tồn tại trong entity này")
            )
        return value

    def validate_status(self, value):
        """Validates status is 0 or 1"""
        if value not in [0, 1]:
            raise serializers.ValidationError(
                _("Trạng thái không hợp lệ")
            )
        return value

    def validate(self, data):
        """Custom validation for the entire object"""
        if 'ten_dvt2' not in data or not data['ten_dvt2']:
            data['ten_dvt2'] = data.get('ten_dvt')
        return data
