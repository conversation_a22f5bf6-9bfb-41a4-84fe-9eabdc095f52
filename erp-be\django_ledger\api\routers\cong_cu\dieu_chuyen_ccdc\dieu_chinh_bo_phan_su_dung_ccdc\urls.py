"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

DieuChinhBoPhanSuDungCCDC (Department Usage Adjustment for Tools) model router implementation.
"""

from rest_framework.routers import DefaultRouter
from django_ledger.api.views.cong_cu.dieu_chuyen_ccdc import DieuChinhBoPhanSuDungCCDCViewSet

# Create router and register viewset
router = DefaultRouter()
router.register(r'', DieuChinhBoPhanSuDungCCDCViewSet, basename='dieu-chinh-bo-phan-su-dung-ccdc')

# URL patterns
urlpatterns = router.urls
