"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for HoaDonBanHangIPosModel.
"""

from django.utils.translation import gettext_lazy as _
from django_ledger.api.serializers.accounts import AccountModelSerializer
from django_ledger.api.serializers.ban_hang.hoa_don_ban_hang_ipos.chi_tiet_chiet_khau_hoa_don_ban_hang_ipos import (
    ChiTietChietKhauHoaDonBanHangIPosSerializer,
)
from django_ledger.api.serializers.ban_hang.hoa_don_ban_hang_ipos.chi_tiet_hoa_don_ban_hang_ipos import (
    ChiTietHoaDonBanHangIPosSerializer,
)
from django_ledger.api.serializers.ban_hang.hoa_don_ban_hang_ipos.chiet_khau_hoa_don_ban_hang_ipos import (
    ChietKhauHoaDonBanHangIPosSerializer,
)
from django_ledger.api.serializers.ban_hang.hoa_don_ban_hang_ipos.thong_tin_thanh_toan_hoa_don_ban_hang_ipos import (
    ThongTinThanhToanHoaDonBanHangIPosSerializer,
)
from django_ledger.api.serializers.chung_tu import ChungTuSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.danh_muc_nguon_don import (
    DanhMucNguonDonModelSerializer,
)
from django_ledger.api.serializers.nhan_vien import NhanVienModelSerializer
from django_ledger.api.serializers.tai_khoan_cua_hang import (
    TaiKhoanCuaHangModelSerializer,
)
from django_ledger.models import HoaDonBanHangIPosModel
from rest_framework import serializers


class HoaDonBanHangIPosSerializer(serializers.ModelSerializer):
    """
    Serializer for HoaDonBanHangIPosModel.
    """

    # Read-only fields for related objects
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    tk_data = serializers.SerializerMethodField(read_only=True)
    ma_tt_data = serializers.SerializerMethodField(read_only=True)
    ma_httt_data = serializers.SerializerMethodField(read_only=True)
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    ma_nk_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)
    ma_nvbh_data = serializers.SerializerMethodField(read_only=True)
    ma_ptvc_data = serializers.SerializerMethodField(read_only=True)
    ma_pttt_data = serializers.SerializerMethodField(read_only=True)
    ma_ptgh_data = serializers.SerializerMethodField(read_only=True)

    # Child data
    chi_tiet_hoa_don_data = serializers.SerializerMethodField(read_only=True)
    thong_tin_thanh_toan_data = serializers.SerializerMethodField(read_only=True)
    chiet_khau_hoa_don_data = serializers.SerializerMethodField(read_only=True)
    chi_tiet_chiet_khau_hoa_don_data = serializers.SerializerMethodField(read_only=True)

    # Write-only fields for creating/updating child records
    chi_tiet_hoa_don = serializers.ListField(required=False, write_only=True)
    thong_tin_thanh_toan = serializers.ListField(required=False, write_only=True)
    chiet_khau_hoa_don = serializers.ListField(required=False, write_only=True)
    chi_tiet_chiet_khau_hoa_don = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = HoaDonBanHangIPosModel
        fields = [
            "uuid",
            "entity_model",
            # Employee and customer information
            "ma_ngv",
            "ma_kh",
            "ma_kh_data",
            "ong_ba",
            "ten_kh_thue",
            "dia_chi",
            "ma_so_thue",
            "e_mail",
            # Account and payment information
            "tk",
            "tk_data",
            "ma_tt",
            "ma_tt_data",
            "ma_httt",
            "ma_httt_data",
            "loai_ck",
            # Document information
            "id",
            "unit_id",
            "unit_id_data",
            "i_so_ct",
            "ma_nk",
            "ma_nk_data",
            "so_ct",
            "ngay_ct",
            "ngay_lct",
            "so_ct2",
            # Currency information
            "ma_nt",
            "ma_nt_data",
            "ty_gia",
            # Status and shipping information
            "status",
            "transfer_yn",
            "ma_dc",
            "ma_gd",
            # Employee and method information
            "ma_nvbh",
            "ma_nvbh_data",
            "ma_ptvc",
            "ma_ptvc_data",
            "ma_pttt",
            "ma_pttt_data",
            "ma_ptgh",
            "ma_ptgh_data",
            # Electronic invoice information
            "ma_tthddt",
            "so_ct_hddt",
            "so_ct2_hddt",
            "ma_mau_ct_hddt",
            # iPOS information
            "ma_cuahang",
            "ma_cuahang_data",
            "ma_nguondon",
            "ma_nguondon_data",
            "pos_sale_id",
            "pos_t_tien_ck",
            "pos_t_tien_ship",
            # Total amount fields
            "t_tien_nt",
            "t_tien",
            "t_ck_nt",
            "t_ck",
            "t_gg_nt",
            "t_gg",
            "t_thue_nt",
            "t_thue",
            "t_tt_nt",
            "t_tt",
            "t_so_luong",
            # Other fields
            "dien_giai",
            "tran_no",
            "ly_do_huy",
            "ly_do",
            "so_bk",
            "ngay_bk",
            "ghi_chu",
            "tao_pt",
            "fixed_tax_yn",
            "xfile",
            # Child data
            "chi_tiet_hoa_don_data",
            "thong_tin_thanh_toan_data",
            "chiet_khau_hoa_don_data",
            "chi_tiet_chiet_khau_hoa_don_data",
            # Write-only child fields
            "chi_tiet_hoa_don",
            "thong_tin_thanh_toan",
            "chiet_khau_hoa_don",
            "chi_tiet_chiet_khau_hoa_don",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "entity_model",
            "entity_model_data",
            "ma_kh_data",
            "tk_data",
            "ma_tt_data",
            "ma_httt_data",
            "unit_id_data",
            "ma_nk_data",
            "so_ct_data",
            "so_ct2_data",
            "ma_nt_data",
            "ma_cuahang_data",
            "ma_nguondon_data",
            "ma_nvbh_data",
            "ma_ptvc_data",
            "ma_pttt_data",
            "ma_ptgh_data",
            "chi_tiet_hoa_don_data",
            "thong_tin_thanh_toan_data",
            "chiet_khau_hoa_don_data",
            "chi_tiet_chiet_khau_hoa_don_data",
            "created",
            "updated",
        ]

    def get_ma_kh_data(self, obj):
        """Get customer data."""
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_tk_data(self, obj):
        """Get account data."""
        if obj.tk:
            return AccountModelSerializer(obj.tk).data
        return None

    def get_ma_tt_data(self, obj):
        """Get payment status data."""
        if obj.ma_tt:
            return {
                "uuid": str(obj.ma_tt.uuid),
                "ma_tt": obj.ma_tt.ma_tt,
                "ten_tt": getattr(obj.ma_tt, "ten_tt", None),
            }
        return None

    def get_ma_httt_data(self, obj):
        """Get payment method data."""
        if obj.ma_httt:
            return {
                "uuid": str(obj.ma_httt.uuid),
                "ma_httt": obj.ma_httt.ma_httt,
                "ten_httt": getattr(obj.ma_httt, "ten_httt", None),
            }
        return None

    def get_unit_id_data(self, obj):
        """Get unit data."""
        if obj.unit_id:
            return {"uuid": str(obj.unit_id.uuid), "name": obj.unit_id.name}
        return None

    def get_ma_nk_data(self, obj):
        """Get journal data."""
        if obj.ma_nk:
            return {
                "uuid": str(obj.ma_nk.uuid),
                "ma_nk": obj.ma_nk.ma_nk,
                "ten_nk": getattr(obj.ma_nk, "ten_nk", None),
            }
        return None

    def get_so_ct_data(self, obj):
        """Get document reference data."""
        if obj.so_ct:
            return ChungTuSerializer(obj.so_ct).data
        return None

    def get_so_ct2_data(self, obj):
        """Get document reference 2 data."""
        if obj.so_ct2:
            return ChungTuSerializer(obj.so_ct2).data
        return None

    def get_ma_nt_data(self, obj):
        """Get currency data."""
        if obj.ma_nt:
            return {
                "uuid": str(obj.ma_nt.uuid),
                "ma_nt": obj.ma_nt.ma_nt,
                "ten_nt": getattr(obj.ma_nt, "ten_nt", None),
            }
        return None

    def get_ma_cuahang_data(self, obj):
        """Get store account data."""
        if obj.ma_cuahang:
            return TaiKhoanCuaHangModelSerializer(obj.ma_cuahang).data
        return None

    def get_ma_nguondon_data(self, obj):
        """Get order source data."""
        if obj.ma_nguondon:
            return DanhMucNguonDonModelSerializer(obj.ma_nguondon).data
        return None

    def get_ma_nvbh_data(self, obj):
        """Get sales employee data."""
        if obj.ma_nvbh:
            return NhanVienModelSerializer(obj.ma_nvbh).data
        return None

    def get_ma_ptvc_data(self, obj):
        """Get transportation method data."""
        if obj.ma_ptvc:
            return {
                "uuid": str(obj.ma_ptvc.uuid),
                "ma_ptvc": obj.ma_ptvc.ma_ptvc,
                "ten_ptvc": getattr(obj.ma_ptvc, "ten_ptvc", None),
            }
        return None

    def get_ma_pttt_data(self, obj):
        """Get payment method data."""
        if obj.ma_pttt:
            return {
                "uuid": str(obj.ma_pttt.uuid),
                "ma_pttt": obj.ma_pttt.ma_pttt,
                "ten_pttt": getattr(obj.ma_pttt, "ten_pttt", None),
            }
        return None

    def get_ma_ptgh_data(self, obj):
        """Get delivery method data."""
        if obj.ma_ptgh:
            return {
                "uuid": str(obj.ma_ptgh.uuid),
                "ma_ptgh": obj.ma_ptgh.ma_ptgh,
                "ten_ptgh": getattr(obj.ma_ptgh, "ten_ptgh", None),
            }
        return None

    def get_chi_tiet_hoa_don_data(self, obj):
        """Get invoice details data."""
        chi_tiet = obj.chi_tiet_hoa_don.all()
        return ChiTietHoaDonBanHangIPosSerializer(chi_tiet, many=True).data

    def get_thong_tin_thanh_toan_data(self, obj):
        """Get payment information data."""
        thong_tin = obj.thong_tin_thanh_toan.all()
        return ThongTinThanhToanHoaDonBanHangIPosSerializer(thong_tin, many=True).data

    def get_chiet_khau_hoa_don_data(self, obj):
        """Get discount data."""
        chiet_khau = obj.chiet_khau_hoa_don.all()
        return ChietKhauHoaDonBanHangIPosSerializer(chiet_khau, many=True).data

    def get_chi_tiet_chiet_khau_hoa_don_data(self, obj):
        """Get detailed discount data."""
        chi_tiet_chiet_khau = obj.chi_tiet_chiet_khau_hoa_don.all()
        return ChiTietChietKhauHoaDonBanHangIPosSerializer(
            chi_tiet_chiet_khau, many=True
        ).data
