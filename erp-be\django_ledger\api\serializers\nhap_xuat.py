"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for the NhapXuat (Import/Export) model.
"""

from rest_framework import serializers
from django_ledger.models.nhap_xuat import NhapXuatModel
from django_ledger.models.accounts import AccountModel
from django_ledger.api.serializers.account import AccountModelSerializer


class NhapXuatModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the NhapXuatModel (Import/Export) model.

    This serializer handles the conversion between NhapXuatModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - entity_model: Reference to the entity (ForeignKey to EntityModel)
    - ma_nx: Import/Export code
    - ten_nx: Primary name of the import/export type
    - ten_nx2: Secondary/alternative name (optional)
    - tk: Reference to the related account (ForeignKey to AccountModel)
    - tk_data: Full account data (nested AccountModelSerializer)
    - status: Status indicator ('0'=inactive, '1'=active)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    """
    # Add tk_data field to show full account information
    tk_data = serializers.SerializerMethodField(read_only=True)

    def get_tk_data(self, obj):
        """Method field for tk_data"""
        if obj.tk:
            return AccountModelSerializer(obj.tk).data
        return None

    class Meta:
        model = NhapXuatModel
        fields = ['uuid', 'entity_model', 'ma_nx', 'ten_nx', 'ten_nx2', 'tk', 'tk_data', 'status',
                  'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated', 'tk_data', 'entity_model']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_nx": "NX01",
                "ten_nx": "Nhập hàng từ nhà cung cấp",
                "ten_nx2": "Import from supplier",
                "tk": "815dde91-3f8a-4a27-8316-9e40859780ae",
                "tk_data": {
                    "uuid": "815dde91-3f8a-4a27-8316-9e40859780ae",
                    "code": "1561",
                    "name": "Hàng mua đang đi đường",
                    "role": "assets_ca_inventory",
                    "balance_type": "debit",
                    "active": True
                },
                "status": "1",
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z"
            }
        }
