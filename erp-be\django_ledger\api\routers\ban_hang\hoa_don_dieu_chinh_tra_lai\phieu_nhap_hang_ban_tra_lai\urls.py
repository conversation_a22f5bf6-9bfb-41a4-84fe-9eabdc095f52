"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for PhieuNhapHangBanTraLai (Customer Return Receipt) API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai import (
    PhieuNhapHangBanTraLaiViewSet,
    ChiTietPhieuNhapHangBanTraLaiViewSet
)

# Main router for PhieuNhapHangBanTraLai
router = DefaultRouter()
router.register('', PhieuNhapHangBanTraLaiViewSet, basename='phieu-nhap-hang-ban-tra-lai')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),

    # Nested routes for phieu_nhap_hang_ban_tra_lai
    path('<uuid:phieu_nhap_hang_ban_tra_lai_uuid>/', include([
        # ChiTietPhieuNhapHangBanTraLai routes
        path('chi-tiet/', ChiTietPhieuNhapHangBanTraLaiViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-tiet-phieu-nhap-hang-ban-tra-lai-list'),

        path('chi-tiet/<uuid:uuid>/', ChiTietPhieuNhapHangBanTraLaiViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-tiet-phieu-nhap-hang-ban-tra-lai-detail'),
    ])),
]
