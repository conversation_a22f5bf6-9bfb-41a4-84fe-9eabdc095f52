"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for Danh Muc CCDC (Tool Inventory Report) API endpoints.
"""

from django.urls import path
from django_ledger.api.views.cong_cu.kiem_ke_ccdc.danh_muc_ccdc import DanhMucCCDCViewSet

# URL patterns - Single endpoint for tool inventory report with filters as POST body data
urlpatterns = [
    # Tool Inventory Report endpoint - returns report directly with filter POST body data
    path("", DanhMucCCDCViewSet.as_view({"post": "get_report"}), name="danh-muc-ccdc-report"),
]
