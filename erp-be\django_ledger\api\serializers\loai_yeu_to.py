"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Dedicated Serializer for LoaiYeuTo model.
"""

from rest_framework import serializers

from django_ledger.api.serializers.base import GlobalModelSerializer
from django_ledger.models.loai_yeu_to import LoaiYeuToModel


class LoaiYeuToModelSerializer(GlobalModelSerializer):
    """
    Serializer class for LoaiYeuToModel that handles data serialization/deserialization.
    Inherits from GlobalModelSerializer for common fields and behaviors.
    """

    class Meta:
        model = LoaiYeuToModel
        fields = [
            'uuid',
            'entity_model',
            'ma_loai',
            'ten_loai',
            'ten_loai2',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated', 'entity_model']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "entity_model": "123e4567-e89b-12d3-a456-************",
                "ma_loai": "YT01",
                "ten_loai": "Yếu tố 1",
                "ten_loai2": "Factor 1",
                "status": "1",
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z"
            }
        }

    def validate_ma_loai(self, value):
        """
        Validate ma_loai field

        Parameters
        ----------
        value : str
            The ma_loai value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Mã loại yếu tố không được để trống')
        return value.strip()

    def validate_ten_loai(self, value):
        """
        Validate ten_loai field

        Parameters
        ----------
        value : str
            The ten_loai value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Tên loại yếu tố không được để trống')
        return value.strip()

    def create(self, validated_data):
        """
        Create a new LoaiYeuToModel instance

        Parameters
        ----------
        validated_data : dict
            The validated data

        Returns
        -------
        LoaiYeuToModel
            The created instance
        """
        # Create the instance directly
        instance = LoaiYeuToModel.objects.create(**validated_data)
        return instance
