"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietPhieuNhapDieuChinhGiaHangMua model.
"""
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from django_ledger.models import ChiTietPhieuNhapDieuChinhGiaHangMuaModel
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.warehouse import KhoHangModelSerializer
from django_ledger.api.serializers.accounts import AccountModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.tax import TaxModelSerializer
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.lo import LoModelSerializer
from django_ledger.api.serializers.vi_tri_kho_hang import ViTriKhoHangModelSerializer as ViTriModelSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.tien_do_thanh_toan import DotThanhToanModelSerializer
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import KheUocModelSerializer
from django_ledger.api.serializers.danh_muc import PhiSerializer, ChiPhiKhongHopLeSerializer

class ChiTietPhieuNhapDieuChinhGiaHangMuaSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiTietPhieuNhapDieuChinhGiaHangMua model.
    """
    # Read-only fields for related objects
    phieu_nhap_data = serializers.SerializerMethodField(read_only=True)
    ma_vt_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)
    ma_kho_data = serializers.SerializerMethodField(read_only=True)
    tk_vt_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_lo_data = serializers.SerializerMethodField(read_only=True)
    ma_vi_tri_data = serializers.SerializerMethodField(read_only=True)
    ma_thue_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_dtt_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_cp0_data = serializers.SerializerMethodField(read_only=True)
    entity_model_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ChiTietPhieuNhapDieuChinhGiaHangMuaModel
        fields = [
            'uuid',
            'phieu_nhap',
            'phieu_nhap_data',
            'ma_vt',
            'ma_vt_data',
            'dvt',
            'dvt_data',
            'ma_kho',
            'ma_kho_data',
            'ma_lo',
            'ma_lo_data',
            'ma_vi_tri',
            'ma_vi_tri_data',
            'ma_thue',
            'ma_thue_data',
            'tk_vt',
            'tk_vt_data',
            'tk_thue',
            'tk_thue_data',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_sp_data',
            'ma_cp0',
            'ma_cp0_data',
            'entity_model',
            'line',
            'lo_yn',
            'vi_tri_yn',
            'qc_yn',
            'id_hd4',
            'id_hd5',
            'id_hd7',
            'line_hd',
            'he_so',
            'so_luong',
            'gia_nt',
            'tien_nt',
            'thue_suat',
            'thue_nt',
            'thue',
            'gia',
            'tien',
            'ten_tk_vt',
            'ten_tk_thue',
            'ma_lsx',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'phieu_nhap_data',
            'ma_vt_data',
            'dvt_data',
            'ma_kho_data',
            'tk_vt_data',
            'tk_thue_data',
            'ma_bp_data',
            'ma_lo_data',
            'ma_vi_tri_data',
            'ma_thue_data',
            'ma_vv_data',
            'ma_hd_data',
            'ma_dtt_data',
            'ma_ku_data',
            'ma_phi_data',
            'ma_sp_data',
            'ma_cp0_data',
            'created',
            'updated'
        ]

    def get_phieu_nhap_data(self, obj):
        """
        Get parent phieu nhap data (minimal to avoid circular reference).
        """
        if obj.phieu_nhap:
            return {
                'uuid': str(obj.phieu_nhap.uuid),
                'i_so_ct': obj.phieu_nhap.i_so_ct,
                'ngay_ct': obj.phieu_nhap.ngay_ct,
                'dien_giai': obj.phieu_nhap.dien_giai
            }
        return None

    def get_ma_vt_data(self, obj):
        """
        Get material data.
        """
        if obj.ma_vt:
            return VatTuSerializer(obj.ma_vt).data
        return None

    def get_dvt_data(self, obj):
        """
        Get unit of measure data.
        """
        if obj.dvt:
            return DonViTinhSerializer(obj.dvt).data
        return None

    def get_ma_kho_data(self, obj):
        """
        Get warehouse data.
        """
        if obj.ma_kho:
            return KhoHangModelSerializer(obj.ma_kho).data
        return None

    def get_tk_vt_data(self, obj):
        """
        Get material account data.
        """
        if obj.tk_vt:
            return AccountModelSerializer(obj.tk_vt).data
        return None

    def get_tk_thue_data(self, obj):
        """
        Get tax account data.
        """
        if obj.tk_thue:
            return AccountModelSerializer(obj.tk_thue).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_lo_data(self, obj):
        """
        Get lot data.
        """
        if obj.ma_lo:
            return LoModelSerializer(obj.ma_lo).data
        return None

    def get_ma_vi_tri_data(self, obj):
        """
        Get location data.
        """
        if obj.ma_vi_tri:
            return ViTriModelSerializer(obj.ma_vi_tri).data
        return None

    def get_ma_thue_data(self, obj):
        """
        Get tax data.
        """
        if obj.ma_thue:
            return TaxModelSerializer(obj.ma_thue).data
        return None

    def get_ma_vv_data(self, obj):
        """
        Get case data.
        """
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_hd_data(self, obj):
        """
        Get contract data.
        """
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_dtt_data(self, obj):
        """
        Get payment batch data.
        """
        if obj.ma_dtt:
            return DotThanhToanModelSerializer(obj.ma_dtt).data
        return None

    def get_ma_ku_data(self, obj):
        """
        Get loan agreement data.
        """
        if obj.ma_ku:
            return KheUocModelSerializer(obj.ma_ku).data
        return None

    def get_ma_phi_data(self, obj):
        """
        Get fee data.
        """
        if obj.ma_phi:
            return PhiSerializer(obj.ma_phi).data
        return None

    def get_ma_sp_data(self, obj):
        """
        Get product data.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_cp0_data(self, obj):
        """
        Get invalid expense data.
        """
        if obj.ma_cp0:
            return ChiPhiKhongHopLeSerializer(obj.ma_cp0).data
        return None

    def validate(self, attrs):
        """
        Validate the serializer data.
        """
        # Check if this is an update or create operation
        is_update = self.instance is not None

        # Validate required fields for create operations
        if not is_update:
            required_fields = ['ma_vt', 'dvt', 'ma_kho', 'tk_vt', 'line', 'he_so', 'so_luong', 'ten_tk_vt']
            for field in required_fields:
                if field not in attrs:
                    raise serializers.ValidationError({
                        field: _('This field is required.')
                    })

        # Validate line number is positive
        if 'line' in attrs and attrs['line'] <= 0:
            raise serializers.ValidationError({
                'line': _('Line number must be positive.')
            })

        # Validate decimal fields are positive
        decimal_fields = ['he_so', 'so_luong', 'gia_nt', 'tien_nt', 'thue_suat', 'thue_nt', 'thue', 'gia', 'tien']
        for field in decimal_fields:
            if field in attrs and attrs[field] is not None and attrs[field] < 0:
                raise serializers.ValidationError({
                    field: _('This field must be positive.')
                })

        # Validate tax rate is between 0 and 100
        if 'thue_suat' in attrs and attrs['thue_suat'] is not None:
            if attrs['thue_suat'] < 0 or attrs['thue_suat'] > 100:
                raise serializers.ValidationError({
                    'thue_suat': _('Tax rate must be between 0 and 100.')
                })

        # Validate quantity is positive
        if 'so_luong' in attrs and attrs['so_luong'] is not None and attrs['so_luong'] <= 0:
            raise serializers.ValidationError({
                'so_luong': _('Quantity must be positive.')
            })

        # Validate conversion factor is positive
        if 'he_so' in attrs and attrs['he_so'] is not None and attrs['he_so'] <= 0:
            raise serializers.ValidationError({
                'he_so': _('Conversion factor must be positive.')
            })

        # Validate integer flags are 0 or 1
        flag_fields = ['lo_yn', 'vi_tri_yn', 'qc_yn']
        for field in flag_fields:
            if field in attrs and attrs[field] is not None:
                if attrs[field] not in [0, 1]:
                    raise serializers.ValidationError({
                        field: _('This field must be 0 or 1.')
                    })

        return attrs


