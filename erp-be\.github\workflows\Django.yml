name: Django CI

on:
  push:
    branches: [develop]
  pull_request:
    branches:
      - develop
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    # strategy:
    #   matrix:
    #     python-version: [ '3.8.10', '3.9.0', '3.10.0' ]

    steps:
      - uses: actions/checkout@v2

      # - name: Set up Python ${{ matrix.python-version }}
      - name: Set up Python 3.9
        uses: actions/setup-python@v2
        with:
          python-version: 3.9
          # python-version: ${{ matrix.python-version }}

      - name: Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
      - name: Run Tests
        run: |
          python manage.py test --noinput
