"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for KhaiBaoDoiTuongNhanPhanBoChiPhi model.
"""

from rest_framework import serializers

from django_ledger.models import KhaiBaoDoiTuongNhanPhanBoChiPhiModel
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.unit import EntityUnitModelSimpleSerializer
from django_ledger.api.serializers.gia_thanh.danh_muc.danh_muc_cong_doan import DanhMucCongDoanSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.gia_thanh.danh_muc.yeu_to.yeu_to import YeuToModelSerializer


class KhaiBaoDoiTuongNhanPhanBoChiPhiModelSerializer(serializers.ModelSerializer):
    """
    Serializer for KhaiBaoDoiTuongNhanPhanBoChiPhiModel.
    """
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    ma_yt_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_bp0_data = serializers.SerializerMethodField(read_only=True)

    def get_unit_id_data(self, obj):
        """
        Method field for unit_id_data that returns the full EntityUnitModel object data
        """
        if obj.unit_id:
            return EntityUnitModelSimpleSerializer(obj.unit_id).data
        return None

    def get_ma_yt_data(self, obj):
        """
        Method field for ma_yt_data that returns the full YeuToModel object data
        """
        if obj.ma_yt:
            return YeuToModelSerializer(obj.ma_yt).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Method field for ma_bp_data that returns the full DanhMucCongDoanModel object data
        """
        if obj.ma_bp:
            return DanhMucCongDoanSerializer(obj.ma_bp).data
        return None

    def get_ma_sp_data(self, obj):
        """
        Method field for ma_sp_data that returns the full VatTuModel object data
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_bp0_data(self, obj):
        """
        Method field for ma_bp0_data that returns the full DanhMucCongDoanModel object data
        """
        if obj.ma_bp0:
            return DanhMucCongDoanSerializer(obj.ma_bp0).data
        return None

    class Meta:
        model = KhaiBaoDoiTuongNhanPhanBoChiPhiModel
        fields = [
            'uuid',
            'entity_model',
            'unit_id',
            'unit_id_data',
            'ky',
            'nam',
            'ma_yt',
            'ma_yt_data',
            'ma_bp',
            'ma_bp_data',
            'ma_sp',
            'ma_sp_data',
            'ma_lsx',
            'ma_bp0',
            'ma_bp0_data',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated', 'entity_model']
        swagger_schema_fields = {
            'title': 'KhaiBaoDoiTuongNhanPhanBoChiPhi',
            'description': 'Khai báo đối tượng nhận phân bổ chi phí model serializer'
        }
