"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the URL patterns for the PhieuNhapKho API.
"""

from django.urls import path

from django_ledger.api.views.ton_kho.nhap_kho_noi_bo.phieu_nhap_kho import (
    ChiTietPhieuNhapKhoViewSet,
    PhieuNhapKhoViewSet,
)

urlpatterns = [
    # PhieuNhapKho URLs
    path(
        "",
        PhieuNhapKhoViewSet.as_view({"get": "list", "post": "create"}),
        name="phieu-nhap-kho-list",
    ),
    path(
        "<uuid:pk>/",
        PhieuNhapKhoViewSet.as_view(
            {"get": "retrieve", "put": "update", "delete": "destroy"}
        ),
        name="phieu-nhap-kho-detail",
    ),
    # ChiTietPhieuNhapKho URLs
    path(
        "<uuid:phieu_nhap_kho_pk>/chi-tiet/",
        ChiTietPhieuNhapKhoViewSet.as_view({"get": "list", "post": "create"}),
        name="chi-tiet-phieu-nhap-kho-list",
    ),
    path(
        "<uuid:phieu_nhap_kho_pk>/chi-tiet/<uuid:pk>/",
        ChiTietPhieuNhapKhoViewSet.as_view(
            {"get": "retrieve", "put": "update", "delete": "destroy"}
        ),
        name="chi-tiet-phieu-nhap-kho-detail",
    ),
]
