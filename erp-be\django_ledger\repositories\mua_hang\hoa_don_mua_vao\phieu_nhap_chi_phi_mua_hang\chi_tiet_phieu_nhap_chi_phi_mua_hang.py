"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiTietPhieuNhapChiPhiMuaHangRepository, which handles database operations
for the ChiTietPhieuNhapChiPhiMuaHangModel.
"""

from typing import Dict, Any, Optional, List, Union
from uuid import UUID

from django.db.models import QuerySet

from django_ledger.models.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import ChiTietPhieuNhapChiPhiMuaHangModel, PhieuNhapChiPhiMuaHangModel
from django_ledger.repositories.base import BaseRepository
from django_ledger.models import (
            VatTuModel, DonViTinhModel, KhoHangModel,
            AccountModel, BoPhanModel
        )

class ChiTietPhieuNhapChiPhiMuaHangRepository(BaseRepository):
    """
    Repository class for ChiTietPhieuNhapChiPhiMuaHangModel.
    Handles database operations for the model.
    """

    def __init__(self):
        super().__init__(model_class=ChiTietPhieuNhapChiPhiMuaHangModel)

    def get_queryset(self) -> QuerySet:
        """
        Returns the base queryset for ChiTietPhieuNhapChiPhiMuaHangModel.

        Returns
        -------
        QuerySet
            The base queryset for ChiTietPhieuNhapChiPhiMuaHangModel.
        """
        return self.model_class.objects.all()

    def get_by_id(self, uuid: Union[str, UUID]) -> Optional[ChiTietPhieuNhapChiPhiMuaHangModel]:
        """
        Retrieves a ChiTietPhieuNhapChiPhiMuaHangModel by its UUID.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietPhieuNhapChiPhiMuaHangModel to retrieve.

        Returns
        -------
        Optional[ChiTietPhieuNhapChiPhiMuaHangModel]
            The ChiTietPhieuNhapChiPhiMuaHangModel with the given UUID, or None if not found.
        """
        try:
            return self.model_class.objects.get(uuid=uuid)
        except self.model_class.DoesNotExist:
            return None

    def list_for_phieu_nhap(self, phieu_nhap_id: Union[str, UUID], **kwargs) -> QuerySet:
        """
        Lists ChiTietPhieuNhapChiPhiMuaHangModel instances for a specific purchase expense receipt.

        Parameters
        ----------
        phieu_nhap_id : Union[str, UUID]
            The UUID of the PhieuNhapChiPhiMuaHangModel.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of ChiTietPhieuNhapChiPhiMuaHangModel instances.
        """
        return self.model_class.objects.for_phieu_nhap(phieu_nhap_id=phieu_nhap_id).filter(**kwargs)

    def create(self, phieu_nhap_id: Union[str, UUID], data: Dict[str, Any]) -> ChiTietPhieuNhapChiPhiMuaHangModel:
        """
        Creates a new ChiTietPhieuNhapChiPhiMuaHangModel instance.

        Parameters
        ----------
        phieu_nhap_id : Union[str, UUID]
            The UUID of the PhieuNhapChiPhiMuaHangModel.
        data : Dict[str, Any]
            The data for the new ChiTietPhieuNhapChiPhiMuaHangModel.

        Returns
        -------
        ChiTietPhieuNhapChiPhiMuaHangModel
            The created ChiTietPhieuNhapChiPhiMuaHangModel instance.
        """
        # Get the PhieuNhapChiPhiMuaHangModel instance
        phieu_nhap = PhieuNhapChiPhiMuaHangModel.objects.get(uuid=phieu_nhap_id)

        # Convert UUIDs to model instances
        data = self.convert_uuids_to_model_instances(data)

        # Create the ChiTietPhieuNhapChiPhiMuaHangModel instance
        instance = self.model_class(phieu_nhap=phieu_nhap, **data)
        instance.save()

        return instance

    def update(self, uuid: Union[str, UUID], data: Dict[str, Any]) -> Optional[ChiTietPhieuNhapChiPhiMuaHangModel]:
        """
        Updates an existing ChiTietPhieuNhapChiPhiMuaHangModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietPhieuNhapChiPhiMuaHangModel to update.
        data : Dict[str, Any]
            The data to update the ChiTietPhieuNhapChiPhiMuaHangModel with.

        Returns
        -------
        Optional[ChiTietPhieuNhapChiPhiMuaHangModel]
            The updated ChiTietPhieuNhapChiPhiMuaHangModel instance, or None if not found.
        """
        instance = self.get_by_id(uuid=uuid)
        if instance:
            # Convert UUIDs to model instances
            data = self.convert_uuids_to_model_instances(data)

            for key, value in data.items():
                setattr(instance, key, value)
            instance.save()
            return instance
        return None

    def delete(self, uuid: Union[str, UUID]) -> bool:
        """
        Deletes a ChiTietPhieuNhapChiPhiMuaHangModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietPhieuNhapChiPhiMuaHangModel to delete.

        Returns
        -------
        bool
            True if the ChiTietPhieuNhapChiPhiMuaHangModel was deleted, False otherwise.
        """
        instance = self.get_by_id(uuid=uuid)
        if instance:
            instance.delete()
            return True
        return False

    def convert_uuids_to_model_instances(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Converts UUID strings in the data to model instances.

        Parameters
        ----------
        data : Dict[str, Any]
            The data containing UUID strings.

        Returns
        -------
        Dict[str, Any]
            The data with UUID strings converted to model instances.
        """


        # Convert ma_vt UUID to VatTuModel instance
        if 'ma_vt' in data and data['ma_vt']:
            try:
                data['ma_vt'] = VatTuModel.objects.get(uuid=data['ma_vt'])
            except VatTuModel.DoesNotExist:
                data['ma_vt'] = None

        # Convert dvt UUID to DonViTinhModel instance
        if 'dvt' in data and data['dvt']:
            try:
                data['dvt'] = DonViTinhModel.objects.get(uuid=data['dvt'])
            except DonViTinhModel.DoesNotExist:
                data['dvt'] = None

        # Convert ma_kho UUID to KhoHangModel instance
        if 'ma_kho' in data and data['ma_kho']:
            try:
                data['ma_kho'] = KhoHangModel.objects.get(uuid=data['ma_kho'])
            except KhoHangModel.DoesNotExist:
                data['ma_kho'] = None

        # Convert tk_vt UUID to AccountModel instance
        if 'tk_vt' in data and data['tk_vt']:
            try:
                data['tk_vt'] = AccountModel.objects.get(uuid=data['tk_vt'])
            except AccountModel.DoesNotExist:
                data['tk_vt'] = None

        # Convert ma_bp UUID to BoPhanModel instance
        if 'ma_bp' in data and data['ma_bp']:
            try:
                data['ma_bp'] = BoPhanModel.objects.get(uuid=data['ma_bp'])
            except BoPhanModel.DoesNotExist:
                data['ma_bp'] = None

        return data
