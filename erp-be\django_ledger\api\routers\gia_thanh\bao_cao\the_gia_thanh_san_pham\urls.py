"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for TheGiaThanhSanPham (Product Cost Card) API endpoints.
"""

from django.urls import path
from django_ledger.api.views.gia_thanh.bao_cao.the_gia_thanh_san_pham import TheGiaThanhSanPhamViewSet

# URL patterns - Single endpoint for product cost card report with filters as POST body data
urlpatterns = [
    # Product Cost Card Report endpoint - returns report directly with filter POST body data
    path("", TheGiaThanhSanPhamViewSet.as_view({"post": "get_report"}), name="the-gia-thanh-san-pham-report"),
]
