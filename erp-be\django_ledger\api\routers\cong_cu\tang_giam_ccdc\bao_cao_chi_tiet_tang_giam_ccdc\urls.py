"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for Bao Cao Chi Tiet Tang Giam CCDC (Detailed CCDC Increase/Decrease Report) API endpoints.
"""

from django.urls import path
from django_ledger.api.views.cong_cu.tang_giam_ccdc.bao_cao_chi_tiet_tang_giam_ccdc import BaoCaoChiTietTangGiamCCDCViewSet

# URL patterns - Single endpoint for CCDC increase/decrease report with filters as POST body data
urlpatterns = [
    # CCDC Increase/Decrease Report endpoint - returns report directly with filter POST body data
    path("", BaoCaoChiTietTangGiamCCDCViewSet.as_view({"post": "get_report"}), name="bao-cao-chi-tiet-tang-giam-ccdc-report"),
]
