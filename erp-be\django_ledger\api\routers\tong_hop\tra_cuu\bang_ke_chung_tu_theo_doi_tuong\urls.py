"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for Bang Ke Chung Tu Theo <PERSON> (Document Tracking by Object Report) API endpoints.
"""

from django.urls import path
from django_ledger.api.views.tong_hop.tra_cuu.bang_ke_chung_tu_theo_doi_tuong import (
    BangKeChungTuTheoDoiTuongViewSet,
)

# URL patterns - Support both GET and POST for document tracking report
urlpatterns = [
    # Document Tracking by Object Report endpoint - supports both GET (query params) and POST (body data)
    path(
        "",
        BangKeChungTuTheoDoiTuongViewSet.as_view(
            {
                "get": "list",  # GET with query parameters
                "post": "get_report",  # POST with body data
            }
        ),
        name="bang-ke-chung-tu-theo-doi-tuong-report",
    ),
    # Alternative POST endpoint for explicit report generation
    path(
        "get-report/",
        BangKeChungTuTheoDoiTuongViewSet.as_view({"post": "get_report"}),
        name="bang-ke-chung-tu-theo-doi-tuong-get-report",
    ),
]
