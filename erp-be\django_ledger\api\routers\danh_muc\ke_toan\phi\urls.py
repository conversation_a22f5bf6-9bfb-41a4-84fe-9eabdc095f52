"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for PhiModel API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.danh_muc import PhiViewSet

# Main router for PhiModel
router = DefaultRouter()
router.register('', PhiViewSet, basename='phi')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
