"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

DieuChinhGiaTriTSCD (Fixed Asset Value Adjustment) serializer implementation.
"""

from rest_framework import serializers

from django_ledger.models import DieuChinhGiaTriTSCDModel
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer 
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.danh_muc.tai_san_va_cong_cu.ly_do_tang_giam_tai_san_co_dinh import LyDoTangGiamTaiSanCoDinhSerializer


from django_ledger.api.serializers.tai_san.khai_bao_tang_giam_tscd.khai_bao_thong_tin_tai_san_co_dinh import KhaiBaoThongTinTaiSanCoDinhSerializer


class DieuChinhGiaTriTSCDModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the DieuChinhGiaTriTSCDModel.

    This serializer handles the conversion between DieuChinhGiaTriTSCDModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields
    - Adds additional fields with "_data" suffix that contain the complete nested object data
    - When deserializing, accepts UUIDs for reference fields
    """
    # Add nested serializers for foreign key fields
    ma_ts_data = KhaiBaoThongTinTaiSanCoDinhSerializer(source='ma_ts', read_only=True)
    ma_tg_ts_data = LyDoTangGiamTaiSanCoDinhSerializer(source='ma_tg_ts', read_only=True)
    ma_nt_data = NgoaiTeSerializer(source='ma_nt', read_only=True)

    class Meta:
        model = DieuChinhGiaTriTSCDModel
        fields = [
            'uuid',
            'entity_model',
            'ma_ts',
            'ma_ts_data',
            'ma_tg_ts',
            'ma_tg_ts_data',
            'loai_tg_ts',
            'ky',
            'nam',
            'so_ct',
            'ngay_ct',
            'ma_nt',
            'ma_nt_data',
            'ty_gia',
            'nguyen_gia_nt',
            'nguyen_gia',
            'gt_da_kh_nt',
            'gt_da_kh',
            'gt_cl_nt',
            'gt_cl',
            'so_ky_kh',
            'gt_kh_ky_nt',
            'gt_kh_ky',
            'gt_kh_ht_nt',
            'gt_kh_ht',
            'gt_kh_sdc_nt',
            'gt_kh_sdc',
            'dien_giai',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'ma_ts_data',
            'ma_tg_ts_data',
            'ma_nt_data',
            'created',
            'updated'
        ]


