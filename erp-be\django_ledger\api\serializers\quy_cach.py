"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for QuyCach model.
"""

from rest_framework import serializers

from django_ledger.models import QuyCachModel


class QuyCachModelSerializer(serializers.ModelSerializer):
    """
    Serializer for QuyCachModel.
    """

    class Meta:
        model = QuyCachModel
        fields = [
            'uuid',
            'entity_model',
            'id_specs',
            'id',
            'text',
            'code_specs',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated', 'entity_model']
        swagger_schema_fields = {
            'title': 'QuyCach',
            'description': 'Quy cách model serializer'
        }

    def validate_id_specs(self, value):
        """
        Validate id_specs field
        
        Parameters
        ----------
        value : int
            The id_specs value to validate

        Returns
        -------
        int
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if value is None:
            raise serializers.ValidationError('ID quy cách không được để trống')
        return value

    def validate_id(self, value):
        """
        Validate id field
        
        Parameters
        ----------
        value : int
            The id value to validate

        Returns
        -------
        int
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if value is None:
            raise serializers.ValidationError('ID không được để trống')
        return value

    def validate_text(self, value):
        """
        Validate text field
        
        Parameters
        ----------
        value : str
            The text value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Mô tả quy cách không được để trống')
        return value.strip()

    def validate_code_specs(self, value):
        """
        Validate code_specs field
        
        Parameters
        ----------
        value : str
            The code_specs value to validate

        Returns
        -------
        str
            The validated value

        Raises
        ------
        serializers.ValidationError
            If validation fails
        """
        if not value:
            raise serializers.ValidationError('Mã quy cách không được để trống')
        return value.strip()
