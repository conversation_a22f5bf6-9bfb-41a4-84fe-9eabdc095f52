"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for The TSCD (Fixed Asset Report) API endpoints.
"""

from django.urls import path
from django_ledger.api.views.tai_san.kiem_ke_tscd.the_tscd import TheTSCDViewSet

# URL patterns - Single endpoint for fixed asset report with filters as POST body data
urlpatterns = [
    # Fixed Asset Report endpoint - returns report directly with filter POST body data
    path("", TheTSCDViewSet.as_view({"post": "get_report"}), name="the-tscd-report"),
]
