"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for Bao Cao Chi Tiet Tang Giam TSCD (Detailed Fixed Asset Increase/Decrease Report) API endpoints.
"""

from django.urls import path
from django_ledger.api.views.tai_san.tang_giam_tscd.bao_cao_chi_tiet_tang_giam_tscd import BaoCaoChiTietTangGiamTSCDViewSet

# URL patterns - Single endpoint for detailed asset increase/decrease report with filters as POST body data
urlpatterns = [
    # Detailed Asset Increase/Decrease Report endpoint - returns report directly with filter POST body data
    path("", BaoCaoChiTietTangGiamTSCDViewSet.as_view({"post": "get_report"}), name="bao-cao-chi-tiet-tang-giam-tscd-report"),
]
