"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the TaiKhoanHoaDonDienTuSerializer, which handles serialization
for the TaiKhoanHoaDonDienTuModel.
"""

from rest_framework import serializers

from django_ledger.models.he_thong.ket_noi_hddt.tai_khoan_hoa_don_dien_tu import TaiKhoanHoaDonDienTuModel
from django_ledger.api.serializers.entity import EntityModelSerializer


class TaiKhoanHoaDonDienTuSerializer(serializers.ModelSerializer):
    """
    Serializer for TaiKhoanHoaDonDienTuModel.
    """
    entity_model = EntityModelSerializer(read_only=True)

    # Reference data fields
    ma_tthddt_data = serializers.SerializerMethodField()

    class Meta:
        model = TaiKhoanHoaDonDienTuModel
        fields = [
            'uuid',
            'entity_model',
            'ma_tkhddt',
            'ten_tkhddt',
            'ten_tkhddt2',
            'url1',
            'url2',
            'url3',
            'user1',
            'pass1',
            'user2',
            'pass2',
            'ma_nkhddt',
            'loai_hddt',
            'ma_tthddt',
            'ma_tthddt_data',
            'thong_tu',
            'ky_token_yn',
            'status',
            'created',
            'updated',
            'created_by',
            'updated_by'
        ]
        read_only_fields = ['uuid', 'entity_model', 'created', 'updated', 'created_by', 'updated_by']

    def get_ma_tthddt_data(self, obj):
        """
        Returns the permission data for the ma_tthddt field.
        """
        if obj.ma_tthddt:
            return {
                'uuid': str(obj.ma_tthddt.uuid),
                'ma_nkhddt': obj.ma_tthddt.ma_nkhddt,
                'ten_nkhddt': obj.ma_tthddt.ten_nkhddt,
                'mau_so': obj.ma_tthddt.mau_so,
                'ky_hieu': obj.ma_tthddt.ky_hieu,
                'loai_hd': obj.ma_tthddt.loai_hd
            }
        return None
