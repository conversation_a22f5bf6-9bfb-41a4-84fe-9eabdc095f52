"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Bao Cao Chi Tiet Tang Giam CCDC (Detailed CCDC Increase/Decrease Report) API.
"""

from rest_framework import serializers
from django.core.validators import MinValueValidator, MaxValueValidator


class BaoCaoChiTietTangGiamCCDCRequestSerializer(serializers.Serializer):
    """
    Serializer for validating CCDC increase/decrease report POST body data.
    Validates all POST body parameters from cURL request.
    """

    # Required parameters
    tu_ky = serializers.IntegerField(
        required=True,
        validators=[MinValueValidator(1), MaxValueValidator(12)],
        help_text="Từ kỳ báo cáo (1-12)",
    )
    tu_nam = serializers.IntegerField(
        required=True,
        validators=[MinValueValidator(1900), MaxValueValidator(9999)],
        help_text="Từ năm báo cáo",
    )
    den_ky = serializers.IntegerField(
        required=True,
        validators=[MinValueValidator(1), MaxValueValidator(12)],
        help_text="Đến kỳ báo cáo (1-12)",
    )
    den_nam = serializers.IntegerField(
        required=True,
        validators=[MinValueValidator(1900), MaxValueValidator(9999)],
        help_text="Đến năm báo cáo",
    )

    # Optional filter parameters (UUID or string for model references)
    ma_lcc = serializers.CharField(
        required=False, allow_blank=True, help_text="UUID hoặc mã loại công cụ filter"
    )
    ma_bp = serializers.CharField(
        required=False, allow_blank=True, help_text="UUID hoặc mã bộ phận filter"
    )
    nh_cc1 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="UUID hoặc mã nhóm công cụ 1 filter",
    )
    nh_cc2 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="UUID hoặc mã nhóm công cụ 2 filter",
    )
    nh_cc3 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="UUID hoặc mã nhóm công cụ 3 filter",
    )
    loai_tg_cc = serializers.CharField(
        required=False, allow_blank=True, help_text="Loại tăng giảm công cụ filter"
    )
    ma_unit = serializers.CharField(
        required=False, allow_blank=True, help_text="UUID của đơn vị filter"
    )
    mau_bc = serializers.IntegerField(
        required=False, default=20, help_text="Mẫu báo cáo"
    )
    data_analysis_struct = serializers.CharField(
        required=False, allow_blank=True, help_text="Cấu trúc phân tích dữ liệu"
    )

    def validate(self, data):
        """
        Validate that tu_ky/tu_nam is before or equal to den_ky/den_nam.
        """
        tu_ky = data.get('tu_ky')
        tu_nam = data.get('tu_nam')
        den_ky = data.get('den_ky')
        den_nam = data.get('den_nam')

        if tu_nam > den_nam or (tu_nam == den_nam and tu_ky > den_ky):
            raise serializers.ValidationError(
                "Từ kỳ/năm phải nhỏ hơn hoặc bằng đến kỳ/năm"
            )

        return data


class BaoCaoChiTietTangGiamCCDCResponseSerializer(serializers.Serializer):
    """
    Serializer for CCDC increase/decrease report response data.
    Defines the structure of each report record returned.
    """

    stt = serializers.IntegerField(help_text="Số thứ tự")
    ma_cc = serializers.CharField(help_text="Mã công cụ")
    ten_cc = serializers.CharField(help_text="Tên công cụ")
    ma_bp = serializers.CharField(help_text="Mã bộ phận", allow_null=True)
    ten_bp = serializers.CharField(help_text="Tên bộ phận", allow_null=True)
    ngay_mua = serializers.DateField(help_text="Ngày mua", allow_null=True)
    so_ky_kh = serializers.DecimalField(
        max_digits=10, decimal_places=2, help_text="Số kỳ khấu hao", allow_null=True
    )
    ma_lcc = serializers.CharField(help_text="Mã loại công cụ", allow_null=True)
    ten_lcc = serializers.CharField(help_text="Tên loại công cụ", allow_null=True)
    ma_tg_cc = serializers.CharField(help_text="Mã tăng giảm công cụ", allow_null=True)
    ten_tg_cc = serializers.CharField(help_text="Tên tăng giảm công cụ", allow_null=True)
    nguyen_gia = serializers.DecimalField(
        max_digits=15, decimal_places=2, help_text="Nguyên giá", allow_null=True
    )
    gt_da_kh = serializers.DecimalField(
        max_digits=15, decimal_places=2, help_text="Giá trị đã khấu hao", allow_null=True
    )
    gt_cl = serializers.DecimalField(
        max_digits=15, decimal_places=2, help_text="Giá trị còn lại", allow_null=True
    )
    nh_cc1 = serializers.CharField(help_text="Nhóm công cụ 1", allow_null=True)
    nh_cc2 = serializers.CharField(help_text="Nhóm công cụ 2", allow_null=True)

    class Meta:
        fields = [
            'stt',
            'ma_cc',
            'ten_cc',
            'ma_bp',
            'ten_bp',
            'ngay_mua',
            'so_ky_kh',
            'ma_lcc',
            'ten_lcc',
            'ma_tg_cc',
            'ten_tg_cc',
            'nguyen_gia',
            'gt_da_kh',
            'gt_cl',
            'nh_cc1',
            'nh_cc2',
        ]
