"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

KhaiBaoThoiPhanBoCCDC (Tool Allocation Time Declaration) model implementation.
"""

from uuid import uuid4

from django.db import models
from django.db.models import Q, QuerySet, Manager
from django.utils.translation import gettext_lazy as _

from django_ledger.models.mixins import CreateUpdateMixIn


class KhaiBaoThoiPhanBoCCDCModelQueryset(QuerySet):
    """
    A custom defined KhaiBaoThoiPhanBoCCDCModelQueryset that will act as an interface to handling the DB queries to the
    KhaiBaoThoiPhanBoCCDCModel.
    """

    def for_entity(self, entity_slug, user_model=None):
        """
        Fetches a QuerySet of KhaiBaoThoiPhanBoCCDCModels for a specific entity.

        Parameters
        __________
        entity_slug: str
            The entity slug to filter by.
        user_model: UserModel
            The user model to filter by.
        """
        qs = self.filter(entity_model__slug__exact=entity_slug)
        if user_model:
            qs = qs.filter(
                Q(entity_model__admin=user_model)
                | Q(entity_model__managers__in=[user_model])
            )
        return qs


class KhaiBaoThoiPhanBoCCDCModelManager(Manager):
    """
    A custom defined KhaiBaoThoiPhanBoCCDCModelManager that will act as an interface to handling the DB queries to the
    KhaiBaoThoiPhanBoCCDCModel.
    """

    def get_queryset(self):
        """
        Returns the custom KhaiBaoThoiPhanBoCCDCModelQueryset.
        """
        return KhaiBaoThoiPhanBoCCDCModelQueryset(self.model, using=self._db)

    def for_entity(self, entity_slug, user_model=None):
        """
        Returns KhaiBaoThoiPhanBoCCDCModel for a specific entity.

        Parameters
        ----------
        entity_slug: str
            The entity slug to filter by.
        user_model: UserModel
            The user model to filter by.

        Returns
        -------
        KhaiBaoThoiPhanBoCCDCModelQueryset
            A QuerySet of KhaiBaoThoiPhanBoCCDCModel with applied filters.
        """
        return self.get_queryset().for_entity(
            entity_slug=entity_slug, user_model=user_model
        )


class KhaiBaoThoiPhanBoCCDCModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the KhaiBaoThoiPhanBoCCDCModel database will inherit from.
    The KhaiBaoThoiPhanBoCCDCModel inherits functionality from the following MixIns:

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    __________
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().

    entity_model: EntityModel
        The EntityModel associated with this tool allocation time declaration.

    ma_cc: KhaiBaoThongTinCCDCModel
        The tool code, foreign key to KhaiBaoThongTinCCDCModel.

    ngay_kh1: date
        The allocation start date.
    """

    uuid = models.UUIDField(
        primary_key=True,
        default=uuid4,
        editable=False,
        verbose_name=_("UUID"),
        help_text=_("Unique identifier for the record"),
    )
    entity_model = models.ForeignKey(
        "django_ledger.EntityModel",
        on_delete=models.CASCADE,
        db_column="entity_model",
        verbose_name=_("Entity Model"),
        help_text=_("Entity that this tool allocation time declaration belongs to"),
    )
    ma_cc = models.ForeignKey(
        "django_ledger.KhaiBaoThongTinCCDCModel",
        on_delete=models.CASCADE,
        db_column="ma_cc",
        null=True,
        blank=True,
        verbose_name=_("Mã công cụ"),
        help_text=_("Tool code"),
        related_name="thoi_phan_bo_ccdc",
    )
    ngay_kh1 = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Ngày phân bổ"), help_text=_("Allocation start date")
    )

    objects = KhaiBaoThoiPhanBoCCDCModelManager.from_queryset(
        KhaiBaoThoiPhanBoCCDCModelQueryset
    )()

    class Meta:
        abstract = True
        verbose_name = _("Khai Báo Thời Phân Bổ CCDC")
        verbose_name_plural = _("Khai Báo Thời Phân Bổ CCDC")
        indexes = [
            models.Index(fields=["entity_model"]),
            models.Index(fields=["ma_cc"]),
        ]

    def __str__(self):
        return f"CCDC: {self.ma_cc} - Ngày phân bổ: {self.ngay_kh1}"


class KhaiBaoThoiPhanBoCCDCModel(KhaiBaoThoiPhanBoCCDCModelAbstract):
    """
    Base Tool Allocation Time Declaration Model Implementation
    """

    class Meta(KhaiBaoThoiPhanBoCCDCModelAbstract.Meta):
        abstract = False
        db_table = "khai_bao_thoi_phan_bo_ccdc"
