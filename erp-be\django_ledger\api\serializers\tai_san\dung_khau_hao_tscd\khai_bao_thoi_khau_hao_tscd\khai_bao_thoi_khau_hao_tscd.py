"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for KhaiBaoThoiKhauHaoTSCD (Fixed Asset Depreciation Suspension Declaration) model
"""

from rest_framework import serializers

from django_ledger.models import KhaiBaoThoiKhauHaoTSCDModel
from django_ledger.api.serializers.entity import EntityModelSerializer


class KhaiBaoThoiKhauHaoTSCDSerializer(serializers.ModelSerializer):
    """
    Serializer for KhaiBaoThoiKhauHaoTSCDModel.
    """
    entity_model = EntityModelSerializer(read_only=True)

    class Meta:
        model = KhaiBaoThoiKhauHaoTSCDModel
        fields = [
            'uuid',
            'entity_model',
            'ma_ts',
            'ten_ts',
            'ngay_kh1',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'entity_model', 'created', 'updated']
