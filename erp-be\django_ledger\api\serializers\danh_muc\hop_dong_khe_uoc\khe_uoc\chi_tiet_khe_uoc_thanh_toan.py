"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for ChiTietKheUocThanhToan (Loan Payment Detail) model.
"""

from rest_framework import serializers

from django_ledger.models.danh_muc.hop_dong_khe_uoc.khe_uoc.chi_tiet_khe_uoc_thanh_toan import ChiTietKheUocThanhToanModel
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import KheUocModelSerializer
from django_ledger.services.danh_muc.hop_dong_khe_uoc.khe_uoc.chi_tiet_khe_uoc_thanh_toan import ChiTietKheUocThanhToanService


class ChiTietKheUocThanhToanModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the ChiTietKheUocThanhToanModel
    """
    ma_ku_data = KheUocModelSerializer(source='ma_ku', read_only=True)
    
    class Meta:
        model = ChiTietKheUocThanhToanModel
        fields = [
            'uuid', 'ma_ku', 'ma_ku_data',
            'line', 'ngay_tt', 'tt', 'tt_nt', 'ghi_chu',
            'created', 'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated']
    
    def create(self, validated_data):
        """
        Create a new loan payment detail
        """
        entity_slug = self.context.get('entity_slug')
        user_model = self.context.get('request').user
        
        service = ChiTietKheUocThanhToanService(
            entity_slug=entity_slug,
            user_model=user_model
        )
        
        return service.create(validated_data)
    
    def update(self, instance, validated_data):
        """
        Update an existing loan payment detail
        """
        entity_slug = self.context.get('entity_slug')
        user_model = self.context.get('request').user
        
        service = ChiTietKheUocThanhToanService(
            entity_slug=entity_slug,
            user_model=user_model
        )
        
        return service.update(instance.uuid, validated_data)
