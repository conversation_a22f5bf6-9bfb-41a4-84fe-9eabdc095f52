"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Customized by TTMI Office.
"""

from rest_framework import serializers

from django_ledger.api.serializers.CapNhatDieuKienThanhToan.dieu_kien_thanh_toan import DieuKienThanhToanModelSerializer
from django_ledger.api.serializers.erp import CustomerModelSerializer
from django_ledger.models import ChiTietDieuKienThanhToanModel


class ChiTietDieuKienThanhToanModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the ChiTietDieuKienThanhToanModel
    """
    dieu_kien_thanh_toan_data = DieuKienThanhToanModelSerializer(source='dieu_kien_thanh_toan', read_only=True)
    ma_kh_data = CustomerModelSerializer(source='ma_kh', read_only=True)

    class Meta:
        model = ChiTietDieuKienThanhToanModel
        fields = [
            'uuid',
            'entity_model',
            'dieu_kien_thanh_toan',
            'dieu_kien_thanh_toan_data',
            'ma_kh',
            'ma_kh_data',
            'ma_dk',
            'line',
            'ngay_gom_hd1',
            'ngay_gom_hd2',
            'created_by',
            'updated_by',
            'created',
            'updated',
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'created',
            'updated',
        ]


class ChiTietDieuKienThanhToanModelCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating ChiTietDieuKienThanhToanModel
    """
    class Meta:
        model = ChiTietDieuKienThanhToanModel
        fields = [
            'dieu_kien_thanh_toan',
            'ma_kh',
            'ma_dk',
            'line',
            'ngay_gom_hd1',
            'ngay_gom_hd2',
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Make fields optional for PATCH requests
        request = self.context.get('request')
        if request and request.method == 'PATCH':
            for field in self.fields:
                self.fields[field].required = False
