"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Quy Doi Don Vi <PERSON> (Common Unit Conversion) module.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.quy_doi_don_vi_tinh_chung import QuyDoiDonViTinhChungViewSet

# Main router for QuyDoiDonViTinhChung
router = DefaultRouter()
router.register('', QuyDoiDonViTinhChungViewSet, basename='quy-doi-don-vi-tinh-chung')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
