"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Finance-related models
"""

from rest_framework import serializers
from django_ledger.api.serializers.base import GlobalModelSerializer, BaseModelSerializer
from django_ledger.models.utils import lazy_loader
from django_ledger.models import (
    PhuongThucThanhToanModel,
    ThueSuatThueGTGTModel, NhomPhiModel, DotThanhToanModel
)




class PhuongThucThanhToanModelSerializer(BaseModelSerializer):
    """
    Serializer for the PhuongThucThanhToanModel (Payment Method) model.

    This serializer handles the conversion between PhuongThucThanhToanModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - entity_model: The entity that owns this payment method
    - ma_pttt: Unique code for the payment method
    - ten_pttt: Primary name of the payment method
    - ten_pttt2: Secondary/alternative name (optional)
    - ma_ph_thuc_hddt: Electronic invoice code
    - status: Status indicator ('0'=inactive, '1'=active)
    - created_at: Timestamp of creation (read-only)
    - updated_at: Timestamp of last update (read-only)
    """
    class Meta:
        model = PhuongThucThanhToanModel
        fields = ['uuid', 'entity_model', 'ma_pttt', 'ten_pttt', 'ten_pttt2', 'ma_ph_thuc_hddt', 'status',
                  'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "entity_model": "36d84c13-a4f8-4f13-bd01-fbd0b192cf11",
                "ma_pttt": "TM",
                "ten_pttt": "Tiền mặt",
                "ten_pttt2": "Cash",
                "ma_ph_thuc_hddt": "TM",
                "status": "1",
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z"
            }
        }


class ThueSuatThueGTGTModelSerializer(GlobalModelSerializer):
    class Meta:
        model = ThueSuatThueGTGTModel
        fields = ['uuid', 'ma_thue', 'ten_thue', 'ten_khac', 'thue_suat',
                  'thue_suat_hddt', 'nhom_thue', 'tk_thue_dau_ra',
                  'tk_thue_dau_vao', 'trang_thai',
                  'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated']





class NhomPhiModelSerializer(GlobalModelSerializer):
    """
    Serializer for the NhomPhiModel (Fee Group) model.

    This serializer handles the conversion between NhomPhiModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - ma_nhom: Unique code for the fee group
    - ten_phan_nhom: Primary name of the fee group
    - ten_2: Secondary/alternative name (optional)
    - trang_thai: Status indicator (integer)
    - loai_nhom: Group type indicator (integer)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    """
    class Meta:
        model = NhomPhiModel
        fields = ['uuid', 'ma_nhom', 'ten_phan_nhom', 'ten_2',
                  'trang_thai', 'loai_nhom',
                  'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_nhom": "NHOMPHI001",
                "ten_phan_nhom": "Nhóm phí vận chuyển",
                "ten_2": "Transport Fee Group",
                "trang_thai": 1,
                "loai_nhom": 1,
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z"
            }
        }
class DotThanhToanModelSerializer(GlobalModelSerializer):
    """
    Serializer for the DotThanhToanModel (Payment Installment) model.

    This serializer handles the conversion between DotThanhToanModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - entity_model: The entity that owns this payment installment
    - ma_dtt: Unique code for the payment installment
    - ten_dtt: Primary name of the payment installment
    - ten_dtt2: Secondary/alternative name (optional)
    - stt: Order number of the payment installment
    - status: Status indicator ('0'=inactive, '1'=active)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    - created_by: Username of creator
    - updated_by: Username of last updater
    """
    # Make entity_model read-only as it will be set from the URL parameters
    entity_model = serializers.PrimaryKeyRelatedField(read_only=True)

    class Meta:
        model = DotThanhToanModel
        fields = ['uuid', 'entity_model', 'ma_dtt', 'ten_dtt', 'ten_dtt2', 'stt', 'status',
                  'created', 'updated', 'created_by', 'updated_by']
        read_only_fields = ['uuid', 'created', 'updated']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "entity_model": "36d84c13-a4f8-4f13-bd01-fbd0b192cf11",
                "ma_dtt": "DTT01",
                "ten_dtt": "Đợt 1",
                "ten_dtt2": "First Installment",
                "stt": 1.00,
                "status": "1",
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z",
                "created_by": "admin",
                "updated_by": "admin"
            }
        }
