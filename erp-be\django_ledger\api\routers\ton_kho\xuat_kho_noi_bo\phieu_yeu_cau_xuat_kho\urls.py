"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuYeuCauXuatKho (Warehouse Export Request) router implementation.
"""

from django.urls import path

from django_ledger.api.views.ton_kho.xuat_kho_noi_bo.phieu_yeu_cau_xuat_kho import PhieuYeuCauXuatKhoViewSet, ChiTietPhieuYeuCauXuatKhoViewSet

urlpatterns = [
    # PhieuYeuCauXuatKho routes
    path('', PhieuYeuCauXuatKhoViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='phieu-yeu-cau-xuat-kho-list'),

    path('<uuid:pk>/', PhieuYeuCauXuatKhoViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='phieu-yeu-cau-xuat-kho-detail'),

    # Nested routes for ChiTietPhieuYeuCauXuatKho
    path('<uuid:phieu_yeu_cau_xuat_kho_uuid>/chi-tiet/', ChiTietPhieuYeuCauXuatKhoViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='chi-tiet-phieu-yeu-cau-xuat-kho-list'),

    path('<uuid:phieu_yeu_cau_xuat_kho_uuid>/chi-tiet/<uuid:uuid>/', ChiTietPhieuYeuCauXuatKhoViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='chi-tiet-phieu-yeu-cau-xuat-kho-detail'),
]
