"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ViewSet for ButToanDieuChinhGiamCongNo (Debt Reduction Adjustment) model.
"""

from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import status, permissions
from rest_framework.response import Response

from django_ledger.api.views.common import ERPPagination
from django_ledger.models.ban_hang.dieu_chinh_can_tru_cong_no.but_toan_dieu_chinh_giam_cong_no import ButToanDieuChinhGiamCongNoModel
from django_ledger.api.serializers.ban_hang.dieu_chinh_can_tru_cong_no.but_toan_dieu_chinh_giam_cong_no import ButToanDieuChinhGiamCongNoSerializer
from django_ledger.services.ban_hang.dieu_chinh_can_tru_cong_no.but_toan_dieu_chinh_giam_cong_no import ButToanDieuChinhG<PERSON><PERSON>ongNoService
from django_ledger.api.viewsets import EntityRelatedViewSet


@extend_schema_view(
    list=extend_schema(tags=["ButToanDieuChinhGiamCongNo"]),
    create=extend_schema(tags=["ButToanDieuChinhGiamCongNo"]),
    retrieve=extend_schema(tags=["ButToanDieuChinhGiamCongNo"]),
    update=extend_schema(tags=["ButToanDieuChinhGiamCongNo"]),
    partial_update=extend_schema(tags=["ButToanDieuChinhGiamCongNo"]),
    destroy=extend_schema(tags=["ButToanDieuChinhGiamCongNo"])
)
class ButToanDieuChinhGiamCongNoViewSet(EntityRelatedViewSet):
    """
    ViewSet for ButToanDieuChinhGiamCongNo model.
    """
    queryset = ButToanDieuChinhGiamCongNoModel.objects.all()
    serializer_class = ButToanDieuChinhGiamCongNoSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination
    lookup_field = 'uuid'

    def __init__(self, *args, **kwargs):
        """
        Initialize the ViewSet.

        Parameters
        ----------
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments
        """
        super().__init__(*args, **kwargs)
        self.service = ButToanDieuChinhGiamCongNoService()

    def get_queryset(self):
        """
        Get the queryset for the viewset.

        Returns
        -------
        QuerySet
            The queryset for the viewset.
        """
        entity_slug = self.kwargs.get('entity_slug')
        if entity_slug:
            self.service.entity_slug = entity_slug
            self.service.user_model = self.request.user
            return self.service.get_queryset()
        return self.queryset

    def list(self, request, *args, **kwargs):
        """
        List all ButToanDieuChinhGiamCongNo instances.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Additional arguments.
        **kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        entity_slug = self.kwargs.get('entity_slug')
        self.service.entity_slug = entity_slug
        self.service.user_model = self.request.user

        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        serializer = self.get_serializer(page, many=True)
        return self.get_paginated_response(serializer.data)

    def retrieve(self, request, *args, **kwargs):
        """
        Retrieve a ButToanDieuChinhGiamCongNo instance.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Additional arguments.
        **kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        entity_slug = self.kwargs.get('entity_slug')
        uuid = self.kwargs.get('uuid')
        self.service.entity_slug = entity_slug
        self.service.user_model = self.request.user
        instance = self.service.get(entity_slug=entity_slug, uuid=uuid)
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        """
        Create a new ButToanDieuChinhGiamCongNo instance.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Additional arguments.
        **kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        entity_slug = self.kwargs.get('entity_slug')
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Extract child data if present
        validated_data = serializer.validated_data
        child_data = validated_data.pop('chitiet_data', None)

        # Create the instance
        self.service.entity_slug = entity_slug
        self.service.user_model = self.request.user
        instance = self.service.create(
            entity_slug=entity_slug,
            data=validated_data,
            child_data=child_data
        )

        # Return the serialized instance
        serializer = self.get_serializer(instance)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def update(self, request, *args, **kwargs):
        """
        Update a ButToanDieuChinhGiamCongNo instance.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Additional arguments.
        **kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        entity_slug = self.kwargs.get('entity_slug')
        uuid = self.kwargs.get('uuid')

        # Get the instance
        self.service.entity_slug = entity_slug
        self.service.user_model = self.request.user
        instance = self.service.get(entity_slug=entity_slug, uuid=uuid)

        # Validate the data
        serializer = self.get_serializer(instance, data=request.data, partial=kwargs.get('partial', False))
        serializer.is_valid(raise_exception=True)

        # Extract child data if present
        validated_data = serializer.validated_data
        child_data = validated_data.pop('chitiet_data', None)

        # Update the instance
        instance = self.service.update(
            uuid=uuid,
            data=validated_data,
            child_data=child_data
        )

        # Return the serialized instance
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def partial_update(self, request, *args, **kwargs):
        """
        Partially update a ButToanDieuChinhGiamCongNo instance.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Additional arguments.
        **kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        """
        Delete a ButToanDieuChinhGiamCongNo instance.

        Parameters
        ----------
        request : Request
            The request object.
        *args : tuple
            Additional arguments.
        **kwargs : dict
            Additional keyword arguments.

        Returns
        -------
        Response
            The response object.
        """
        entity_slug = self.kwargs.get('entity_slug')
        uuid = self.kwargs.get('uuid')

        # Delete the instance
        self.service.entity_slug = entity_slug
        self.service.user_model = self.request.user
        self.service.delete(uuid=uuid)

        return Response(status=status.HTTP_204_NO_CONTENT)


