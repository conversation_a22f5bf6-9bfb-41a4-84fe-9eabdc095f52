from rest_framework import serializers
from django_ledger.models import QuocGiaModel

class QuocGiaModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the QuocGiaModel (Country) model.

    This serializer handles the conversion between QuocGiaModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Fields:
    - uuid: Unique identifier (read-only)
    - ma_qg: Country code (e.g., US, VN, JP)
    - ten_qg: Primary name of the country
    - ten_qg2: Secondary/alternative name (optional)
    - status: Status indicator ('0'=inactive, '1'=active)
    - created: Timestamp of creation (read-only)
    - updated: Timestamp of last update (read-only)
    """

    class Meta:
        model = QuocGiaModel
        fields = ['uuid', 'ma_qg', 'ten_qg', 'ten_qg2', 'status','entity_model',
                  'created', 'updated']
        read_only_fields = ['uuid', 'created', 'updated']

        # Example response for Swagger documentation
        swagger_schema_fields = {
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "ma_qg": "VN",
                "ten_qg": "Việt Nam",
                "ten_qg2": "Vietnam",
                "status": "1",
                "created": "2023-04-21T10:30:00Z",
                "updated": "2023-04-21T10:30:00Z"
            }
        }
