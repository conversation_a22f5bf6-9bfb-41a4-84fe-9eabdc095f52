"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for PhiModel.
"""
from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models.danh_muc import PhiModel
from django_ledger.api.serializers.group import GroupModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer


class PhiSerializer(serializers.ModelSerializer):
    """
    Serializer for PhiModel.
    """
    # Read-only fields for related objects
    nhom_phi_1_data = serializers.SerializerMethodField(read_only=True)
    nhom_phi_2_data = serializers.SerializerMethodField(read_only=True)
    nhom_phi_3_data = serializers.SerializerMethodField(read_only=True)
    bo_phan_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = PhiModel
        fields = [
            'uuid',
            'entity_model',
            'ma_phi',
            'ten_phi',
            'ten_khac',
            'nhom_phi_1',
            'nhom_phi_1_data',
            'nhom_phi_2',
            'nhom_phi_2_data',
            'nhom_phi_3',
            'nhom_phi_3_data',
            'bo_phan',
            'bo_phan_data',
            'trang_thai',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'nhom_phi_1_data',
            'nhom_phi_2_data',
            'nhom_phi_3_data',
            'bo_phan_data',
            'created',
            'updated'
        ]

    def get_nhom_phi_1_data(self, obj):
        """
        Get primary fee group data.
        """
        if obj.nhom_phi_1:
            return GroupModelSerializer(obj.nhom_phi_1).data
        return None

    def get_nhom_phi_2_data(self, obj):
        """
        Get secondary fee group data.
        """
        if obj.nhom_phi_2:
            return GroupModelSerializer(obj.nhom_phi_2).data
        return None

    def get_nhom_phi_3_data(self, obj):
        """
        Get tertiary fee group data.
        """
        if obj.nhom_phi_3:
            return GroupModelSerializer(obj.nhom_phi_3).data
        return None

    def get_bo_phan_data(self, obj):
        """
        Get department data.
        """
        if obj.bo_phan:
            return BoPhanModelSerializer(obj.bo_phan).data
        return None

    def validate_ma_phi(self, value):
        """
        Validate fee code.
        """
        if not value or len(value.strip()) == 0:
            raise serializers.ValidationError(_('Fee code cannot be empty'))

        if len(value) > 50:
            raise serializers.ValidationError(_('Fee code must not exceed 50 characters'))

        return value.strip()

    def validate_ten_phi(self, value):
        """
        Validate fee name.
        """
        if not value or len(value.strip()) == 0:
            raise serializers.ValidationError(_('Fee name cannot be empty'))

        if len(value) > 100:
            raise serializers.ValidationError(_('Fee name must not exceed 100 characters'))

        return value.strip()

    def validate_ten_khac(self, value):
        """
        Validate alternative name.
        """
        if value is not None:
            if len(value) > 100:
                raise serializers.ValidationError(_('Alternative name must not exceed 100 characters'))
            return value.strip() if value.strip() else None
        return value

    def validate_trang_thai(self, value):
        """
        Validate status.
        """
        if not isinstance(value, int):
            raise serializers.ValidationError(_('Status must be an integer'))

        if value not in [0, 1]:
            raise serializers.ValidationError(_('Status must be 0 (inactive) or 1 (active)'))

        return value

    def validate(self, attrs):
        """
        Validate the entire object.
        """
        # Check if primary group is required
        if 'nhom_phi_1' not in attrs or not attrs['nhom_phi_1']:
            raise serializers.ValidationError({
                'nhom_phi_1': _('Primary fee group is required')
            })

        # Validate group hierarchy (if nhom_phi_3 is set, nhom_phi_2 must also be set)
        if attrs.get('nhom_phi_3') and not attrs.get('nhom_phi_2'):
            raise serializers.ValidationError({
                'nhom_phi_2': _('Secondary fee group is required when tertiary group is specified')
            })

        return attrs
