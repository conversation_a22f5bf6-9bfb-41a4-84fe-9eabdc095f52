"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL Configuration for Khai Bao Su Dung TKHDDT (Electronic Invoice Account Usage Declaration) API.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.he_thong.ket_noi_hddt.khai_bao_su_dung_tkhddt import KhaiBaoSuDungTKHDDTViewSet

router = DefaultRouter()
router.register('', KhaiBaoSuDungTKHDDTViewSet, basename='khai-bao-su-dung-tkhddt')

urlpatterns = [
    path('', include(router.urls))
]
