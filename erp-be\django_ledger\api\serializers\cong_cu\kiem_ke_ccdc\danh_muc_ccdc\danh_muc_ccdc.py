"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Danh Muc CCDC (Tool Inventory Report) API.
"""

from django.core.validators import MaxValueValidator, MinValueValidator
from rest_framework import serializers


class DanhMucCCDCRequestSerializer(serializers.Serializer):
    """
    Serializer for validating tool inventory report POST body data.
    Validates all POST body parameters from cURL request.
    """

    # Filter parameters based on cURL request
    den_ngay = serializers.DateField(
        required=False,
        help_text="Ngày kết thúc filter (YYYY-MM-DD format)"
    )
    loai = serializers.CharField(
        required=False,
        allow_blank=True,
        default="*",
        help_text="Loại công cụ filter"
    )
    ma_lcc = serializers.Char<PERSON>ield(
        required=False,
        allow_blank=True,
        help_text="UUID hoặc mã loại công cụ filter"
    )
    ma_bp = serializers.Cha<PERSON><PERSON><PERSON>(
        required=False,
        allow_blank=True,
        help_text="UUID hoặc mã bộ phận filter"
    )
    nh_cc1 = serializers.Char<PERSON>ield(
        required=False,
        allow_blank=True,
        help_text="UUID hoặc mã nhóm công cụ 1 filter"
    )
    nh_cc2 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="UUID hoặc mã nhóm công cụ 2 filter"
    )
    nh_cc3 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="UUID hoặc mã nhóm công cụ 3 filter"
    )
    ma_unit = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="UUID hoặc mã đơn vị filter"
    )
    mau_bc = serializers.IntegerField(
        required=False,
        default=20,
        help_text="Mẫu báo cáo"
    )
    data_analysis_struct = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Cấu trúc phân tích dữ liệu"
    )


class DanhMucCCDCResponseSerializer(serializers.Serializer):
    """
    Serializer for tool inventory report response data.
    Defines the structure of each report item returned.
    """

    stt = serializers.IntegerField(
        help_text="Số thứ tự"
    )
    ma_cc = serializers.CharField(
        help_text="Mã công cụ"
    )
    ma_bp = serializers.CharField(
        help_text="Mã bộ phận"
    )
    nguyen_gia = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Nguyên giá"
    )
    gt_da_kh_nt0 = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Giá trị đã khấu hao ngoại tệ đầu kỳ"
    )
    gt_da_kh0 = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Giá trị đã khấu hao đầu kỳ"
    )
    gt_cl = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Giá trị còn lại"
    )
    gt_kh_ky = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Giá trị khấu hao kỳ này"
    )
    gt_da_kh_dk = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Giá trị đã khấu hao đến kỳ"
    )
    gt_da_kh = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Giá trị đã khấu hao"
    )
    ma_giam_cc = serializers.CharField(
        help_text="Mã giảm công cụ"
    )
    gt_cl_ck = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Giá trị còn lại cuối kỳ"
    )
    ten_cc = serializers.CharField(
        help_text="Tên công cụ"
    )
    ngay_mua = serializers.CharField(
        help_text="Ngày mua"
    )
    ngay_kh0 = serializers.CharField(
        help_text="Ngày bắt đầu khấu hao"
    )
    ngay_giam = serializers.CharField(
        help_text="Ngày giảm"
    )
    so_ky_kh = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Số kỳ khấu hao"
    )
    ngay_ct = serializers.CharField(
        help_text="Ngày chứng từ"
    )
    so_ct = serializers.CharField(
        help_text="Số chứng từ"
    )
    tk_cc = serializers.CharField(
        help_text="Tài khoản công cụ"
    )
    tk_kh = serializers.CharField(
        help_text="Tài khoản khấu hao"
    )
    tk_cp = serializers.CharField(
        help_text="Tài khoản chi phí"
    )
    nh_cc1 = serializers.CharField(
        help_text="Nhóm công cụ 1"
    )
    nh_cc2 = serializers.CharField(
        help_text="Nhóm công cụ 2"
    )
    nh_cc3 = serializers.CharField(
        help_text="Nhóm công cụ 3"
    )
    unit_id = serializers.CharField(
        help_text="ID đơn vị"
    )
    ma_unit = serializers.CharField(
        help_text="Mã đơn vị"
    )
