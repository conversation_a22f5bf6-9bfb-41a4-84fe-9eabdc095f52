"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL configuration for DotThanhToan API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.danh_muc.hop_dong_khe_uoc.dot_thanh_toan import DotThanhToanViewSet

# Main router for DotThanhToan
router = DefaultRouter()
router.register('', DotThanhToanViewSet, basename='dot-thanh-toan')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
