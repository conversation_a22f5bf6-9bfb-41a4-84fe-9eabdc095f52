"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Journal Entry Serializers Module
"""
from rest_framework import serializers

from django_ledger.models import JournalEntryModel, TransactionModel
from django_ledger.services.provider import service_provider


class TransactionModelSerializer(serializers.ModelSerializer):
    """Serializer for transactions."""
    
    class Meta:
        model = TransactionModel
        fields = [
            'account',
            'tx_type',
            'amount',
            'description'
        ]

class JournalEntrySerializer(serializers.ModelSerializer):
    """Serializer for journal entries."""
    transactions = TransactionModelSerializer(many=True, required=False)
    
    class Meta:
        model = JournalEntryModel
        fields = [
            'uuid',
            'ledger',
            'description',
            'timestamp',
            'activity',
            'origin',
            'posted',
            'transactions'
        ]
        read_only_fields = ['uuid', 'posted']

    def create(self, validated_data):
        """Create journal entry with transactions."""
        transactions_data = validated_data.pop('transactions', [])
        entity_slug = self.context['entity_slug']
        
        # Get service factory
        factory = service_provider.get_factory(entity_slug)
        
        # Create journal entry
        journal_entry = factory.journal_entry.create_journal_entry(
            ledger_model=validated_data['ledger'],
            description=validated_data['description'],
            timestamp=validated_data['timestamp'],
            activity=validated_data.get('activity'),
            origin=validated_data.get('origin', 'api')
        )
        
        # Create transactions if provided
        if transactions_data:
            factory.journal_entry.create_transactions(
                journal_entry=journal_entry,
                transactions_data=transactions_data
            )
            
        return journal_entry
        
    def update(self, instance, validated_data):
        """Update journal entry."""
        if instance.posted:
            raise serializers.ValidationError(
                'Cannot update posted journal entry'
            )
            
        transactions_data = validated_data.pop('transactions', None)
        entity_slug = self.context['entity_slug']
        
        # Get service factory
        factory = service_provider.get_factory(entity_slug)
        
        # Update transactions if provided
        if transactions_data is not None:
            factory.journal_entry.create_transactions(
                journal_entry=instance,
                transactions_data=transactions_data
            )
            
        # Update other fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        return instance
