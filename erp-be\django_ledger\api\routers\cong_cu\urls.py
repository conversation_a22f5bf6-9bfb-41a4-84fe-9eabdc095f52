"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Cong Cu (Tool) router implementation.
"""

from django.urls import include, path

urlpatterns = [
    path(
        "khai-bao-tang-giam-ccdc/",
        include("django_ledger.api.routers.cong_cu.khai_bao_tang_giam_ccdc.urls"),
    ),
    path(
        "dung-phan-bo-ccdc/",
        include("django_ledger.api.routers.cong_cu.dung_phan_bo_ccdc.urls"),
    ),
    path(
        "dieu-chuyen-ccdc/",
        include("django_ledger.api.routers.cong_cu.dieu_chuyen_ccdc.urls"),
    ),
    # Include kiem_ke_ccdc URLs
    path(
        "kiem-ke-ccdc/", include("django_ledger.api.routers.cong_cu.kiem_ke_ccdc.urls")
    ),
    path(
        "tang-giam-ccdc/",
        include("django_ledger.api.routers.cong_cu.tang_giam_ccdc.urls"),
    ),
    # Include phan_bo_ccdc URLs
    path(
        "phan-bo-ccdc/",
        include("django_ledger.api.routers.cong_cu.phan_bo_ccdc.urls"),
    ),
]
