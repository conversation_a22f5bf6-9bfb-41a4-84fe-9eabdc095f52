"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Gia Ban (Price List) module.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.danh_muc.ban_hang.gia_ban import (
    GiaBanViewSet,
    GiaBanChiTietViewSet
)

# Main router for GiaBan
router = DefaultRouter()
router.register('', GiaBanViewSet, basename='gia-ban')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
    
    # Nested routes for gia-ban
    path('<uuid:gia_ban_uuid>/', include([
        # Chi tiet gia ban routes
        path('chi-tiet/', GiaBanChiTietViewSet.as_view({
            'get': 'list',
            'post': 'create'
        }), name='chi-tiet-gia-ban-list'),

        path('chi-tiet/<uuid:uuid>/', GiaBanChiTietViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
            'patch': 'partial_update',
            'delete': 'destroy'
        }), name='chi-tiet-gia-ban-detail'),
    ])),
]
