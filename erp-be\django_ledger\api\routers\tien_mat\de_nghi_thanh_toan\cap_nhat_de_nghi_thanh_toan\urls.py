"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for CapNhatDeNghiThanhToan (Payment Proposal Update) module.
"""

from django.urls import include, path
from django_ledger.api.views.tien_mat.de_nghi_thanh_toan.cap_nhat_de_nghi_thanh_toan import (
    CapNhatDeNghiThanhToanViewSet,
    ChiTietCapNhatDeNghiThanhToanViewSet,
)
from rest_framework.routers import DefaultRouter

# Main router for CapNhatDeNghiThanhToan
router = DefaultRouter()
router.register(
    "", CapNhatDeNghiThanhToanViewSet, basename="cap-nhat-de-nghi-thanh-toan"
)

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path("", include(router.urls)),
    # Nested routes for cap-nhat-de-nghi-thanh-toan
    path(
        "<uuid:parent_uuid>/",
        include(
            [
                # Chi tiet cap nhat de nghi thanh toan routes
                path(
                    "chi-tiet/",
                    ChiTietCapNhatDeNghiThanhToanViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="chi-tiet-cap-nhat-de-nghi-thanh-toan-list",
                ),
                path(
                    "chi-tiet/<uuid:uuid>/",
                    ChiTietCapNhatDeNghiThanhToanViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "delete": "destroy",
                        }
                    ),
                    name="chi-tiet-cap-nhat-de-nghi-thanh-toan-detail",
                ),
            ]
        ),
    ),
]
