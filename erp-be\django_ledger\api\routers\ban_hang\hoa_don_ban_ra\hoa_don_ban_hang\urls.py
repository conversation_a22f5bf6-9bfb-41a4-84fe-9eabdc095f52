from django.urls import include, path
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang import (
    ChiTietHoaDonBanHangViewSet,
    HoaDonBanHangViewSet,
    ThongTinThanhToanHoaDonBanHangViewSet,
)

# Main router for HoaDonBanHang
router = DefaultRouter()
router.register("hoa-don-ban-hang", HoaDonBanHangViewSet, basename="hoa-don-ban-hang")

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path("", include(router.urls)),
    # Nested routes for hoa-don-ban-hang
    path(
        "hoa-don-ban-hang/<uuid:hoa_don_uuid>/",
        include(
            [
                # Chi tiet hoa don ban hang routes
                path(
                    "chi-tiet/",
                    ChiTietHoaDonBanHangViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="chi-tiet-hoa-don-ban-hang-list",
                ),
                path(
                    "chi-tiet/<uuid:uuid>/",
                    ChiTietHoaDonBanHangViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="chi-tiet-hoa-don-ban-hang-detail",
                ),
                # Thong tin thanh toan hoa don ban hang routes
                path(
                    "thanh-toan/",
                    ThongTinThanhToanHoaDonBanHangViewSet.as_view(
                        {"get": "list", "post": "create"}
                    ),
                    name="thong-tin-thanh-toan-hoa-don-ban-hang-list",
                ),
                path(
                    "thanh-toan/<uuid:uuid>/",
                    ThongTinThanhToanHoaDonBanHangViewSet.as_view(
                        {
                            "get": "retrieve",
                            "put": "update",
                            "patch": "partial_update",
                            "delete": "destroy",
                        }
                    ),
                    name="thong-tin-thanh-toan-hoa-don-ban-hang-detail",
                ),
            ]
        ),
    ),
]
