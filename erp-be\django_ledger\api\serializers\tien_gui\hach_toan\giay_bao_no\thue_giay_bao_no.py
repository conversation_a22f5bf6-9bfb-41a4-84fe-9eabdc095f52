"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for TaxDetail model.
"""
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from django_ledger.models import TaxDetailModel
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.tax import TaxModelSerializer
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.han_thanh_toan import HanThanhToanModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer


class TaxDetailSerializer(serializers.ModelSerializer):
    """
    Serializer for TaxDetail model.
    """
    # Read-only fields for related objects
    giay_bao_no_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    ma_thue_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_no_data = serializers.SerializerMethodField(read_only=True)
    tk_du_data = serializers.SerializerMethodField(read_only=True)
    ma_kh9_data = serializers.SerializerMethodField(read_only=True)
    ma_tt_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = TaxDetailModel
        fields = [
            'uuid',
            'giay_bao_no',
            'giay_bao_no_data',
            'line',
            'ngay_ct0',
            'thue_suat',
            'ma_mau_ct',
            'ma_mau_bc',
            'ma_tc_thue',
            'ten_kh_thue',
            'dia_chi',
            'ma_so_thue',
            'ten_vt_thue',
            't_tien_nt',
            't_tien',
            'ten_tk_thue_no',
            'ten_tk_du',
            't_thue_nt',
            't_thue',
            'ten_kh9',
            'ten_tt',
            'ghi_chu',
            'id_tt',
            'ma_lsx',
            'ma_kh',
            'ma_kh_data',
            'ma_thue',
            'ma_thue_data',
            'so_ct0',
            'so_ct2',
            'tk_thue_no',
            'tk_thue_no_data',
            'tk_du',
            'tk_du_data',
            'ma_kh9',
            'ma_kh9_data',
            'ma_tt',
            'ma_tt_data',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_ku',
            'ma_phi',
            'ma_sp',
            'ma_sp_data',
            'ma_cp0',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'giay_bao_no',
            'giay_bao_no_data',
            'ma_kh_data',
            'ma_thue_data',
            'tk_thue_no_data',
            'tk_du_data',
            'ma_kh9_data',
            'ma_tt_data',
            'ma_bp_data',
            'ma_vv_data',
            'ma_hd_data',
            'ma_sp_data',
            'created',
            'updated'
        ]

    def get_giay_bao_no_data(self, obj):
        """
        Get parent bank transfer document data.
        """
        if obj.giay_bao_no:
            return {
                'uuid': str(obj.giay_bao_no.uuid),
                'i_so_ct': obj.giay_bao_no.i_so_ct,
                'ngay_ct': obj.giay_bao_no.ngay_ct,
                'dien_giai': obj.giay_bao_no.dien_giai
            }
        return None

    def get_ma_kh_data(self, obj):
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_ma_thue_data(self, obj):
        """
        Get tax data.
        """
        if obj.ma_thue:
            return TaxModelSerializer(obj.ma_thue).data
        return None

    def get_tk_thue_no_data(self, obj):
        """
        Get tax debit account data.
        """
        if obj.tk_thue_no:
            return AccountModelSerializer(obj.tk_thue_no).data
        return None

    def get_tk_du_data(self, obj):
        """
        Get balance account data.
        """
        if obj.tk_du:
            return AccountModelSerializer(obj.tk_du).data
        return None

    def get_ma_kh9_data(self, obj):
        """
        Get customer9 data.
        """
        if obj.ma_kh9:
            return CustomerModelSerializer(obj.ma_kh9).data
        return None

    def get_ma_tt_data(self, obj):
        """
        Get payment term data.
        """
        if obj.ma_tt:
            return HanThanhToanModelSerializer(obj.ma_tt).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):
        """
        Get task data.
        """
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_hd_data(self, obj):
        """
        Get contract data.
        """
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_sp_data(self, obj):
        """
        Get product data.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def validate_line(self, value):
        """
        Validate line number.
        """
        if value <= 0:
            raise serializers.ValidationError("Line number must be greater than 0")
        return value

    def validate_thue_suat(self, value):
        """
        Validate tax rate.
        """
        if value is not None and (value < 0 or value > 100):
            raise serializers.ValidationError("Tax rate must be between 0 and 100")
        return value

    def validate_t_tien(self, value):
        """
        Validate total amount.
        """
        if value is not None and value < 0:
            raise serializers.ValidationError("Total amount cannot be negative")
        return value

    def validate_t_tien_nt(self, value):
        """
        Validate total foreign currency amount.
        """
        if value is not None and value < 0:
            raise serializers.ValidationError("Total foreign currency amount cannot be negative")
        return value

    def validate_t_thue(self, value):
        """
        Validate total tax amount.
        """
        if value is not None and value < 0:
            raise serializers.ValidationError("Total tax amount cannot be negative")
        return value

    def validate_t_thue_nt(self, value):
        """
        Validate total foreign currency tax amount.
        """
        if value is not None and value < 0:
            raise serializers.ValidationError("Total foreign currency tax amount cannot be negative")
        return value

    def validate(self, attrs):
        """
        Validate the entire object.
        """
        # Validate tax calculation
        if attrs.get('thue_suat') and attrs.get('t_tien'):
            expected_tax = attrs['t_tien'] * (attrs['thue_suat'] / 100)
            if attrs.get('t_thue') and abs(attrs['t_thue'] - expected_tax) > 1:
                raise serializers.ValidationError("Total tax amount does not match total amount * tax rate")

        # Validate foreign currency tax calculation
        if attrs.get('thue_suat') and attrs.get('t_tien_nt'):
            expected_tax_nt = attrs['t_tien_nt'] * (attrs['thue_suat'] / 100)
            if attrs.get('t_thue_nt') and abs(attrs['t_thue_nt'] - expected_tax_nt) > 1:
                raise serializers.ValidationError("Total foreign currency tax amount does not match total foreign currency amount * tax rate")

        return attrs



