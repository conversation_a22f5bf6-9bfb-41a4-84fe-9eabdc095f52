"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

KhaiBaoMaHangIpos Serializer module.
"""

from rest_framework import serializers

from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.models.danh_muc.khai_bao_kho_vat_tu.khai_bao_ma_hang_ipos import KhaiBaoMaHangIposModel
from django_ledger.services.danh_muc.khai_bao_kho_vat_tu.khai_bao_ma_hang_ipos import KhaiBaoMaHangIposService


class KhaiBaoMaHangIposSerializer(serializers.ModelSerializer):
    """
    Serializer for KhaiBaoMaHangIpos model. Handles conversion between KhaiBaoMaHangIpos model instances and JSON representations.
    """
    # Nested serializers
    ma_vt_data = serializers.SerializerMethodField(read_only=True)

    def get_ma_vt_data(self, obj):
        """Method field for ma_vt_data"""
        if obj.ma_vt:
            return VatTuSerializer(obj.ma_vt).data
        return None

    class Meta:
        model = KhaiBaoMaHangIposModel
        fields = [
            'uuid',
            'entity_model',
            'ma_vt',
            'ma_vt_data',
            'ma_khai_bao',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'ma_vt_data',
            'created',
            'updated'
        ]

    def create(self, validated_data):
        """
        Create a new KhaiBaoMaHangIpos instance.
        
        Parameters
        ----------
        validated_data : dict
            The validated data to create the instance with.
            
        Returns
        -------
        KhaiBaoMaHangIposModel
            The created KhaiBaoMaHangIposModel instance.
        """
        entity_model = self.context['entity_model']
        service = KhaiBaoMaHangIposService(entity_model=entity_model)
        
        # Check if a declaration already exists for this material
        if service.exists_by_ma_vt(validated_data['ma_vt']):
            raise serializers.ValidationError(
                {'ma_vt': 'A declaration already exists for this material.'}
            )
        
        # Set the entity_model
        validated_data['entity_model'] = entity_model
        
        # Create the instance
        instance = super().create(validated_data)
        return instance

    def update(self, instance, validated_data):
        """
        Update an existing KhaiBaoMaHangIpos instance.
        
        Parameters
        ----------
        instance : KhaiBaoMaHangIposModel
            The instance to update.
        validated_data : dict
            The validated data to update the instance with.
            
        Returns
        -------
        KhaiBaoMaHangIposModel
            The updated KhaiBaoMaHangIposModel instance.
        """
        # Update the instance
        instance = super().update(instance, validated_data)
        return instance
