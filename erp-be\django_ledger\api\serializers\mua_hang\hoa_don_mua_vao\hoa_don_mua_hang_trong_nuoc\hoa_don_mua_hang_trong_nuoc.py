"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Hoa Don Mua Hang Trong Nuoc (Domestic Purchase Invoice) serializer implementation.
"""

from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_phi_chi_tiet_hoa_don_mua_hang_trong_nuoc import (
    ChiPhiChiTietHoaDonMuaHangTrongNuocModelCreateUpdateSerializer,
    ChiPhiChiTietHoaDonMuaHangTrongNuocModelSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_phi_hoa_don_mua_hang_trong_nuoc import (
    ChiPhiHoaDonMuaHangTrongNuocModelCreateUpdateSerializer,
    ChiPhiHoaDonMuaHangTrongNuocModelSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.chi_tiet_hoa_don_mua_hang_trong_nuoc import (
    ChiTietHoaDonMuaHangTrongNuocModelCreateUpdateSerializer,
    ChiTietHoaDonMuaHangTrongNuocModelSerializer,
)
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc.thue_hoa_don_mua_hang_trong_nuoc import (
    ThueHoaDonMuaHangTrongNuocModelCreateUpdateSerializer,
    ThueHoaDonMuaHangTrongNuocModelSerializer,
)
from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc import (
    HoaDonMuaHangTrongNuocModel,
)
from django_ledger.api.serializers.unit import EntityUnitModelSimpleSerializer
from rest_framework import serializers


class HoaDonMuaHangTrongNuocModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the HoaDonMuaHangTrongNuocModel.

    This serializer handles the conversion between HoaDonMuaHangTrongNuocModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).
    """

    # Read-only fields for related objects
    vendor_data = serializers.SerializerMethodField(read_only=True)
    ma_nvmh_data = serializers.SerializerMethodField(read_only=True)
    ma_tt_data = serializers.SerializerMethodField(read_only=True)
    ma_nk_data = serializers.SerializerMethodField(read_only=True)
    ma_nk_pn_data = serializers.SerializerMethodField(read_only=True)
    so_ct_data = serializers.SerializerMethodField(read_only=True)
    so_ct0_data = serializers.SerializerMethodField(read_only=True)
    so_ct2_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)

    # Add nested collections using SerializerMethodField
    chi_tiet_hoa_don = serializers.SerializerMethodField(read_only=True)
    chi_phi_hoa_don = serializers.SerializerMethodField(read_only=True)
    chi_phi_chi_tiet_hoa_don = serializers.SerializerMethodField(read_only=True)
    thue_hoa_don = serializers.SerializerMethodField(read_only=True)
    unit_id_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = HoaDonMuaHangTrongNuocModel
        fields = [
            "uuid",
            "entity_model",
            "nguoi_tao",
            "ngay_tao",
            "hdmh_yn",
            "pn_yn",
            "pc_tao_yn",
            "ma_httt",
            "xt_yn",
            "loai_ck",
            "ck_tl_nt",
            "ma_gd",
            "ma_ngv",
            "ten_kh",
            "ong_ba",
            "vendor",
            "vendor_data",
            "ma_nvmh",
            "ma_nvmh_data",
            "e_mail",
            "tk",
            "ma_tt",
            "ma_tt_data",
            "dien_giai",
            "unit_id",
            "unit_id_data",
            "i_so_ct",
            "ma_nk",
            "ma_nk_data",
            "so_ct",
            "so_ct_data",
            "ngay_ct",
            "ngay_lct",
            "so_ct0",
            "so_ct0_data",
            "ngay_ct0",
            "so_ct2",
            "so_ct2_data",
            "ma_nk_pn",
            "ma_nk_pn_data",
            "so_pn",
            "ngay_pn",
            "i_so_pn",
            "ma_nt",
            "ma_nt_data",
            "ty_gia",
            "status",
            "transfer_yn",
            "pc_ngay_ct",
            "pc_ma_ct",
            "pc_ma_nk",
            "pc_tknh",
            "pc_tk",
            "pc_t_tt_nt",
            "chi_tiet_hoa_don",
            "chi_phi_hoa_don",
            "chi_phi_chi_tiet_hoa_don",
            "thue_hoa_don",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "entity_model_data",
            "vendor_data",
            "ma_nvmh_data",
            "ma_tt_data",
            "ma_nk_data",
            "ma_nk_pn_data",
            "so_ct_data",
            "so_ct0_data",
            "so_ct2_data",
            "ma_nt_data",
            "chi_tiet_hoa_don",
            "chi_phi_hoa_don",
            "chi_phi_chi_tiet_hoa_don",
            "thue_hoa_don",
            "created",
            "updated",
        ]

    def get_vendor_data(self, obj):
        """
        Get basic information about the vendor
        """
        if not obj.vendor:
            return None

        return {
            "uuid": str(obj.vendor.uuid),
            "customer_code": getattr(obj.vendor, "customer_code", ""),
            "customer_name": getattr(obj.vendor, "customer_name", ""),
        }

    def get_ma_nvmh_data(self, obj):
        """
        Get basic information about the purchasing staff
        """
        if not obj.ma_nvmh:
            return None

        return {
            "uuid": str(obj.ma_nvmh.uuid),
            "ho_ten_nhan_vien": getattr(obj.ma_nvmh, "ho_ten_nhan_vien", ""),
            "ma_nhan_vien": getattr(obj.ma_nvmh, "ma_nhan_vien", ""),
        }

    def get_ma_tt_data(self, obj):
        """
        Get basic information about the payment method
        """
        if not obj.ma_tt:
            return None

        return {
            "uuid": str(obj.ma_tt.uuid),
            "ma_tt": getattr(obj.ma_tt, "ma_tt", ""),
            "ten_tt": getattr(obj.ma_tt, "ten_tt", ""),
        }

    def get_ma_nk_data(self, obj):
        """
        Get basic information about the document book
        """
        if not obj.ma_nk:
            return None

        return {
            "uuid": str(obj.ma_nk.uuid),
            "ma_nk": getattr(obj.ma_nk, "ma_nk", ""),
            "ten_nk": getattr(obj.ma_nk, "ten_nk", ""),
        }

    def get_ma_nk_pn_data(self, obj):
        """
        Get basic information about the receipt document book
        """
        if not obj.ma_nk_pn:
            return None

        return {
            "uuid": str(obj.ma_nk_pn.uuid),
            "ma_nk": getattr(obj.ma_nk_pn, "ma_nk", ""),
            "ten_nk": getattr(obj.ma_nk_pn, "ten_nk", ""),
        }

    def get_so_ct_data(self, obj):
        """
        Get basic information about the document
        """
        if not obj.so_ct:
            return None

        return {
            "uuid": str(obj.so_ct.uuid),
            "ma_ct": getattr(obj.so_ct, "ma_ct", ""),
            "ten_ct": getattr(obj.so_ct, "ten_ct", ""),
        }

    def get_so_ct0_data(self, obj):
        """
        Get basic information about the reference document
        """
        if not obj.so_ct0:
            return None

        return {
            "uuid": str(obj.so_ct0.uuid),
            "ma_ct": getattr(obj.so_ct0, "ma_ct", ""),
            "ten_ct": getattr(obj.so_ct0, "ten_ct", ""),
        }

    def get_so_ct2_data(self, obj):
        """
        Get basic information about the secondary reference document
        """
        if not obj.so_ct2:
            return None

        return {
            "uuid": str(obj.so_ct2.uuid),
            "ma_ct": getattr(obj.so_ct2, "ma_ct", ""),
            "ten_ct": getattr(obj.so_ct2, "ten_ct", ""),
        }

    def get_ma_nt_data(self, obj):
        """
        Get basic information about the currency
        """
        if not obj.ma_nt:
            return None

        return {
            "uuid": str(obj.ma_nt.uuid),
            "ma_nt": getattr(obj.ma_nt, "ma_nt", ""),
            "ten_nt": getattr(obj.ma_nt, "ten_nt", ""),
        }

    def get_chi_tiet_hoa_don(self, obj):
        """
        Get the chi_tiet_hoa_don (details) for the HoaDonMuaHangTrongNuoc.
        """
        chi_tiet = obj.chi_tiet_hoa_don.all()
        return ChiTietHoaDonMuaHangTrongNuocModelSerializer(chi_tiet, many=True).data

    def get_chi_phi_hoa_don(self, obj):
        """
        Get the chi_phi_hoa_don (expenses) for the HoaDonMuaHangTrongNuoc.
        """
        chi_phi = obj.chi_phi_hoa_don.all()
        return ChiPhiHoaDonMuaHangTrongNuocModelSerializer(chi_phi, many=True).data

    def get_chi_phi_chi_tiet_hoa_don(self, obj):
        """
        Get the chi_phi_chi_tiet_hoa_don (expense details) for the HoaDonMuaHangTrongNuoc.
        """
        chi_phi_chi_tiet = obj.chi_phi_chi_tiet_hoa_don.all()
        return ChiPhiChiTietHoaDonMuaHangTrongNuocModelSerializer(
            chi_phi_chi_tiet, many=True
        ).data

    def get_thue_hoa_don(self, obj):
        """
        Get the thue_hoa_don (taxes) for the HoaDonMuaHangTrongNuoc.
        """
        thue = obj.thue_hoa_don.all()
        return ThueHoaDonMuaHangTrongNuocModelSerializer(thue, many=True).data

    def get_unit_id_data(self, obj):
        if obj.unit_id:
            return EntityUnitModelSimpleSerializer(obj.unit_id).data
        return None


class HoaDonMuaHangTrongNuocModelCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for the HoaDonMuaHangTrongNuocModel.
    Used for create and update operations.

    This serializer handles the conversion between JSON data and HoaDonMuaHangTrongNuocModel instances,
    supporting both creation of new instances and updating existing ones.

    Key features:
    - Accepts nested data for related collections (chi_tiet_hoa_don, chi_phi_hoa_don, etc.)
    - Performs validation on the entire data structure
    - Accepts UUID references for foreign key fields
    """

    chi_tiet_hoa_don = ChiTietHoaDonMuaHangTrongNuocModelCreateUpdateSerializer(
        many=True, required=False
    )
    chi_phi_hoa_don = ChiPhiHoaDonMuaHangTrongNuocModelCreateUpdateSerializer(
        many=True, required=False
    )
    chi_phi_chi_tiet_hoa_don = (
        ChiPhiChiTietHoaDonMuaHangTrongNuocModelCreateUpdateSerializer(
            many=True, required=False
        )
    )
    thue_hoa_don = ThueHoaDonMuaHangTrongNuocModelCreateUpdateSerializer(
        many=True, required=False
    )

    class Meta:
        model = HoaDonMuaHangTrongNuocModel
        fields = [
            "uuid",
            "entity_model",
            "hdmh_yn",
            "pn_yn",
            "pc_tao_yn",
            "ma_httt",
            "xt_yn",
            "loai_ck",
            "ck_tl_nt",
            "ma_ngv",
            "ten_kh",
            "ong_ba",
            "vendor",
            "ma_nvmh",
            "e_mail",
            "tk",
            "ma_tt",
            "dien_giai",
            "unit_id",
            "i_so_ct",
            "ma_nk",
            "so_ct",
            "ngay_ct",
            "ngay_lct",
            "so_ct0",
            "ngay_ct0",
            "so_ct2",
            "ma_nk_pn",
            "so_pn",
            "ngay_pn",
            "i_so_pn",
            "ma_nt",
            "ty_gia",
            "status",
            "transfer_yn",
            "ma_gd",
            "pc_ngay_ct",
            "pc_ma_ct",
            "pc_ma_nk",
            "pc_tknh",
            "pc_tk",
            "pc_t_tt_nt",
            "nguoi_tao",
            "ngay_tao",
            "chi_tiet_hoa_don",
            "chi_phi_hoa_don",
            "chi_phi_chi_tiet_hoa_don",
            "thue_hoa_don",
        ]
        read_only_fields = ["uuid", "created", "updated"]

    def validate(self, data):
        """
        Validate the data before creating or updating the model instance.

        Parameters
        ----------
        data : dict
            The data to validate.

        Returns
        -------
        dict
            The validated data.
        """
        # Add any custom validation here
        return data
