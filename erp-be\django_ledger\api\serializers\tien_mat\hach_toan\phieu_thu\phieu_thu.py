"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for PhieuThu model.
"""

from rest_framework import serializers

from django.utils.translation import gettext_lazy as _
from django_ledger.models import PhieuThuModel
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.unit import EntityUnitModelSerializer
from django_ledger.api.serializers.quyen_chung_tu import QuyenChungTuDetailSerializer
from django_ledger.api.serializers.chung_tu import ChungTuSerializer
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer
from django_ledger.api.serializers.han_thanh_toan import HanThanhToanModelSerializer
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.tien_mat.hach_toan.phieu_thu.phieu_thu_chi_tiet import (
    PhieuThuChiTietSerializer,
)


class PhieuThuSerializer(serializers.ModelSerializer):
    """
    Serializer for PhieuThu model.
    """

    # Read-only fields for related objects
    tk_data = serializers.SerializerMethodField(read_only=True)
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    ma_nk_data = serializers.SerializerMethodField(read_only=True)
    so_ct_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)
    so_ct0_data = serializers.SerializerMethodField(read_only=True)
    ma_tt_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)

    # Child data
    child_data = serializers.SerializerMethodField(read_only=True)
    child_items = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = PhieuThuModel
        fields = [
            "uuid",
            "entity_model",
            "id",
            "ma_ngv",
            "dia_chi",
            "ong_ba",
            "dien_giai",
            "tk",
            "tk_data",
            "unit_id",
            "unit_id_data",
            "i_so_ct",
            "ma_nk",
            "ma_nk_data",
            "so_ct",
            "so_ct_data",
            "ngay_ct",
            "ngay_lct",
            "ma_nt",
            "ma_nt_data",
            "ty_gia",
            "status",
            "so_ct0",
            "so_ct0_data",
            "ngay_ct0",
            "so_ct_goc",
            "dien_giai_ct_goc",
            "ma_tt",
            "ma_tt_data",
            "ma_kh",
            "ma_kh_data",
            "t_tien_nt",
            "t_tien",
            "child_data",
            "child_items",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "entity_model",
            "tk_data",
            "unit_id_data",
            "ma_nk_data",
            "so_ct_data",
            "ma_nt_data",
            "so_ct0_data",
            "ma_tt_data",
            "ma_kh_data",
            "child_data",
            "t_tien_nt",
            "t_tien",
            "created",
            "updated",
        ]

    def get_tk_data(self, obj):
        """
        Get account data.
        """
        if obj.tk:
            return AccountModelSerializer(obj.tk).data
        return None

    def get_unit_id_data(self, obj):
        """
        Get unit data.
        """
        if obj.unit_id:
            return EntityUnitModelSerializer(obj.unit_id).data
        return None

    def get_ma_nk_data(self, obj):
        """
        Get document type data.
        """
        if obj.ma_nk:
            return QuyenChungTuDetailSerializer(obj.ma_nk).data
        return None

    def get_so_ct_data(self, obj):
        """
        Get document number data.
        """
        if obj.so_ct:
            return ChungTuSerializer(obj.so_ct).data
        return None

    def get_ma_nt_data(self, obj):
        """
        Get currency data.
        """
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_so_ct0_data(self, obj):
        """
        Get original document number data.
        """
        if obj.so_ct0:
            return ChungTuSerializer(obj.so_ct0).data
        return None

    def get_ma_tt_data(self, obj):
        """
        Get payment term data.
        """
        if obj.ma_tt:
            return HanThanhToanModelSerializer(obj.ma_tt).data
        return None

    def get_ma_kh_data(self, obj):
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_child_data(self, obj):
        """
        Get child details data.
        """
        children = obj.children.all()
        return PhieuThuChiTietSerializer(children, many=True).data
