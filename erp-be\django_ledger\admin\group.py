from django.contrib import admin
from django.utils.translation import gettext_lazy as _


class GroupModelAdmin(admin.ModelAdmin):
    list_display = ['ma_nhom', 'ten_phan_nhom', 'ten2', 'entity_model', 'trang_thai', 'loai_nhom', 'created', 'updated']
    search_fields = ['ma_nhom', 'ten_phan_nhom', 'ten2', 'entity_model', 'trang_thai', 'loai_nhom']
    list_filter = ['entity_model', 'trang_thai', 'loai_nhom']