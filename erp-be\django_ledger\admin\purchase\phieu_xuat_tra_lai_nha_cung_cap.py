from django.contrib import admin

from django_ledger.models import PhieuXuatTraLaiNhaCungCapModel, ChiTietPhieuXuatTraLaiNhaCungCapModel


class ChiTietPhieuXuatTraLaiNhaCungCapInline(admin.TabularInline):
    model = ChiTietPhieuXuatTraLaiNhaCungCapModel
    extra = 1


class PhieuXuatTraLaiNhaCungCapModelAdmin(admin.ModelAdmin):
    list_display = ['so_ct', 'ma_nk', 'ngay_ct', 'ma_kh', 'ten_kh_thue', 'status', 't_so_luong', 't_tien']
    list_filter = ['status', 'ngay_ct', 'entity_model']
    search_fields = ['so_ct', 'ma_nk', 'ma_kh', 'ten_kh_thue', 'dien_giai']
    inlines = [ChiTietPhieuXuatTraLaiNhaCungCapInline]


class ChiTietPhieuXuatTraLaiNhaCungCapModelAdmin(admin.ModelAdmin):
    list_display = ['phieu_xuat_tra_lai', 'line', 'ma_vt', 'dvt', 'so_luong', 'gia', 'tien']
    list_filter = ['phieu_xuat_tra_lai__status', 'phieu_xuat_tra_lai__ngay_ct']
    search_fields = ['phieu_xuat_tra_lai__so_ct', 'ma_vt', 'ma_kho']
