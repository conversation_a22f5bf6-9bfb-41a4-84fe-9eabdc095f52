"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer cho MuaHangModel
"""
from rest_framework import serializers

from django_ledger.models.mua_hang.purchase import PurchaseModel

class PurchaseModelSerializer(serializers.ModelSerializer):
    """
    Serializer cho PurchaseModel
    Xử lý chuyển đổi dữ liệu giữa model và API
    """

    class Meta:
        model = PurchaseModel
        fields = [
            'uuid',
            'entity_model',
            'product_code',
            'unit_of_measure',
            'effective_date',
            'vendor_code',
            'currency_code',
            'quantity_from',
            'price_in_currency',
            'status',
            'created',
            'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated']

    def validate_quantity_from(self, value):
        """
        Kiểm tra số lượng từ không được âm
        """
        if value < 0:
            raise serializers.ValidationError(
                "Số lượng từ không đượ<PERSON> âm"
            )
        return value

    def validate_price_in_currency(self, value):
        """
        Kiểm tra giá không được âm
        """
        if value < 0:
            raise serializers.ValidationError(
                "Giá không được âm"
            )
        return value

    def validate(self, data):
        """
        Kiểm tra toàn bộ dữ liệu trước khi lưu
        """
        # Có thể thêm các validation khác ở đây
        # Ví dụ: kiểm tra mã tiền tệ có hợp lệ không
        # Kiểm tra mã vật tư có tồn tại không
        # Kiểm tra mã khách hàng có tồn tại không
        return data
