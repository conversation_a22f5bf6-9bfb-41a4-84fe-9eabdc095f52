"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for DanhMucCongDoan (Production Stage Catalog).
"""

from rest_framework import serializers

from django_ledger.models.gia_thanh.danh_muc.danh_muc_cong_doan import DanhMucCongDoanModel
from django_ledger.api.serializers.base import GlobalModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer


class DanhMucCongDoanSerializer(GlobalModelSerializer):
    """
    Serializer for DanhMucCongDoan model.
    Handles serialization and deserialization of DanhMucCongDoan instances.
    """

    # Read-only fields for related objects
    entity_slug = serializers.ReadOnlyField(source='entity_model.slug')
    entity_name = serializers.ReadOnlyField(source='entity_model.name')

    # Nested serializers for related objects
    ma_bp_data = BoPhanModelSerializer(source='ma_bp', read_only=True)
    ma_bp0_data = BoPhanModelSerializer(source='ma_bp0', read_only=True)

    class Meta:
        model = DanhMucCongDoanModel
        fields = [
            'uuid',
            'entity_slug',
            'entity_name',
            'ma_bp',
            'ma_bp_data',
            'truc_tiep',
            'ma_bp0',
            'ma_bp0_data',
            'status',
            'created',
            'updated',
        ]
        read_only_fields = ['uuid', 'entity_slug', 'entity_name', 'created', 'updated']

    def validate(self, data):
        """
        Validate the data before saving.

        Args:
            data: Dictionary containing data to validate

        Returns:
            Validated data

        Raises:
            serializers.ValidationError: If validation fails
        """
        # No need to check if ma_bp0 equals ma_bp since they both reference BoPhanModel
        return data
