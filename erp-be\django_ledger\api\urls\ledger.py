from django.urls import include, path
from django_ledger.api.views.ledger.ledger import LedgerModelViewSet
from django_ledger.api.views.ledger.ledger_items import LedgerItemModelViewSet
from rest_framework.routers import DefaultRouter

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r"", LedgerModelViewSet, basename="ledger")

# Create a router for ledger items
ledger_items_router = DefaultRouter()
ledger_items_router.register(r"", LedgerItemModelViewSet, basename="ledger-item")

urlpatterns = [
    path("", include(router.urls)),
    path("<uuid:ledger_uuid>/items/", include(ledger_items_router.urls)),
]
