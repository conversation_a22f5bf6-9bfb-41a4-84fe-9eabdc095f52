"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for CapNhatKiemKe (Inventory Update) model.
"""

from rest_framework import serializers

from django_ledger.models.ton_kho.kiem_ke.cap_nhat_kiem_ke import CapNhatKiemKeModel

# Import serializers for foreign key fields
from django_ledger.api.serializers.unit import EntityUnitModelSimpleSerializer
from django_ledger.api.serializers.don_vi_tinh import DonViTinhSerializer
from django_ledger.api.serializers.warehouse import KhoHangModelSerializer
from django_ledger.api.serializers.vi_tri_kho_hang import ViTriKhoHangModelSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer
from django_ledger.api.serializers.lo import LoModelSerializer


class CapNhatKiemKeModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the CapNhatKiemKeModel.

    This serializer handles the conversion between CapNhatKiemKeModel instances and JSON representations,
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields (entity_model, unit_id, dvt, ma_kho, ma_vi_tri, ma_vt, ma_lo)
    - Adds additional fields with "_data" suffix (unit_id_data, dvt_data, ma_kho_data, ma_vi_tri_data, ma_vt_data, ma_lo_data)
      that contain the complete nested object data with all available fields
    - When deserializing, accepts UUIDs for reference fields
    """

    # SerializerMethodField for related data fields
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    dvt_data = serializers.SerializerMethodField(read_only=True)
    ma_kho_data = serializers.SerializerMethodField(read_only=True)
    ma_vi_tri_data = serializers.SerializerMethodField(read_only=True)
    ma_vt_data = serializers.SerializerMethodField(read_only=True)
    ma_lo_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = CapNhatKiemKeModel
        fields = [
            'uuid',
            'entity_model',
            'unit_id',
            'unit_id_data',
            'id',
            'dvt',
            'dvt_data',
            'ton_cuoi',
            'sl_kk',
            'he_so',
            'du',
            'du_kk',
            'ma_kho',
            'ma_kho_data',
            'ma_vi_tri',
            'ma_vi_tri_data',
            'ma_vt',
            'ma_vt_data',
            'ma_lo',
            'ma_lo_data',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'unit_id_data',
            'dvt_data',
            'ma_kho_data',
            'ma_vi_tri_data',
            'ma_vt_data',
            'ma_lo_data',
            'entity_model',
            'created',
            'updated'
        ]

    def get_unit_id_data(self, obj):
        """
        Get the entity unit data corresponding to the unit_id field
        """
        if obj.unit_id:
            return EntityUnitModelSimpleSerializer(obj.unit_id).data
        return None

    def get_dvt_data(self, obj):
        """
        Get the unit of measure data corresponding to the dvt field
        """
        if obj.dvt:
            return DonViTinhSerializer(obj.dvt).data
        return None

    def get_ma_kho_data(self, obj):
        """
        Get the warehouse data corresponding to the ma_kho field
        """
        if obj.ma_kho:
            return KhoHangModelSerializer(obj.ma_kho).data
        return None

    def get_ma_vi_tri_data(self, obj):
        """
        Get the location data corresponding to the ma_vi_tri field
        """
        if obj.ma_vi_tri:
            return ViTriKhoHangModelSerializer(obj.ma_vi_tri).data
        return None

    def get_ma_vt_data(self, obj):
        """
        Get the material/item data corresponding to the ma_vt field
        """
        if obj.ma_vt:
            return VatTuSerializer(obj.ma_vt).data
        return None

    def get_ma_lo_data(self, obj):
        """
        Get the lot data corresponding to the ma_lo field
        """
        if obj.ma_lo:
            return LoModelSerializer(obj.ma_lo).data
        return None
