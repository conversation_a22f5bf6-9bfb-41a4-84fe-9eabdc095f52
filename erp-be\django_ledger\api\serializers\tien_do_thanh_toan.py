from rest_framework import serializers
from django_ledger.models import TienDoThanhToanModel
from django_ledger.models.danh_muc import DotThanhToanModel

class DotThanhToanModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = DotThanhToanModel
        fields = ['uuid', 'ma_dtt', 'ten_dtt']
        read_only_fields = ['uuid']

class TienDoThanhToanModelCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = TienDoThanhToanModel
        fields = [
            'line', 'ma_dot', 'ten_dot',
            'ngay_tt', 'ty_le', 'tien_nt', 'tien',
            'han_tt', 'ghi_chu', 'status'
        ]

class TienDoThanhToanModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = TienDoThanhToanModel
        fields = [
            'uuid', 'hop_dong', 'line', 'ma_dot', 'ten_dot',
            'ngay_tt', 'ty_le', 'tien_nt', 'tien',
            'han_tt', 'ghi_chu', 'status',
            'created', 'updated'
        ]
        read_only_fields = ['uuid', 'created', 'updated']