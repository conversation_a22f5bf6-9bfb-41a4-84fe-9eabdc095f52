"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Router for KheUoc (Loan/Contract) model.
This file is kept for backward compatibility.
The new URL structure is defined in urls.py.
"""

from rest_framework.routers import DefaultRouter
from rest_framework_nested.routers import NestedDefaultRouter

from django_ledger.api.views.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import KheUocModelViewSet
from django_ledger.api.views.danh_muc.hop_dong_khe_uoc.khe_uoc.chi_tiet_khe_uoc_lai_suat import ChiTietKheUocLaiSuatModelViewSet
from django_ledger.api.views.danh_muc.hop_dong_khe_uoc.khe_uoc.chi_tiet_khe_uoc_thanh_toan import ChiTietKheUocThanhToanModelViewSet


# Main router for KheUoc (for backward compatibility)
khe_uoc_router = DefaultRouter()
khe_uoc_router.register('danh_muc/hop_dong_khe_uoc/khe_uoc', KheUocModelViewSet, basename='khe-uoc-legacy')

# Nested routers for subsidiary tables (for backward compatibility)
khe_uoc_nested_router = NestedDefaultRouter(khe_uoc_router, 'danh_muc/hop_dong_khe_uoc/khe_uoc', lookup='khe_uoc')
khe_uoc_nested_router.register('chi_tiet_lai_suat', ChiTietKheUocLaiSuatModelViewSet, basename='chi-tiet-khe-uoc-lai-suat-legacy-nested')
khe_uoc_nested_router.register('chi_tiet_thanh_toan', ChiTietKheUocThanhToanModelViewSet, basename='chi-tiet-khe-uoc-thanh-toan-legacy-nested')

# Keep the original routes for backward compatibility
khe_uoc_router.register('danh_muc/hop_dong_khe_uoc/chi_tiet_khe_uoc_lai_suat', ChiTietKheUocLaiSuatModelViewSet, basename='chi-tiet-khe-uoc-lai-suat-legacy')
khe_uoc_router.register('danh_muc/hop_dong_khe_uoc/chi_tiet_khe_uoc_thanh_toan', ChiTietKheUocThanhToanModelViewSet, basename='chi-tiet-khe-uoc-thanh-toan-legacy')
