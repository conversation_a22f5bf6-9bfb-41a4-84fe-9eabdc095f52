"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

QuyetToanCacLanTamUng (Advance Payment Settlement) Serializer
"""

from rest_framework import serializers

from django_ledger.models.mua_hang.thanh_toan_tam_ung import QuyetToanCacLanTamUngModel
from django_ledger.services.mua_hang.thanh_toan_tam_ung import QuyetToanCacLanTamUngService


class QuyetToanCacLanTamUngSerializer(serializers.ModelSerializer):
    """
    A serializer class for the QuyetToanCacLanTamUngModel.
    """

    _data = serializers.SerializerMethodField()
    phieu_thanh_toan_data = serializers.SerializerMethodField()
    ma_kh_data = serializers.SerializerMethodField()

    class Meta:
        model = QuyetToanCacLanTamUngModel
        fields = [
            'uuid',
            'phieu_thanh_toan',
            'phieu_thanh_toan_data',
            'line',
            'ma_kh',
            'ma_kh_data',
            'id_chi',
            'tien_nt',
            'tien',
            '_data'
        ]
        read_only_fields = ['uuid']

    def get__data(self, instance):
        """
        Get referenced data for the instance.

        Parameters
        ----------
        instance: QuyetToanCacLanTamUngModel
            The QuyetToanCacLanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing referenced data.
        """
        data = {}

        if hasattr(instance, 'phieu_thanh_toan') and instance.phieu_thanh_toan:
            data['phieu_thanh_toan'] = {
                'uuid': instance.phieu_thanh_toan.uuid,
                'so_ct': instance.phieu_thanh_toan.so_ct,
                'dien_giai': instance.phieu_thanh_toan.dien_giai
            }

        if hasattr(instance, 'ma_kh') and instance.ma_kh:
            data['ma_kh'] = {
                'uuid': instance.ma_kh.uuid,
                'ma_kh': instance.ma_kh.ma_kh,
                'ten_kh': instance.ma_kh.ten_kh
            }

        return data

    def get_phieu_thanh_toan_data(self, instance):
        """
        Get data for the phieu_thanh_toan field.

        Parameters
        ----------
        instance: QuyetToanCacLanTamUngModel
            The QuyetToanCacLanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing the phieu_thanh_toan data.
        """
        if hasattr(instance, 'phieu_thanh_toan') and instance.phieu_thanh_toan:
            return {
                'uuid': instance.phieu_thanh_toan.uuid,
                'so_ct': instance.phieu_thanh_toan.so_ct,
                'dien_giai': instance.phieu_thanh_toan.dien_giai
            }
        return None

    def get_ma_kh_data(self, instance):
        """
        Get data for the ma_kh field.

        Parameters
        ----------
        instance: QuyetToanCacLanTamUngModel
            The QuyetToanCacLanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing the ma_kh data.
        """
        if hasattr(instance, 'ma_kh') and instance.ma_kh:
            return {
                'uuid': instance.ma_kh.uuid,
                'ma_kh': instance.ma_kh.ma_kh,
                'ten_kh': instance.ma_kh.ten_kh
            }
        return None

    def create(self, validated_data):
        """
        Create a new QuyetToanCacLanTamUngModel instance.

        Parameters
        ----------
        validated_data: dict
            The validated data for the new instance.

        Returns
        -------
        QuyetToanCacLanTamUngModel
            The newly created QuyetToanCacLanTamUngModel instance.
        """
        entity_slug = self.context['entity_slug']
        user_model = self.context['request'].user

        phieu_thanh_toan_uuid = None
        if 'phieu_thanh_toan' in validated_data and validated_data['phieu_thanh_toan']:
            phieu_thanh_toan_uuid = validated_data.pop('phieu_thanh_toan').uuid

        service = QuyetToanCacLanTamUngService()
        instance = service.create(
            entity_slug=entity_slug,
            user_model=user_model,
            phieu_thanh_toan_uuid=phieu_thanh_toan_uuid,
            **validated_data
        )
        return instance

    def update(self, instance, validated_data):
        """
        Update an existing QuyetToanCacLanTamUngModel instance.

        Parameters
        ----------
        instance: QuyetToanCacLanTamUngModel
            The QuyetToanCacLanTamUngModel instance to update.
        validated_data: dict
            The validated data for the update.

        Returns
        -------
        QuyetToanCacLanTamUngModel
            The updated QuyetToanCacLanTamUngModel instance.
        """
        entity_slug = self.context['entity_slug']
        user_model = self.context['request'].user

        if 'phieu_thanh_toan' in validated_data:
            validated_data.pop('phieu_thanh_toan')

        service = QuyetToanCacLanTamUngService()
        instance = service.update(
            entity_slug=entity_slug,
            user_model=user_model,
            instance=instance,
            **validated_data
        )
        return instance
