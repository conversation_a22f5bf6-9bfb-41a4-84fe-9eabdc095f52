"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for BankTransferDetail model.
"""
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from django_ledger.models import BankTransferDetailModel
from django_ledger.api.serializers.customer import CustomerModelSerializer
from django_ledger.api.serializers.tax import TaxModelSerializer
from django_ledger.api.serializers.bank_account import BankAccountModelSerializer
from django_ledger.api.serializers.account import AccountModelSerializer
from django_ledger.api.serializers.organization import BoPhanModelSerializer
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer
from django_ledger.api.serializers.contract import ContractModelSerializer
from django_ledger.api.serializers.vat_tu import VatTuSerializer


class BankTransferDetailSerializer(serializers.ModelSerializer):
    """
    Serializer for BankTransferDetail model.
    """
    # Read-only fields for related objects
    giay_bao_no_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    ma_thue_data = serializers.SerializerMethodField(read_only=True)
    tknh2_data = serializers.SerializerMethodField(read_only=True)
    tk_no_data = serializers.SerializerMethodField(read_only=True)
    tk_thue_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = BankTransferDetailModel
        fields = [
            'uuid',
            'giay_bao_no',
            'giay_bao_no_data',
            'line',
            'dien_giai',
            'id_hd',
            'ty_gia2',
            'tien_nt',
            'tien',
            'ma_loai_hd',
            'ten_thue',
            'thue_suat',
            'ten_tk_thue',
            'so_ct2_str',
            'ngay_ct0',
            'ma_mau_ct',
            'ma_mau_bc',
            'ma_tc_thue',
            'ma_kh_thue',
            'ten_kh_thue',
            'dia_chi',
            'ma_so_thue',
            'ten_vt_thue',
            'thue_nt',
            'thue',
            'ten_kh9',
            'id_dn',
            'line_dn',
            'ghi_chu',
            'id_tt',
            'ma_lsx',
            'ma_kh',
            'ma_kh_data',
            'ma_thue',
            'ma_thue_data',
            'tknh2',
            'tknh2_data',
            'tk_no',
            'tk_no_data',
            'tk_thue',
            'tk_thue_data',
            'so_ct0',
            'so_ct2',
            'ma_kh9',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_ku',
            'ma_phi',
            'ma_sp',
            'ma_sp_data',
            'ma_cp0',
            'created',
            'updated'
        ]
        read_only_fields = [
            'uuid',
            'giay_bao_no',
            'giay_bao_no_data',
            'ma_kh_data',
            'ma_thue_data',
            'tknh2_data',
            'tk_no_data',
            'tk_thue_data',
            'ma_bp_data',
            'ma_vv_data',
            'ma_hd_data',
            'ma_sp_data',
            'created',
            'updated'
        ]

    def get_giay_bao_no_data(self, obj):
        """
        Get parent bank transfer document data.
        """
        if obj.giay_bao_no:
            return {
                'uuid': str(obj.giay_bao_no.uuid),
                'i_so_ct': obj.giay_bao_no.i_so_ct,
                'ngay_ct': obj.giay_bao_no.ngay_ct,
                'dien_giai': obj.giay_bao_no.dien_giai
            }
        return None

    def get_ma_kh_data(self, obj):
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_ma_thue_data(self, obj):
        """
        Get tax data.
        """
        if obj.ma_thue:
            return TaxModelSerializer(obj.ma_thue).data
        return None

    def get_tknh2_data(self, obj):
        """
        Get bank account data.
        """
        if obj.tknh2:
            return BankAccountModelSerializer(obj.tknh2).data
        return None

    def get_tk_no_data(self, obj):
        """
        Get debit account data.
        """
        if obj.tk_no:
            return AccountModelSerializer(obj.tk_no).data
        return None

    def get_tk_thue_data(self, obj):
        """
        Get tax account data.
        """
        if obj.tk_thue:
            return AccountModelSerializer(obj.tk_thue).data
        return None

    def get_ma_bp_data(self, obj):
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):
        """
        Get task data.
        """
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_hd_data(self, obj):
        """
        Get contract data.
        """
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_sp_data(self, obj):
        """
        Get product data.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def validate_line(self, value):
        """
        Validate line number.
        """
        if value <= 0:
            raise serializers.ValidationError("Line number must be greater than 0")
        return value

    def validate_ty_gia2(self, value):
        """
        Validate exchange rate.
        """
        if value is not None and value <= 0:
            raise serializers.ValidationError("Exchange rate must be greater than 0")
        return value

    def validate_thue_suat(self, value):
        """
        Validate tax rate.
        """
        if value is not None and (value < 0 or value > 100):
            raise serializers.ValidationError("Tax rate must be between 0 and 100")
        return value

    def validate_tien(self, value):
        """
        Validate amount.
        """
        if value is not None and value < 0:
            raise serializers.ValidationError("Amount cannot be negative")
        return value

    def validate_tien_nt(self, value):
        """
        Validate foreign currency amount.
        """
        if value is not None and value < 0:
            raise serializers.ValidationError("Foreign currency amount cannot be negative")
        return value

    def validate(self, attrs):
        """
        Validate the entire object.
        """
        # Validate amount consistency with exchange rate
        if attrs.get('tien_nt') and attrs.get('tien') and attrs.get('ty_gia2'):
            expected_vnd = attrs['tien_nt'] * attrs['ty_gia2']
            if abs(attrs['tien'] - expected_vnd) > 1:  # Allow small rounding differences
                raise serializers.ValidationError("VND amount does not match foreign currency amount * exchange rate")

        # Validate tax calculation
        if attrs.get('thue_suat') and attrs.get('tien'):
            expected_tax = attrs['tien'] * (attrs['thue_suat'] / 100)
            if attrs.get('thue') and abs(attrs['thue'] - expected_tax) > 1:
                raise serializers.ValidationError("Tax amount does not match amount * tax rate")

        return attrs



