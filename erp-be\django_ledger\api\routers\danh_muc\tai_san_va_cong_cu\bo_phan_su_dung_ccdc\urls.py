"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for BoPhanSuDungCCDC API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.danh_muc.tai_san_va_cong_cu.bo_phan_su_dung_ccdc import BoPhanSuDungCCDCViewSet

# Main router for BoPhanSuDungCCDC
router = DefaultRouter()
router.register('', BoPhanSuDungCCDCViewSet, basename='bo-phan-su-dung-ccdc')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
