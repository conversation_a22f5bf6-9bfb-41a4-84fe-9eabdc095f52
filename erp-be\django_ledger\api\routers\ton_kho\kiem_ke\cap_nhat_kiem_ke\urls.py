"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for CapNhatKiemKe (Inventory Update) module.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from django_ledger.api.views.ton_kho.kiem_ke.cap_nhat_kiem_ke import CapNhatKiemKeViewSet

# Main router for CapNhatKiemKe
router = DefaultRouter()
router.register('', CapNhatKiemKeViewSet, basename='cap-nhat-kiem-ke')

# URL patterns for the module
urlpatterns = [
    # Include the main router
    path('', include(router.urls)),
]
